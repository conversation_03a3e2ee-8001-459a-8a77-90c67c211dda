import React, { useMemo } from 'react';
import { ScrollView, StyleSheet } from 'react-native';
import {
  Alert,
  BlockView,
  checkSupportCity,
  CleaningTools,
  Colors,
  ColorsV2,
  ConditionView,
  CText,
  Duration,
  FontSizes,
  IService,
  ISO_CODE,
  Markdown,
  NavigationService,
  NotSupportCity,
  PostTaskHelpers,
  PremiumOptional,
  PriceButton,
  ProcessButton,
  SERVICES,
  SizedBox,
  Spacing,
  useAppStore,
  useSettingsStore,
} from '@btaskee/design-system';
import { usePostTaskStore } from '@store';
import { includes, isEmpty } from 'lodash-es';

import {
  AddOnServices,
  Optional,
  TaskerFavoriteForRebook,
  WorkingProcess,
} from '@components';
import { useAppNavigation, useChangeData, useI18n, usePostTask } from '@hooks';
import { RouteName } from '@navigation/RouteName';

import styles from './styles';

let timeoutId: any = null;

export const ChooseDuration = () => {
  const { t } = useI18n();
  const navigation = useAppNavigation();
  const { resetState } = usePostTaskStore();
  const { isoCode } = useAppStore();

  const {
    address,
    duration,
    setDuration,
    setRequirements,
    requirements,
    isPremium,
    setIsPremium,
    isAutoChooseTasker,
    setIsAutoChooseTasker,
    setIsFavouriteTasker,
    pet,
    addons,
    setAddons,
    service,
    forceTasker,
    dataQuickPostTask,
    price,
    setDateTime,
  } = usePostTaskStore();
  const {
    onChangeAddOnService,
    onChangePremiumService,
    checkPremiumOption,
    onChangeDuration,
    setPetOption,
  } = useChangeData();

  const settings = useSettingsStore().settings;
  const { getPrice } = usePostTask();

  const contentNoteDuration = useMemo(() => {
    if (isoCode === ISO_CODE.TH) {
      return (
        <Markdown
          text={t('SV_HC_SCR2_DETAIL_DURATION_TITLE_NOTE_TH')}
          textStyle={styles.txtDescription}
          paragraphStyle={styles.txtParagraph}
        />
      );
    }
    return (
      <Markdown
        text={t('SV_HC_SCR2_DETAIL_DURATION_TITLE_NOTE')}
        textStyle={styles.txtDescription}
        paragraphStyle={styles.txtParagraph}
      />
    );
  }, [t, isoCode]);

  const isSupportPremium = useMemo(() => {
    // cities in this arr, will show option
    const applyForCities = service?.premiumOptions?.applyForCities || [];
    // not support city, no render
    return includes(applyForCities, address?.city);
  }, [address?.city, service]);

  React.useEffect(() => {
    // Did mount here
    setDateTime(
      PostTaskHelpers.getDefaultDateTime(
        {
          serviceName: SERVICES.CLEANING,
          defaultTaskTime: service?.defaultTaskTime,
        },
        service?.defaultTaskTime,
        address?.city,
      ),
    );
    // auto off FavTasker for ThaiLand
    timeoutId = setTimeout(async () => {
      if (isoCode === ISO_CODE.TH) {
        setIsFavouriteTasker(false);
      }
      if (dataQuickPostTask?.requirements) {
        setRequirements?.(dataQuickPostTask?.requirements);
      }
      // auto set duration to 3h
      if (!duration || dataQuickPostTask?.duration) {
        setDuration?.(dataQuickPostTask?.duration || 3);
      }
      if (dataQuickPostTask?.isPremium) {
        setIsPremium?.(dataQuickPostTask?.isPremium);
      }
      // Nếu là công việc đăng với Tasker yêu thích mà có option tự chọn người làm thì tự động tắt đi
      if (!isEmpty(forceTasker) && !isAutoChooseTasker) {
        setIsAutoChooseTasker?.(true);
      }
      // get price
      getPrice();
    }, 200);

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }
    };
  }, []);

  React.useEffect(() => {
    // check support city
    checkPremiumOption({
      citiesApplyPremium: service?.premiumOptions?.applyForCities,
      address,
    });
  }, [address?.city, service?.premiumOptions?.applyForCities]);

  const goToDeepCleaning = async () => {
    const services = settings?.services || [];
    const selectedService = services?.find(
      (e) => e?.name === SERVICES.DEEP_CLEANING,
    );
    if (!selectedService) {
      return null;
    }
    resetState();
    NavigationService.popToTop?.();
    NavigationService.navigate('DeepCleaningService');
  };

  const _onConfirmed = () => {
    // if (checkSupportCityAndAlert(address?.city)) {
    navigation?.navigate?.(RouteName.ChooseDateTime);
    // }
  };

  const shouldRenderNavigateDeepCleaning = useMemo(() => {
    const services = settings?.services || [];
    if (isoCode === ISO_CODE.ID) {
      return (
        <CText style={{ fontSize: FontSizes.SIZE_12 }}>
          {t('SV_HC_SCR2_DETAIL_NOTE_INDO')}
        </CText>
      );
    }
    const selectedService = services?.find(
      (e) => e?.name === SERVICES.DEEP_CLEANING,
    );
    if (!selectedService) {
      return null;
    }

    return (
      <CText style={{ fontSize: FontSizes.SIZE_12 }}>
        {t('SV_HC_SCR2_DETAIL_NOTE')}
        <CText
          bold
          size={FontSizes.SIZE_12}
          color={ColorsV2.green500}
          onPress={goToDeepCleaning}
        >
          {t('NOTE_POST_TASK_CLEANING_1')}
        </CText>
        <CText style={{ fontSize: FontSizes.SIZE_12 }}>
          {t('NOTE_POST_TASK_CLEANING_2')}
        </CText>
      </CText>
    );
  }, [settings?.services]);

  const handleOpenModalWorkingProcess = () => {
    Alert.alert?.open?.({
      title: t('TASK_DETAIL'),
      message: <WorkingProcess />,
      actions: [
        {
          text: t('CLOSE'),
          style: 'cancel',
        },
      ],
      containerStyle: {
        backgroundColor: Colors.BG_COLOR2,
      },
    });
  };

  if (!checkSupportCity(service?.city, address?.city)) {
    return <NotSupportCity />;
  }

  return (
    <BlockView
      inset={['bottom']}
      style={styles.container}
    >
      <ScrollView
        testID="scrollStep2Cleaning"
        contentContainerStyle={styles.containerScroll}
        showsVerticalScrollIndicator={false}
      >
        <ConditionView
          condition={!isEmpty(forceTasker)}
          viewTrue={<TaskerFavoriteForRebook forceTasker={forceTasker} />}
        />
        <BlockView>
          <CText
            bold
            style={styles.txtPanel}
          >
            {t('SV_HC_SCR2_DETAIL_DURATION_TITLE')}
          </CText>
          {contentNoteDuration}
        </BlockView>
        <Duration
          duration={duration}
          onChange={onChangeDuration}
        />
        {shouldRenderNavigateDeepCleaning}

        {/* Khi forceTasker không có premium thì sẽ không hiển thị option premium */}
        <ConditionView
          condition={!isEmpty(forceTasker) && !forceTasker?.isPremiumTasker}
          viewFalse={
            <BlockView>
              <PremiumOptional
                service={service as IService}
                address={address}
                isPremium={isPremium}
                onChangePremium={onChangePremiumService}
                txtPropStyle={StyleSheet.flatten([
                  styles.txtPanel,
                  { fontSize: FontSizes.SIZE_20 },
                ])}
              />
              <CleaningTools
                isPremiumSelected={isPremium}
                isSupportPremium={isSupportPremium}
              />
            </BlockView>
          }
          viewTrue={
            <CleaningTools
              containerStyle={
                forceTasker?.isPremiumTasker ? {} : styles.wrapCleaningTool
              }
              isPremiumSelected={isPremium}
              isSupportPremium={forceTasker?.isPremiumTasker}
            />
          }
        />
        <AddOnServices
          data={requirements}
          onChange={onChangeAddOnService}
          isPremium={isPremium}
        />
        <Optional
          onChangePet={setPetOption}
          optional={service?.optional}
          pet={pet}
          addons={service?.addons}
          setAddons={setAddons}
          addonsSelected={addons}
        />
        <SizedBox height={Spacing.SPACE_16} />
        <ConditionView
          condition={!isEmpty(service?.workingProcessV2?.detail)}
          viewTrue={<ProcessButton onPress={handleOpenModalWorkingProcess} />}
        />
      </ScrollView>
      <PriceButton
        testID="btnNextStep2"
        onPress={_onConfirmed}
        pricePostTask={price}
      />
    </BlockView>
  );
};
