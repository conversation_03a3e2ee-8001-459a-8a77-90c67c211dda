import {
  DateTimeHel<PERSON>,
  IAddress,
  IDate,
  PostTaskHelpers,
} from '@btaskee/design-system';
import { includes, isEmpty } from 'lodash-es';

import { usePostTaskStore } from '@stores';

import { usePostTask } from './usePostTask';

export const useChangeData = () => {
  const {
    setDuration,
    isPremium,
    setIsPremium,
    setPet,
    setMonth,
    setSchedule,
    setStartDate,
    setWeekdays,
    setIsEco,
    setEndDate,
    setPrice,
    setNote,
    setHomeNumber,
    setAddress,
  } = usePostTaskStore();
  const { getPrice } = usePostTask();

  const onChangePremiumService = async (value: boolean) => {
    setIsPremium(value);
    // get price
    await getPrice?.();
  };

  const onChangeDuration = async (newDuration: number) => {
    setDuration(newDuration);
    // get price
    await getPrice?.();
  };

  const checkPremiumOption = async ({
    citiesApplyPremium = [],
    address,
  }: {
    citiesApplyPremium: string[];
    address: IAddress;
  }) => {
    // check premium
    if (!includes(citiesApplyPremium, address?.city) && isPremium) {
      // set isPremium
      setIsPremium(false);
    }
    // get price
    await getPrice?.();
  };

  const setPetOption = async (data: any) => {
    setPet(data);
    // get price
    await getPrice?.();
  };

  /**
   * Handles the change of the eco subscription.
   *
   * @param {boolean} value - The new value of the eco subscription.
   * @return {Promise<void>} A promise that resolves when the eco subscription is set and get price.
   */
  const onChangeEco = (value: boolean) => {
    // set eco subscription
    setIsEco(value);
    // get price
    getPrice?.();
  };

  const onChangeDateTime = async (newDate: IDate) => {
    // set start date
    setStartDate?.(newDate);
    // Reset schedule
    await handleSchedule?.();
    // get price again
    await getPrice?.();
  };

  const onChangeMonth = async (e: number) => {
    setMonth?.(e);
    // reset schedule
    await handleSchedule?.();
    // get price
    await getPrice?.();
  };

  const handleSchedule = async () => {
    const { startDate, month, weekdays, address } = usePostTaskStore.getState();
    if (!startDate || !month || isEmpty(weekdays)) {
      return null;
    }
    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);
    const endDate = DateTimeHelpers.formatToString({
      timezone,
      date: DateTimeHelpers.toDateTz({ timezone, date: startDate })
        .add(month, 'month')
        .endOf('day'),
    });
    const schedules = PostTaskHelpers.getDateFromWeekday({
      timezone,
      weekdays: weekdays,
      startDate: startDate,
      endDate,
    });
    setEndDate(endDate);
    setSchedule(schedules);
  };

  const onChangeRepeatWeekly = async (days: number[]) => {
    // User no choose date, set (price, weekday, schedule) is null
    if (isEmpty(days)) {
      return resetWeekdays?.();
    }

    // set weekdays
    setWeekdays?.(days);
    // reset schedule
    await handleSchedule?.();
    // get price
    await getPrice?.();
  };

  // User no choose date, set price is null
  const resetWeekdays = () => {
    setWeekdays([]);
    setSchedule([]);
    setPrice(null);
  };

  const setDataPTSubscription = async (data: any) => {
    const address = PostTaskHelpers.getAddressSub(data);
    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

    if (data.taskNote) {
      setNote(data.taskNote);
    }

    // Set address data
    setMonth(data?.month || 1);
    setDuration(data?.duration);
    setHomeNumber(data?.description);
    setWeekdays(PostTaskHelpers.getDayNumberFromDate(timezone, data.schedule));
    setSchedule();
    setAddress({
      ...address,
      homeType: data?.homeType,
    });
    await getPrice();
  };

  return {
    onChangePremiumService,
    checkPremiumOption,
    onChangeDuration,
    setPetOption,
    onChangeDateTime,
    onChangeMonth,
    onChangeRepeatWeekly,
    onChangeEco,
    handleSchedule,
    setDataPTSubscription,
  };
};
