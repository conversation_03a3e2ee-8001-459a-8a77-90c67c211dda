import {
  Al<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  EndpointKeys,
  getPhoneNumber,
  handleError,
  IApiError,
  IPriceSub,
  PAYMENT_METHOD,
  useApiMutation,
  useAppLoadingStore,
  useAppStore,
  useI18n,
  useUserStore,
} from '@btaskee/design-system';
import { IDataBooking, IParamsGetPrice } from '@types';
import { get, isEmpty } from 'lodash-es';

import { usePostTaskStore } from '@stores';

export const usePostTask = () => {
  const { t } = useI18n('common');
  const { isoCode } = useAppStore();
  const { user } = useUserStore();
  const { showLoading, hideLoading } = useAppLoadingStore();

  const { service, setPrice, setLoadingPrice, setLoadingPostTask } =
    usePostTaskStore();
  const { mutate: getPriceCleaningSubscription } =
    useApiMutation<EndpointKeys.getPriceCleaningSubscription>({
      key: EndpointKeys.getPriceCleaningSubscription,
    });
  const { mutate: postTaskAirConditioner } = useApiMutation({
    key: EndpointKeys.postTaskAirConditioner,
  });
  const { mutate: checkTaskSameTime, data: checkTaskSameTimeData } =
    useApiMutation({
      key: EndpointKeys.checkTaskSameTime,
    });

  const getDataPricing = async () => {
    // Get the latest state directly from the store
    const currentState = usePostTaskStore.getState();
    const {
      address,
      isPremium,
      duration,
      addons,
      schedule,
      month,
      promotion,
      isEco,
      paymentMethod,
    } = currentState;

    if (isEmpty(schedule) || !duration || !month) {
      return null;
    }
    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

    const schedules = schedule.map((e) =>
      DateTimeHelpers.formatToString({ timezone, date: e }),
    );
    const params: IParamsGetPrice = {
      schedule: schedules,
      timezone,
      service: {
        _id: service?._id || '',
      },
      task: {
        taskPlace: {
          country: address?.country,
          city: address?.city,
          district: address?.district,
        },
        duration: duration,
        autoChooseTasker: true,
        homeType: address?.homeType,
      },
      month,
      isoCode: isoCode || '',
    };
    // promotion
    if (promotion?.code) {
      params.task.promotion = { code: promotion.code };
    }

    if (isPremium) {
      params.task.isPremium = Boolean(isPremium);
    }

    if (isEco) {
      params.task.isEco = Boolean(isEco);
    }

    if (paymentMethod) {
      params.task.payment = {
        method: paymentMethod?.value,
      };
    }

    if (!isEmpty(addons)) {
      params.task.addons = addons;
    }

    return params;
  };

  const getPrice = async () => {
    await showLoading();
    // refactor data after call get price
    const data = await getDataPricing();

    // data is null, no get price
    if (!data) {
      await hideLoading();
      // set price is nul --> hide price button.
      return setPrice(null);
    }
    // show loading price
    setLoadingPrice(true);

    // call get price API
    getPriceCleaningSubscription(data, {
      onSuccess: (result) => {
        setPrice(result as IPriceSub);
      },
      onError: (error: IApiError) => {
        handleError(error);
        setPrice(null);
      },
    });
    hideLoading();
    setLoadingPrice(false);
  };

  const _refactorDataPostTask = () => {
    // Get the latest state directly from the store
    const currentState = usePostTaskStore.getState();

    const {
      address,
      date,
      duration,
      addons,
      paymentMethod,
      isApplyNoteForAllTask,
      note,
      promotion,
    } = currentState;

    const isAddressMaybeWrong = Boolean(address?.isAddressMaybeWrong);
    const isTet = service?.isTet || false;
    const city = address?.city;
    const timezone = DateTimeHelpers.getTimezoneByCity(city);

    // base task info
    const task: IDataBooking = {
      address: address?.address,
      contactName: address?.contact || user?.name,
      lat: address?.lat,
      lng: address?.lng,
      phone: address?.phoneNumber || user?.phone,
      countryCode: address?.countryCode || user?.countryCode,
      description: address?.description,
      askerId: user?._id,
      autoChooseTasker: true,
      date: DateTimeHelpers.formatToString({ date: date, timezone }),
      timezone,
      deviceInfo: DeviceHelper.getDeviceInfo(),
      duration: duration,
      homeType: address?.homeType,
      houseNumber: address?.description,
      isoCode: user?.isoCode,
      payment: {
        method: paymentMethod?.value,
      },
      serviceId: service?._id,
      taskPlace: {
        country: address?.country,
        city,
        district: address?.district,
      },
      updateTaskNoteToUser: isApplyNoteForAllTask,
      shortAddress: address?.shortAddress,
      addons: [],
    };

    // Refactor phone number - refill 0 at first
    task.phone = getPhoneNumber(task?.phone, task?.countryCode);

    // Địa chỉ có thể bị sai => khi book task, send to slack cho team vận hành xử lý
    if (isAddressMaybeWrong) {
      task.taskPlace.isAddressMaybeWrong = isAddressMaybeWrong;
    }

    if (isTet) {
      task.isTetBooking = true;
    }

    if (note && note?.trim()) {
      task.taskNote = note?.trim();
    }

    // promotion
    if (promotion) {
      task.promotion = { code: promotion?.code };
    }

    // payment with card
    if (task.payment?.method === PAYMENT_METHOD.card) {
      task.payment.cardId = paymentMethod?.cardInfo?._id;
      // task.payment.cardInfo = {
      //   number: paymentMethod?.cardInfo?.number,
      //   type: paymentMethod?.cardInfo?.type,
      //   expiryMonth: paymentMethod?.cardInfo?.expiryMonth,
      //   expiryYear: paymentMethod?.cardInfo?.expiryYear,
      // }
    }

    // payment with virtualAccount
    if (task.payment?.method === PAYMENT_METHOD.virtualAccount) {
      task.payment.bank = paymentMethod?.bank;
    }

    if (!isEmpty(addons)) {
      task.addons = addons;
    }

    return {
      task,
      service: {
        _id: service?._id,
        name: service?.name,
      },
    };
  };

  const _postTaskProvider = async (dataTask: IDataBooking) => {
    if (isEmpty(dataTask?.dateOptions)) {
      delete dataTask.dateOptions;
    }
    postTaskAirConditioner(dataTask, {
      onSuccess: (result) => {},
      onError: (error) => {
        handleError(error);
      },
    });
  };

  const _addTask = async () => {
    const dataTask = _refactorDataPostTask();
    // call post task
    const result = await _postTaskProvider(dataTask);
    // hide loading
    setLoadingPostTask(false);
    // process respond
    if (result?.isSuccess) {
      // Tracking
      // const serviceText = getServiceTextToTracking(dataTask?.service?.name);
      // const dataUser = {
      //   lifetimeCount: get(app, 'user.taskDone', null),
      //   statusUser: getStatusUser(get(app, 'user.taskDone', null)),
      // };
      // const dataTaskTracking = cloneDeep(dataTask?.task);
      // dataTaskTracking.finalCost = price?.finalCost;

      // Tracking CleverTap Post task success
      // await dispatch(setIsBookedTask(true));
      // trackingCleverTapTaskPostSuccess(dataUser, dataTaskTracking, serviceText, app?.configSpecialPreBooking?.name);
      //firebase Analytics
      // TrackFirebaseAnalytics.taskPostSuccess(service?._id, app?.user?.taskDone);
      // End tracking

      // success
      // return dispatch(_setData(result.data));
      return result.data;
    }
    // error
    handleError(result?.error);
    // dispatch(_setData({ error: result?.error }));
    // const alertObj = {
    //   title: 'DIALOG_TITLE_INFORMATION',
    //   message: 'ERROR_TRY_AGAIN',
    //   actions: [{ text: 'CLOSE' }],
    // };
    // switch (result?.error?.code) {
    //   case 'NOT_ENOUGH_MONEY':
    //     alertObj.message = {
    //       text: 'BPAY_LACK_OF_MONEY',
    //       params: {
    //         cost: formatMoney(result?.error?.data?.amount),
    //         currency: getCurrency(result?.error?.data, 1),
    //       },
    //     };
    //     alertObj.actions = [
    //       { text: 'CLOSE', style: 'cancel' },
    //       { text: 'ADD_MONEY', onPress: () => navigation?.navigate(RouteName.UserBpay) },
    //     ];
    //     break;
    //   case 'NOT_ENOUGH_BPAY_BUSINESS':
    //     alertObj.title = 'TITLE_NOT_ENOUGH_MONEY';
    //     alertObj.message = {
    //       text: 'MESSAGE_NOT_ENOUGH_BPAY_BUSINESS',
    //       params: {
    //         cost: formatMoney(result?.error?.data?.amount),
    //         currency: getCurrency(result?.error?.data, 1),
    //       },
    //     };
    //     alertObj.actions = [{ text: 'CLOSE', style: 'cancel' }];
    //     break;
    //   case 'DATE_TIME_ERROR':
    //     alertObj.message = 'INCORRECT_DATE_TIME';
    //     break;
    //   case 'OUTSTANDING_PAYMENT_STATUS_NEW':
    //     return { code: 'OUTSTANDING_PAYMENT_STATUS_NEW' };
    //   case 'PAYMENT_CARD_EXPIRED':
    //     alertObj.message = 'PAYMENT_CARD_EXPIRED';
    //     break;
    //   case 'USER_STATUS_DISABLED':
    //     alertObj.message = 'CANCEL_TASK_DISABLED_CONTENT';
    //     break;
    //   case 'BOOKING_DATE_INVALID':
    //     alertObj.message = 'STEP4_ERROR_DATE_TIME';
    //     break;
    //   case 'BPAY_DEBT':
    //     alertObj.message = 'STEP4_BPAY_DEBT';
    //     alertObj.actions = [{ text: 'PAYMENT_TOP_UP', onPress: () => navigation?.navigate(RouteName.UserBpay) }];
    //     break;
    //   case 'SERVICE_PAUSE':
    //     const reason = result?.error?.data?.reason;
    //     if (reason) {
    //       alertObj.message = {
    //         text: reason[app.locale],
    //         notUsedI18n: true,
    //       };
    //     }
    //     break;
    //   case 'BOOKING_IS_DUPLICATE':
    //     alertObj.message = 'STEP4_ERROR_BOOKING_IS_DUPLICATE';
    //     break;
    //   case 'DESCRIPTION_LIMIT':
    //     alertObj.message = 'STEP4_ERROR_BOOKING_DESCRIPTION_LIMIT';
    //     break;
    //   case 'COUNTRY_CODE_INVALID':
    //     alertObj.message = 'COUNTRY_CODE_INVALID';
    //     alertObj.actions = [
    //       {
    //         text: 'OK',
    //         onPress: () => {
    //           navigation.popToTop();
    //           navigation?.navigate(RouteName.UserProfile);
    //         },
    //       },
    //     ];
    //     break;
    //   case 'SERVICE_NOT_STARTED':
    //     alertObj.message = {
    //       text: 'ERROR_SERVICE_NOT_STARTED',
    //       params: {
    //         t: DateTimeHelpers.formatToString({
    //           timezone: DateTimeHelpers.getTimezoneByCity(city),
    //           date: result?.error?.data?.beginDate,
    //           typeFormat: TypeFormatDate.DateShort,
    //           keepLocalTime: true,
    //         }),
    //       },
    //     };
    //     break;
    //   default:
    //     break;
    // }

    // Post to slack when post task error.
    // sendToSlackBookingError({ serviceName: 'cleaning', error: result });

    // show error
    // return AlertHolder.alert.open(alertObj);
  };

  /**
   * @description function booking task
   * @param payload {{object}} data of task
   */
  const postTask = async (callback?: () => void) => {
    const currentState = usePostTaskStore.getState();
    const { date, address } = currentState;
    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

    setLoadingPostTask(true);

    // check task same time
    checkTaskSameTime({
      taskDate: DateTimeHelpers.formatToString({ date, timezone }),
      serviceId: service?._id || '',
    });

    // time ok
    if (checkTaskSameTimeData === false) {
      // call api book task
      const postTaskResult = await _addTask();

      // success
      if (get(postTaskResult, 'bookingId', null)) {
        callback?.();
      }
      return postTaskResult;
    } // end check same time

    // hide loading
    await setLoadingPostTask(false);

    // same time, alert for user
    return AlertHolder?.alert?.open({
      title: t('DIALOG_TITLE_INFORMATION'),
      message: t('TASK_SAME_TIME_MESSAGE'),
      actions: [
        { text: t('CLOSE'), style: 'cancel' },
        {
          text: t('OK'),
          onPress: async () => {
            // show loading
            await setLoadingPostTask(true);
            // wait modal close
            setTimeout(async () => {
              const postTaskResult = await _addTask();
              if (get(postTaskResult, 'payload.bookingId', null)) {
                callback?.();
              }
            }, 300);
          },
        },
      ],
    });
  };

  return { getPrice, postTask };
};
