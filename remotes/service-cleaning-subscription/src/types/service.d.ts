import {
  IDate,
  IDeviceInfo,
  ILocation,
  IService,
  IUser,
  RelatedService,
  Requirement,
} from '@btaskee/design-system';

import { IObjectText } from './index';

export type IDetailAC = {
  type?: {
    name?: string;
    text?: IObjectText;
  };
  hp?: {
    from?: number | null;
    to?: number | null;
  };
  options: {
    name?: string;
    text?: IObjectText;
    quantity?: number;
  }[];
  quantity?: number;
};

export type IParamsRefillGas = { option: IOpitonRefill } & Pick<
  IDataACRender,
  'type' | 'hp'
>;

export type IOpitonRefill = { quantity?: number } & Pick<
  IServiceOfAC,
  'name' | 'text'
>;
export type ISelectedAC = {
  quantity?: number;
  options?: IOpitonRefill[];
  index?: number;
} & IDataACRender;

type IServiceOfAC = {
  name?: string;
  text?: IObjectText;
  prices?: {
    HPTo?: number;
    price?: number;
    HPFrom?: number;
  }[];
  discountByQty?: {
    discount?: number;
    qty?: number;
  };
};

export type ITypeOfCityAC = {
  name?: string;
  text?: IObjectText;
  services?: IServiceOfAC[];
};

export type IDataACRender = {
  type?: {
    name?: string;
    text?: IObjectText;
  };
  hp?: {
    from?: number | null;
    to?: number | null;
  };
} & Pick<ITypeOfCityAC, 'services'>;

export interface IAddons {
  cost: number;
  name: string;
  text: IObjectText;
}

export interface WorkingProcessDetail {
  name: string;
  text: IObjectText;
  workToDo: IObjectText[];
}

export interface WorkingProcessV2 {
  detail: WorkingProcessDetail[];
}

export interface IDataBooking {
  address?: string;
  locations?: ILocation[];
  user?: IUser;
  service?: IService;
  homeNumber?: string;
  homeType?: string;
  finalCost?: {
    amount: number;
  };
  // Additional properties
  contactName?: string;
  lat?: number;
  lng?: number;
  phone?: string;
  countryCode?: string;
  description?: string;
  askerId?: string;
  autoChooseTasker?: boolean;
  date?: string;
  timezone?: string;
  deviceInfo?: IDeviceInfo;
  duration?: number;
  houseNumber?: string;
  isoCode?: string;
  payment?: any;
  serviceId?: string;
  taskPlace: {
    city?: string;
    country?: string;
    district?: string;
    isAddressMaybeWrong?: boolean;
  };
  updateTaskNoteToUser?: boolean;
  shortAddress?: string;
  isTetBooking?: boolean;
  taskNote?: string;
  promotion?: any;
  forceTasker?: {
    taskerId?: string;
    isResent?: boolean;
  };
  dateOptions?: IDate[];
  addons?: IAddons[];
  source?: {
    from?: string;
    taskId?: string;
  };
}
