import { IAddons, IDate, ITaskPlace } from '@btaskee/design-system';
import { IUser } from '@types/user';

export type IParamsGetPrice = {
  schedule: string[];
  timezone: string;
  month: number;
  task: {
    autoChooseTasker: boolean;
    taskPlace: ITaskPlace;
    homeType?: string;
    duration: number;
    forceTasker?: IUser;
    dateOptions?: IDate[];
    payment?: {
      method: string;
    };
    requirements?: { type: number }[];
    addons?: IAddons[];
    isPremium?: boolean;
    isEco?: boolean;
    promotion?: {
      code: string;
    };
  };
  service: {
    _id: string;
  };
  isoCode: string;
};
