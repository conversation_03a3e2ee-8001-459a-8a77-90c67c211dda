import React from 'react';
import {
  BlockView,
  BorderRadius,
  ColorsV2,
  ConditionView,
  CText,
  FastImage,
  FastImageComponentProps,
  FontSizes,
  IconAssets,
  IconImage,
  Spacing,
  TouchableOpacity,
} from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

import { styles } from './styles';

type WorkingTimeProps = {
  testID: string;
  image: FastImageComponentProps['source'];
  onPress: () => void;
  title: string;
  contents: string[];
};

export const WorkingTime = ({
  testID,
  image,
  onPress,
  title,
  contents,
}: WorkingTimeProps) => {
  return (
    <TouchableOpacity
      testID={testID}
      onPress={onPress}
    >
      <BlockView style={styles.containerItem}>
        <BlockView horizontal>
          <FastImage
            source={image}
            style={styles.imageStyle}
          />
        </BlockView>

        <BlockView style={{ marginTop: Spacing.SPACE_16 }}>
          <CText
            size={FontSizes.SIZE_16}
            bold
            color={ColorsV2.orange500}
          >
            {title}
          </CText>
          <ConditionView
            condition={!isEmpty(contents)}
            viewTrue={
              <BlockView
                row
                style={styles.boxContent}
              >
                <BlockView flex>
                  {contents.map((content) => (
                    <CText
                      key={content}
                      style={styles.txtContent}
                    >
                      {content}
                    </CText>
                  ))}
                </BlockView>
                <BlockView
                  center
                  width={40}
                  height={40}
                  radius={BorderRadius.RADIUS_FULL}
                  backgroundColor={ColorsV2.green500}
                >
                  <IconImage source={IconAssets.icNext} />
                </BlockView>
              </BlockView>
            }
          />
        </BlockView>
      </BlockView>
    </TouchableOpacity>
  );
};
