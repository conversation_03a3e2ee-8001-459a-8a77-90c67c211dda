import { StyleSheet } from 'react-native';
import {
  ColorsV2,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Shadows,
  Spacing,
} from '@btaskee/design-system';

const { WIDTH } = DeviceHelper.WINDOW;
const IMAGE_WIDTH = WIDTH / 1.5;
const IMAGE_HEIGHT = WIDTH / 1.5;

export const styles = StyleSheet.create({
  imageStyle: {
    width: IMAGE_WIDTH,
    height: IMAGE_HEIGHT / 1.5,
  },
  boxContent: {
    marginBottom: Spacing.SPACE_16,
    marginTop: Spacing.SPACE_08,
  },
  txtContent: {
    lineHeight: 22,
  },
  containerItem: {
    backgroundColor: ColorsV2.neutralWhite,
    borderRadius: 16,
    paddingHorizontal: Spacing.SPACE_16,
    marginBottom: Spacing.SPACE_16,
    ...Shadows.SHADOW_1,
  },
});
