import React, { useRef, useState } from 'react';
import {
  BlockView,
  CModal,
  CModalHandle,
  ColorsV2,
  CText,
  FastImage,
  HitSlop,
  IconAssets,
  IconImage,
  Switch,
  TouchableOpacity,
  useI18n,
} from '@btaskee/design-system';

import { icEcoSub, imgEcoSubDetail } from '@images';

import styles from './styles';

const EcoDetails = () => {
  const { t } = useI18n('cleaningSub');

  const contents: any = [];
  const ECO_SUBSCRIPTION_CONTENT_DESCRIPTION = [
    t('SUBSCRIPTION.ECO_SUBSCRIPTION_DETAIL_1'),
    t('SUBSCRIPTION.ECO_SUBSCRIPTION_DETAIL_2'),
    t('SUBSCRIPTION.ECO_SUBSCRIPTION_DETAIL_3'),
    t('SUBSCRIPTION.ECO_SUBSCRIPTION_DETAIL_4'),
  ];
  ECO_SUBSCRIPTION_CONTENT_DESCRIPTION.map((text) => {
    contents.push(
      <BlockView
        key={text}
        row
      >
        <BlockView style={styles.blockIcon}>
          <IconImage
            source={IconAssets.icStar}
            size={20}
          />
        </BlockView>
        <BlockView flex>
          <CText style={styles.contentStyle}>{text}</CText>
        </BlockView>
      </BlockView>,
    );
  });
  return contents;
};

export const EcoOptional = ({
  isEco,
  onChangeEco,
}: {
  isEco: boolean;
  onChangeEco: (value: boolean) => void;
}) => {
  const { t } = useI18n('cleaningSub');

  const modalRef = useRef<CModalHandle | null>(null);

  const [isEcoSub, setIsEcoSub] = useState(Boolean(isEco));

  React.useEffect(() => {
    if (isEco !== isEcoSub) {
      setIsEcoSub(isEco);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isEco]);

  /**
   * A function to handle closing the modal.
   */
  const _handleClose = () => {
    modalRef?.current?.close && modalRef?.current?.close();
  };

  /**
   * A function to handle opening the modal.
   */
  const _handleOpen = () => {
    modalRef?.current?.open && modalRef?.current?.open();
  };

  /**
   * Handles the change of the switch for Eco subscription.
   *
   * @param {boolean} checked - Indicates if the switch is checked or not.
   */
  const _onChangeSwitch = (checked: boolean) => {
    setIsEcoSub(checked);
    onChangeEco(checked);

    if (checked) {
      _handleOpen();
    }
  };

  // Render content in description premium
  return (
    <>
      <BlockView style={styles.container}>
        <CText
          bold
          style={styles.txtTitle}
        >
          {t('CURTAIN_CHOOSE_SERVICE')}
        </CText>
        <BlockView
          row
          style={styles.group}
        >
          <BlockView
            row
            style={styles.left}
          >
            <FastImage
              source={icEcoSub}
              style={styles.iconImage}
            />
            <BlockView flex>
              <BlockView row>
                <CText
                  bold
                  style={styles.txtLabel}
                >
                  {t('SUBSCRIPTION.ECO_SUBSCRIPTION')}
                </CText>
                <TouchableOpacity
                  testID="chooseEcoSubDescription"
                  hitSlop={HitSlop.MEDIUM}
                  onPress={_handleOpen}
                  style={styles.btnInfo}
                >
                  <IconImage
                    source={IconAssets.icQuestion}
                    size={16}
                    color={ColorsV2.green500}
                  />
                </TouchableOpacity>
              </BlockView>
              <CText style={styles.txtContent}>
                {t('SUBSCRIPTION.ECO_SUBSCRIPTION_CONTENT_1')}
              </CText>
            </BlockView>
          </BlockView>
          <Switch
            testID="chooseEcoSub"
            value={isEcoSub}
            onValueChange={_onChangeSwitch}
          />
        </BlockView>
      </BlockView>

      {/* Modal description */}
      <CModal
        ref={modalRef}
        hideButtonClose
        isShowHeader={false}
        containerModal={styles.containerModal}
      >
        <BlockView style={styles.blockImage}>
          <FastImage
            resizeMode={'cover'}
            source={imgEcoSubDetail}
            style={styles.ImageDescription}
          />
          <TouchableOpacity
            testID={'btnCloseModalEco'}
            onPress={_handleClose}
            style={styles.blockCancel}
          >
            <BlockView>
              <IconImage source={IconAssets.icCloseModal} />
            </BlockView>
          </TouchableOpacity>
        </BlockView>

        <BlockView>
          <CText
            h4
            bold
            style={styles.txtPanel}
          >
            {t('SUBSCRIPTION.ECO_SUBSCRIPTION_DETAIL')}
          </CText>
          <EcoDetails />
        </BlockView>
      </CModal>
    </>
  );
};
