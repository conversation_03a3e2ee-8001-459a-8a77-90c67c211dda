import { Dimensions, StyleSheet } from 'react-native';
import {
  BorderRadius,
  ColorsV2,
  FontSizes,
  Spacing,
} from '@btaskee/design-system';

const { width } = Dimensions.get('window');

export default StyleSheet.create({
  btnInfo: {
    paddingLeft: 5,
  },

  left: {
    flex: 1,
    alignItems: 'center',
  },
  group: {
    alignItems: 'center',
    backgroundColor: ColorsV2.green50,
    borderColor: ColorsV2.green500,
    borderWidth: 1,
    borderRadius: BorderRadius.RADIUS_08,
    padding: Spacing.SPACE_16,
    marginTop: Spacing.SPACE_16,
  },
  container: {
    marginTop: '2%',
  },
  txtPanel: {
    marginTop: Spacing.SPACE_16,
    marginBottom: Spacing.SPACE_24,
    fontSize: FontSizes.SIZE_16,
    // marginLeft: 10
  },
  iconImage: {
    width: 40,
    height: 40,
  },
  txtLabel: {
    marginLeft: Spacing.SPACE_16,
  },
  txtContent: {
    marginHorizontal: Spacing.SPACE_16,
    marginTop: Spacing.SPACE_04,
  },
  blockImage: {
    marginHorizontal: -Spacing.SPACE_16,
    marginBottom: Spacing.SPACE_16,
  },
  blockIcon: {
    marginRight: Spacing.SPACE_08,
  },
  ImageDescription: {
    width: width,
    height: width / 2,
  },
  iconStar: {
    width: 20,
    height: 20,
  },
  contentStyle: {
    marginBottom: Spacing.SPACE_16,
  },
  containerModal: {
    margin: 0,
    paddingTop: 0,
    paddingBottom: Spacing.SPACE_16,
  },
  blockCancel: {
    position: 'absolute',
    top: 15,
    right: 15,
  },
  iconCancel: {
    width: 24,
    height: 24,
  },
  txtTitle: {
    fontSize: FontSizes.SIZE_16,
  },
});
