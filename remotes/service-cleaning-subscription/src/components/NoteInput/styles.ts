import { Dimensions, StyleSheet } from 'react-native';
import {
  ColorsV2,
  FontFamily,
  FontSizes,
  Shadows,
  Spacing,
} from '@btaskee/design-system';

const { height } = Dimensions.get('window');
const HEIGHT_INPUT = Math.round(height / 3);

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorsV2.neutralBackground,
    padding: Spacing.SPACE_16,
  },
  wrap_container: {
    paddingHorizontal: Spacing.SPACE_16,
  },
  inputStyle: {
    height: HEIGHT_INPUT,
    marginVertical: Spacing.SPACE_12,
  },
  txtDescription: {
    marginTop: Spacing.SPACE_16,
  },
  titleStyle: {
    fontSize: FontSizes.SIZE_16,
    fontWeight: 'bold',
    fontFamily: FontFamily.FONT_BOLD,
  },
  wrap_btnSubmit: {
    ...Shadows.SHADOW_1,
  },
  contentContainer: {
    marginTop: Spacing.SPACE_12,
    backgroundColor: ColorsV2.neutralWhite,
    flex: 1,
    justifyContent: 'space-between',
  },
  labelStyle: {
    height: 0,
    paddingBottom: 0,
  },
  inputContainer: {
    marginTop: 24,
    paddingTop: 0,
    paddingHorizontal: 0,
  },
});
