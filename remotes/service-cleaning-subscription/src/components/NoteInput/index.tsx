import React, { memo, useState } from 'react';
import { CTextInput, useI18n } from '@btaskee/design-system';

import { styles } from './styles';

interface NoteInputProps {
  value: string;
  onChangeText: (text: string) => void;
  error?: string;
}

export const NoteInput = memo(
  ({ value, onChangeText, error }: NoteInputProps) => {
    const { t } = useI18n('cleaningSub');
    const [note, setNote] = useState(value);

    // const handleChangeText = (text: string) => {
    //   onChangeText(text);
    // };

    return (
      <CTextInput
        inputStyle={styles.inputStyle}
        placeholder={t('POST_TASK_NOTE')}
        containerStyle={styles.inputContainer}
        multiline
        labelStyle={styles.labelStyle}
        maxLength={400}
        onChangeText={setNote}
        value={note}
        error={error}
      />
    );
  },
);
