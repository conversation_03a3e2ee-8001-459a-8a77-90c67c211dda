import { NativeStackNavigationProp } from '@react-navigation/native-stack';

import { RouteName } from './RouteName';

export type MainStackParamList = {
  [RouteName.ChooseAddress]: any;
  [RouteName.ChooseService]?: {
    renewOldSubscription?: any;
  };
  [RouteName.ChooseDateTime]: any;
  [RouteName.ConfirmAndPayment]: any;
  [RouteName.PostTaskSuccess]: any;
  [RouteName.ChooseDuration]?: {
    isShowEcoOption?: boolean;
    renewOldSubscription?: any;
  };
  [RouteName.Notes]: any;
};

export type ParamsNavigationList = MainStackParamList;

export type NavigationProps<T extends keyof MainStackParamList> =
  NativeStackNavigationProp<MainStackParamList, T>;
