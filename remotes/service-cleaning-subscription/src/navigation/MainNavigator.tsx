import React, { useEffect } from 'react';
import {
  BlockView,
  Colors,
  CText,
  FontFamily,
  IconAssets,
  IconImage,
  IService,
  SERVICES,
  Spacing,
  TouchableOpacity,
  useI18n,
  useSettingsStore,
} from '@btaskee/design-system';
import {
  createNativeStackNavigator,
  type NativeStackNavigationOptions,
} from '@react-navigation/native-stack';

import { ChooseAddress, ChooseService, Notes } from '@screens';
import { usePostTaskStore } from '@stores';

import { RouteName } from './RouteName';
import { MainStackParamList } from './type';

const Stack = createNativeStackNavigator<MainStackParamList>();

const MainNavigator = () => {
  const { t } = useI18n('common');

  const { address, setService } = usePostTaskStore();
  const { settings } = useSettingsStore();

  useEffect(() => {
    initData();
  }, []);

  // TODO: init data for cleaning service
  const initData = async () => {
    const cleaningSubscriptionService = settings?.services?.find(
      (service: IService) => service?.name === SERVICES.CLEANING_SUBSCRIPTION,
    );
    setService(cleaningSubscriptionService);
  };

  const renderTitle = () => {
    return (
      <BlockView
        flex
        row
        horizontal
      >
        <IconImage
          source={IconAssets.icLocation}
          size={24}
          color={Colors.RED}
        />
        <BlockView margin={{ left: Spacing.SPACE_08 }}>
          <CText>{address?.shortAddress}</CText>
          <CText
            bold
            numberOfLines={1}
            margin={{ right: Spacing.SPACE_16 }}
          >
            {address?.address}
          </CText>
        </BlockView>
      </BlockView>
    );
  };

  const renderHeaderLeft = (navigation: any) => {
    return (
      <TouchableOpacity
        onPress={() => navigation?.goBack()}
        activeOpacity={0.7}
      >
        <IconImage
          source={IconAssets.icBack}
          size={24}
          color={Colors.BLACK}
        />
      </TouchableOpacity>
    );
  };

  return (
    <Stack.Navigator
      screenOptions={({ navigation }): NativeStackNavigationOptions => ({
        headerShown: true,
        headerLeft: () => renderHeaderLeft(navigation),
        animation: 'slide_from_right',
        animationDuration: 200,
        contentStyle: { backgroundColor: Colors.WHITE },
        headerStyle: {
          backgroundColor: Colors.WHITE,
        },
        headerTitleStyle: {
          color: Colors.BLACK,
          fontSize: 18,
          fontFamily: FontFamily.FONT_BOLD,
        },
      })}
      initialRouteName={RouteName.ChooseAddress}
    >
      <Stack.Screen
        name={RouteName.ChooseAddress}
        component={ChooseAddress}
        options={{ title: t('LIST_OF_LOCATIONS') }}
      />
      <Stack.Screen
        name={RouteName.ChooseService}
        component={ChooseService}
        options={{ headerTitle: renderTitle }}
      />
      <Stack.Screen
        name={RouteName.Notes}
        component={Notes}
        options={{ title: t('POST_TASK_NOTE') }}
      />
      {/* <Stack.Screen
          name="CleaningChooseDateTime"
          component={ChooseDateTime}
          options={{ title: t('WORK_TIME_TITLE') }}
        />
        <Stack.Screen
          name="CleaningConfirmBooking"
          component={ConfirmBooking}
          options={{ title: t('PT2_CONFIRM_HEADER_TITLE') }}
        />
        <Stack.Screen
          name="CleaningPostTaskSuccess"
          component={PostTaskSuccess}
          options={{ headerShown: false }}
        /> */}
    </Stack.Navigator>
  );
};

export default MainNavigator;
