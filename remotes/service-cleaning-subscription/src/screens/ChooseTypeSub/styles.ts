import { StyleSheet } from 'react-native';
import { ColorsV2, <PERSON><PERSON>Helper, Spacing } from '@btaskee/design-system';

const { HEIGHT, WIDTH } = DeviceHelper.WINDOW;

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorsV2.neutralBackground,
  },
  content: {
    marginTop: WIDTH / 3.5,
    paddingHorizontal: Spacing.SPACE_16,
  },
  iconStyle: {
    width: 35,
    height: 35,
    marginLeft: Spacing.SPACE_08,
  },
  contentContainerStyle: {
    minHeight: HEIGHT,
  },
  imageHeader: {
    width: WIDTH,
    height: WIDTH / 2,
    position: 'absolute',
  },
});
