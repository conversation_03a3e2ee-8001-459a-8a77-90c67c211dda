import React from 'react';
import {
  IStatusEcoOption,
  SERVICES,
  useUserStore,
} from '@btaskee/design-system';
import { NativeStackScreenProps } from '@react-navigation/native-stack';

import { RouteName } from '@navigation/RouteName';
import { ParamsNavigationList } from '@navigation/type';
import { usePostTaskStore } from '@stores';

import { ChooseDuration } from '../ChooseDuration';
import { ChooseTypeSub } from '../ChooseTypeSub';

type ChooseServiceRoute = NativeStackScreenProps<
  ParamsNavigationList,
  RouteName.ChooseService
>;

export const ChooseService: React.FC<ChooseServiceRoute> = ({ route }) => {
  const { user } = useUserStore();
  const { service } = usePostTaskStore();
  const ecoSubscriptionCleaning =
    service?.name === SERVICES.CLEANING_SUBSCRIPTION &&
    service?.ecoOptions?.status === IStatusEcoOption.ACTIVE;

  if (user?.isEco && ecoSubscriptionCleaning) {
    return (
      <ChooseTypeSub
        renewOldSubscription={route?.params?.renewOldSubscription}
      />
    );
  }
  return <ChooseDuration route={route} />;
};
