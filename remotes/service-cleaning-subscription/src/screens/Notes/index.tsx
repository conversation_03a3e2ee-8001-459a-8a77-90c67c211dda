import React, { memo, useCallback, useState } from 'react';
import {
  BlockView,
  CText,
  PrimaryButton,
  useI18n,
} from '@btaskee/design-system';

import { NoteInput } from '@components';
import { useAppNavigation } from '@hooks';
import { RouteName } from '@navigation/RouteName';
import { usePostTaskStore } from '@stores';

import { styles } from './styles';

export const Notes = () => {
  const { t } = useI18n('cleaningSub');
  const { t: tCommon } = useI18n('common');

  const navigation = useAppNavigation();
  const [note, setNote] = useState('');
  const [error, setError] = useState('');

  const onSubmit = useCallback(async () => {
    // Check limit
    if (!note || note?.trim()?.length < 10) {
      setError(t('NOTE_SUBSCRIPTION_EMPTY'));
      return;
    }

    // Submit for subscription
    navigation.navigate(RouteName.ConfirmAndPayment);
  }, [note, navigation, t]);

  const onChangeText = useCallback((value: string) => {
    setNote(value);
    setError(''); // Clear error when user types
  }, []);

  return (
    <BlockView
      inset={'bottom'}
      style={styles.container}
    >
      <BlockView flex>
        <NoteInput
          value={note}
          onChangeText={onChangeText}
          error={error}
        />
        <CText
          testID="taskNoteDescription"
          style={styles.txtDescription}
        >
          {t('TASK_NOTE_DESCRIPTION')}
        </CText>
      </BlockView>

      <PrimaryButton
        onPress={onSubmit}
        title={t('CONTINUE')}
        disabled={!note || note?.trim()?.length < 10}
      />
    </BlockView>
  );
};
