/**
 * Styles for the ChooseAddress screen
 */
import { StyleSheet } from 'react-native';
import { ColorsV2, Spacing } from '@btaskee/design-system';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorsV2.neutralBackground,
  },
  wrapFlatList: {
    backgroundColor: ColorsV2.neutralWhite,
    padding: Spacing.SPACE_16,
    flex: 1,
  },
  txtDescription: {
    color: ColorsV2.neutral800,
    marginBottom: Spacing.SPACE_08,
  },
  contentContainer: {
    paddingBottom: 50,
  },
});
