import React, { useMemo } from 'react';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';
import {
  AddonsName,
  Alert,
  BlockView,
  ChangeScheduleSub,
  ChooseMonth,
  CleaningTools,
  ColorsV2,
  ConditionView,
  CText,
  DateTimeHelpers,
  Duration,
  FontFamily,
  FontSizes,
  getDiscountMonthByCity,
  IAddons,
  IconAssets,
  IconImage,
  IDate,
  IPriceSub,
  IService,
  OptionalChoosePet,
  PostTaskHelpers,
  PremiumOptional,
  PriceButtonSubscription,
  RepeatWeekly,
  ScrollView,
  Spacing,
  TimePicker,
  TouchableOpacity,
  useI18n,
  useSettingsStore,
} from '@btaskee/design-system';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { includes, isEmpty } from 'lodash-es';

import { EcoOptional } from '@components';
import { useAppNavigation, useChangeData, usePostTask } from '@hooks';
import { RouteName } from '@navigation/RouteName';
import { ParamsNavigationList } from '@navigation/type';
import { usePostTaskStore } from '@stores';

import styles from './styles';

type ChooseServiceRoute = NativeStackScreenProps<
  ParamsNavigationList,
  RouteName.ChooseDuration
>;

export const ChooseDuration = ({ route }: ChooseServiceRoute) => {
  const navigation = useAppNavigation();

  const { t } = useI18n('cleaningSub');
  const { t: tCommon } = useI18n('common');
  const { settings } = useSettingsStore();

  const {
    address,
    duration,
    month,
    isPremium,
    service,
    isEco,
    price,
    pet,
    addons,
    weekdays,
    startDate,
    isLoadingPrice,
    setAddons,
    setIsEco,
    setStartDate,
    resetState,
    schedule,
    setSchedule,
  } = usePostTaskStore();

  const {
    onChangePremiumService,
    checkPremiumOption,
    onChangeMonth,
    setPetOption,
    onChangeDuration,
    onChangeDateTime,
    onChangeRepeatWeekly,
    onChangeEco,
    setDataPTSubscription,
  } = useChangeData();

  const { getPrice } = usePostTask();

  // Get data from notification
  const isShowEcoOption = route?.params?.isShowEcoOption;
  const renewOldSubscription = route?.params?.renewOldSubscription; // Data from renew subscription

  const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

  const petOption = service?.addons?.find(
    (item: IAddons) => item?.name === AddonsName.Pet,
  );

  const optionToolBar = useMemo(() => {
    return {
      headerTitle: (
        <BlockView
          flex
          row
          horizontal
        >
          <IconImage
            source={IconAssets.icLocation}
            size={24}
            color={ColorsV2.red500}
          />
          <BlockView margin={{ left: Spacing.SPACE_08 }}>
            <CText>{address?.shortAddress}</CText>
            <CText
              bold
              numberOfLines={1}
              margin={{ right: Spacing.SPACE_16 }}
            >
              {address?.address}
            </CText>
          </BlockView>
        </BlockView>
      ),
    };
  }, [address]);

  const isSupportPremium = useMemo(() => {
    return includes(
      service?.premiumOptions?.applyForCities || [],
      address?.city,
    );
  }, [address?.city, service?.premiumOptions?.applyForCities]);

  React.useLayoutEffect(() => {
    navigation.setOptions({
      headerTitle: () => optionToolBar.headerTitle,
    });
  }, [navigation, optionToolBar.headerTitle]);

  React.useEffect(() => {
    const days = settings?.numberOfDaysPostTaskFromNow || 3; //get days min post task
    const hours = settings?.defaultTaskTime || 8; // get time post task
    const defaultDate = DateTimeHelpers.formatToString({
      timezone,
      date: DateTimeHelpers.toDayTz({ timezone })
        .add(days, 'day')
        .set('hour', hours)
        .startOf('hour'),
    });
    setStartDate?.(defaultDate);

    // navigate from renew subscription in task subscription
    if (!isEmpty(renewOldSubscription)) {
      setDataPTSubscription?.(renewOldSubscription);
    }

    // Nếu có yêu cầu thì bật option eco
    if (isShowEcoOption) {
      setIsEco?.(true);
    }
    return () => {
      resetState?.();
    };
  }, []);

  React.useEffect(() => {
    checkPremiumOption({
      citiesApplyPremium: service?.premiumOptions?.applyForCities || [],
      address: address,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [address, service?.premiumOptions?.applyForCities]);

  const onConfirmed = () => {
    const postingLimits = service?.postingLimits;
    // check posting limit, ex 6AM - 10PM
    if (
      !PostTaskHelpers.checkTimeValidFromService(
        timezone,
        startDate as IDate,
        duration,
        postingLimits,
      )
    ) {
      const postingLimitsFormat = PostTaskHelpers.formatPostingLimits({
        timezone,
        postingLimits,
      });

      return Alert.alert.open({
        title: tCommon('DIALOG_TITLE_INFORMATION'),
        message: tCommon('NEW_POST_TASK_STEP2_TIME_INVALID', {
          from: postingLimitsFormat.from,
          to: postingLimitsFormat.to,
        }),
        actions: [{ text: tCommon('CLOSE') }],
      });
    }

    // data ok
    onNextStep();
  };

  const onNextStep = async () => {
    // setStepPostTask?.(TRACKING_STEP.STEP_4);
    navigation.navigate(RouteName.Notes);
  };

  const openSchedule = () => {
    Alert.alert.open({
      title: t('WORKING_SCHEDULE'),
      message: (
        <ChangeScheduleSub
          schedule={schedule}
          weekDays={weekdays}
          getPriceSubscription={getPrice}
          updateScheduleSubscription={setSchedule}
          pricing={price?.pricing}
          service={service as IService}
          setStartDate={setStartDate}
          timezone={timezone}
        />
      ),
      contentContainerStyle: {
        paddingHorizontal: 0,
      },
      titleStyle: {
        fontSize: FontSizes.SIZE_20,
        fontWeight: 'bold',
        fontFamily: FontFamily.FONT_BOLD,
      },
    });
  };

  const discountByMonth = getDiscountMonthByCity(address?.city);

  return (
    <BlockView style={{ backgroundColor: ColorsV2.neutralBackground }}>
      <ScrollView
        testID="scrollStep2Cleaning"
        contentContainerStyle={styles.containerScroll}
        showsVerticalScrollIndicator={false}
      >
        <RepeatWeekly
          isHideWeekly
          isEnabled={true}
          style={styles.weekly}
          weeklyRepeater={weekdays}
          onChange={onChangeRepeatWeekly}
          contentStyle={styles.contentWeeklyStyle}
          titleStyle={styles.txtTitleRepeatWeekly}
          title={t('SELECT_WORK_SCHEDULE')}
        />

        <BlockView>
          <CText style={styles.txtPanel}>{t('CHOOSE_TIME_START')}</CText>
          <TimePicker
            isHideLabel={true}
            value={startDate as IDate}
            onChange={onChangeDateTime}
            settingSystem={settings?.settingSystem}
            style={styles.contentTimePicker}
            timeStyle={styles.timeContainerStyle}
            containerStyle={styles.wrapperTimePicker}
            timezone={timezone}
          />
        </BlockView>

        <BlockView>
          <CText style={[styles.txtPanel]}>{tCommon('DURATION')}</CText>
          <Duration
            duration={duration}
            onChange={onChangeDuration}
          />
        </BlockView>

        <ConditionView
          condition={Boolean(isShowEcoOption)}
          viewFalse={
            <PremiumOptional
              isPremium={isPremium}
              onChangePremium={onChangePremiumService}
              service={service as IService}
              address={address}
              txtPropStyle={styles.txtPanel}
            />
          }
          viewTrue={
            <EcoOptional
              isEco={Boolean(isEco)}
              onChangeEco={onChangeEco}
            />
          }
        />
        {/* Neu eco thi hien thi bo cong cu dung cu thuong */}
        <CleaningTools
          isPremiumSelected={isPremium}
          isSupportPremium={isSupportPremium && !isShowEcoOption}
        />

        {/* Hiển thị pet option */}
        <ConditionView
          condition={Boolean(petOption)}
          viewTrue={
            <BlockView style={[styles.containerPetOption]}>
              <CText
                h4
                bold
                style={styles.txtPanel}
              >
                {t('PT1_DETAIL_OPTION_TITLE')}
              </CText>
              <BlockView>
                <OptionalChoosePet
                  onChangePet={setPetOption}
                  pet={pet}
                  setAddons={setAddons}
                  addons={addons}
                  petOption={petOption}
                />
              </BlockView>
            </BlockView>
          }
        />
        <ChooseMonth
          month={month}
          onChange={onChangeMonth}
          service={service as IService}
          discountByMonth={discountByMonth}
        />

        <ConditionView
          condition={!isEmpty(schedule)}
          viewTrue={
            <BlockView margin={{ top: Spacing.SPACE_16 }}>
              <TouchableOpacity onPress={openSchedule}>
                <Animated.View
                  entering={FadeIn}
                  exiting={FadeOut}
                >
                  <BlockView
                    horizontal
                    jBetween
                    row
                    style={styles.boxContent}
                  >
                    <CText style={styles.txtCheck}>
                      {t('CHECK_SCHEDULE_SUBSCRIPTION')}
                    </CText>
                    <IconImage source={IconAssets.icCalendar} />
                  </BlockView>
                </Animated.View>
              </TouchableOpacity>
            </BlockView>
          }
        />
      </ScrollView>
      <PriceButtonSubscription
        testID="btnNextStep2"
        onPress={onConfirmed}
        price={price as IPriceSub}
        isLoading={isLoadingPrice}
      />
    </BlockView>
  );
};
