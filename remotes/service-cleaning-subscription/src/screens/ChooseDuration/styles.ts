import { Dimensions, StyleSheet } from 'react-native';
import {
  BorderRadius,
  ColorsV2,
  FontSizes,
  Spacing,
} from '@btaskee/design-system';

const { height } = Dimensions.get('window');

export default StyleSheet.create({
  contentWeeklyStyle: {
    marginBottom: 0,
    marginTop: Spacing.SPACE_16,
  },
  weekly: {
    padding: 0,
    marginTop: 0,
    marginHorizontal: 0,
    marginBottom: Spacing.SPACE_16,
  },
  containerScroll: {
    flexGrow: 1,
    padding: Spacing.SPACE_16,
    paddingBottom: height * 0.25,
    marginTop: Spacing.SPACE_08,
    backgroundColor: ColorsV2.neutralWhite,
  },
  txtPanel: {
    fontWeight: 'bold',
    fontFamily: 'Montserrat-Bold',
    color: ColorsV2.neutral800,
    fontSize: FontSizes.SIZE_16,
    marginVertical: Spacing.SPACE_16,
  },
  txtTitleRepeatWeekly: {
    fontWeight: 'bold',
    fontFamily: 'Montserrat-Bold',
    color: ColorsV2.neutral800,
    fontSize: FontSizes.SIZE_16,
  },
  timeContainerStyle: {
    borderWidth: 1,
    backgroundColor: ColorsV2.neutralWhite,
    borderColor: ColorsV2.neutral100,
    borderRadius: BorderRadius.RADIUS_08,
  },
  wrapCleaningToolEco: {
    marginTop: Spacing.SPACE_16,
  },
  containerPetOption: {
    marginTop: '6%',
  },
  wrapperTimePicker: {
    marginHorizontal: 0,
    borderWidth: 0,
    marginTop: 0,
    marginBottom: Spacing.SPACE_16,
  },
  contentTimePicker: {
    paddingHorizontal: 0,
    marginVertical: 0,
  },
  txtCheck: {
    color: ColorsV2.orange500,
  },
  boxContent: {
    height: Spacing.SPACE_48,
    borderRadius: BorderRadius.RADIUS_08,
    paddingHorizontal: Spacing.SPACE_16,
    backgroundColor: ColorsV2.orange50,
  },
});
