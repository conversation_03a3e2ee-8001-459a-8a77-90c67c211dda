{"name": "service-cleaning-subscription", "targets": {"start": {"executor": "nx:run-commands", "options": {"command": "yarn start", "cwd": "remotes/service-cleaning-subscription"}}, "install": {"executor": "nx:run-commands", "options": {"command": "yarn install", "cwd": "remotes/service-cleaning-subscription"}}, "build": {"executor": "nx:run-commands", "options": {"command": "yarn build", "cwd": "remotes/service-cleaning-subscription"}}, "deploy": {"executor": "nx:run-commands", "options": {"command": "yarn deploy", "cwd": "remotes/service-cleaning-subscription"}}, "reset": {"executor": "nx:run-commands", "options": {"command": "yarn reset", "cwd": "remotes/service-cleaning-subscription"}}}, "tags": ["scope:service-cleaning-subscription"]}