import { getTextWithLocale, ISO_CODE, LOCALES } from '@btaskee/design-system';

import { ITypeOfCityAC } from '../../types';

export const getUnitACByIsoCode = (isoCode: ISO_CODE) => {
  if (isoCode === ISO_CODE.TH) {
    return 'BTU';
  }
  if (isoCode === ISO_CODE.ID) {
    return 'PK';
  }
  return 'HP';
};

/**
 * @param {*} typeServiceAC type from AC service
 * @returns array list AC service
 */
export const getTypeServiceAC = (
  typeServiceAC: ITypeOfCityAC[] = [],
  locale: LOCALES = LOCALES.en,
) => {
  const typeService = [];
  for (let index = 0; index < typeServiceAC.length; index++) {
    const element = typeServiceAC[index];
    typeService.push({
      ...element,
      key: index,
      title: getTextWithLocale(element?.text, locale),
    });
  }
  return typeService;
};
