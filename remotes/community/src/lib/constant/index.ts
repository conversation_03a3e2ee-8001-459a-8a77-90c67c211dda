import {
  builtIn,
  cassette,
  ceilling,
  portable,
  wall,
} from '../../assets/images';

export const TYPE_AC_SERVICE = [
  { type: 'Wall', imageBackground: wall },
  { type: 'Split', imageBackground: wall },
  { type: 'Ceilling', imageBackground: ceilling },
  { type: 'Cassette', imageBackground: cassette },
  { type: 'Built-in', imageBackground: builtIn },
  { type: 'Portable', imageBackground: portable },
];

export const CLEANING_AIR_CONDITIONER = 'Cleaning'; // add on service default ac
export const REFILL_GAS_AIR_CONDITIONER = 'Refill'; // add on service default ac
export const MAX_QUANTITY_AIR_CONDITIONER = 10;
export enum SERVICE_AC_TYPES {
  WALL = 'Wall',
  SPLIT = 'Split',
  CEILING = 'Ceilling',
  CASSETTE = 'Cassette',
  BUILT_IN = 'Built-in',
  PORTABLE = 'Portable',
}
