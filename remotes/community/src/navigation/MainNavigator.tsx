import React from 'react';
import { AlertHolder, AlertRef, CAlert } from '@btaskee/design-system';
import { createNativeStackNavigator } from '@react-navigation/native-stack';

import { ChooseAddress } from '@screens/ChooseAddress';
import { ChooseDateTime } from '@screens/ChooseDateTime';
import { ChooseService } from '@screens/ChooseService';
import { ConfirmAndPayment } from '@screens/ConfirmAndPayment';
import { IntroService } from '@screens/IntroService';

import { RouteName } from './RouteName';
import { MainStackParamList } from './type';

const Main = createNativeStackNavigator<MainStackParamList>();

const MainNavigator = () => {
  return (
    <>
      <Main.Navigator screenOptions={{ headerShown: false }}>
        <Main.Screen
          name={RouteName.IntroService}
          component={IntroService}
        />
        <Main.Screen
          name={RouteName.ChooseAddress}
          component={ChooseAddress as any}
        />
        <Main.Screen
          name={RouteName.ChooseService}
          component={ChooseService}
        />
        <Main.Screen
          name={RouteName.ChooseDateTime}
          component={ChooseDateTime}
        />
        <Main.Screen
          name={RouteName.ConfirmAndPayment}
          component={ConfirmAndPayment}
        />
      </Main.Navigator>
      <CAlert ref={(ref: AlertRef) => AlertHolder.setAlert(ref)} />
    </>
  );
};

export default MainNavigator;
