import { NativeStackNavigationProp } from '@react-navigation/native-stack';

import { RouteName } from './RouteName';
import { HomeStackParamList } from './tabs';

export type MainStackParamList = {
  [RouteName.Home]: any;
  [RouteName.IntroService]: any;
  [RouteName.ChooseAddress]: any;
  [RouteName.ChooseService]: any;
  [RouteName.ChooseDateTime]: any;
  [RouteName.ConfirmAndPayment]: any;
};

export type ParamsNavigationList = HomeStackParamList;

export type NavigationProps = NativeStackNavigationProp<ParamsNavigationList>;
