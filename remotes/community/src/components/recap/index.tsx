import React, { useMemo } from 'react';
import {
  BlockView,
  BorderRadius,
  Colors,
  ConditionView,
  CText,
  <PERSON>ceHelper,
  FontSizes,
  HitSlop,
  Icon,
  Spacing,
  TouchableOpacity,
  useI18n,
} from '@btaskee/design-system';
import { useACStore } from '@store';

import { ListDeviceSelected } from '../list-device-selected';
import styles from './styles';

export const Recap = ({
  onPress,
  isShow,
  setIndex,
  hideModal,
  scrollToBottom,
}: {
  onPress: () => void;
  setIndex: (index: number) => void;
  isShow: boolean;
  hideModal: () => void;
  scrollToBottom: (value: boolean) => void;
}) => {
  const { t } = useI18n('common');
  const { selectedAirConditioner } = useACStore();

  const totalAC = useMemo(() => {
    let numberAC = 0;
    selectedAirConditioner?.forEach((element) => {
      numberAC += element?.quantity || 0;
    });
    return numberAC;
  }, [selectedAirConditioner]);

  if (totalAC === 0) {
    return null;
  }

  return (
    <BlockView
      margin={{ bottom: Spacing.SPACE_16 }}
      hitSlop={HitSlop.MEDIUM}
    >
      <TouchableOpacity
        testID="btnRecap"
        activeOpacity={0.7}
        onPress={onPress}
      >
        <BlockView
          row
          jBetween
          horizontal
        >
          <CText
            testID="txtSelectedDevice"
            bold
            size={FontSizes.SIZE_16}
          >
            {t('SELECTED_DEVICE')}
          </CText>
          <BlockView
            row
            horizontal
          >
            <BlockView
              center
              margin={{ right: Spacing.SPACE_12 }}
              width={24}
              height={24}
              radius={BorderRadius.RADIUS_16}
              backgroundColor={Colors.RED}
            >
              <CText
                testID="txtTotalAC"
                color={Colors.WHITE}
              >
                {totalAC}
              </CText>
            </BlockView>
            <Icon
              name={isShow ? 'icArrowDown' : 'icArrowUp'}
              color={Colors.SECONDARY_COLOR}
              size={20}
            />
          </BlockView>
        </BlockView>
      </TouchableOpacity>
      <ConditionView
        condition={isShow}
        viewTrue={
          <BlockView
            margin={{ top: Spacing.SPACE_16 }}
            backgroundColor={Colors.BORDER_COLOR}
            maxHeight={DeviceHelper.WINDOW.HEIGHT / 2.5}
            style={styles.wrapList}
          >
            <ListDeviceSelected
              dataAC={selectedAirConditioner}
              setIndex={setIndex}
              hideModal={hideModal}
              scrollToBottom={scrollToBottom}
            />
          </BlockView>
        }
      />
    </BlockView>
  );
};
