import { StyleSheet } from 'react-native';
import {
  Colors,
  DeviceHelper,
  FontSizes,
  Spacing,
} from '@btaskee/design-system';

const styles = StyleSheet.create({
  processCleaningContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: Spacing.SPACE_12,
    backgroundColor: Colors.GREY_3,
    paddingVertical: Spacing.SPACE_08,
    borderRadius: 8,
  },
  iconProcessStyle: {
    width: 40,
    height: 40,
    marginLeft: Spacing.SPACE_12,
  },
  processContentContainer: {
    marginLeft: Spacing.SPACE_12,
    flex: 1,
  },
  processTxtContent: {
    lineHeight: 20,
    color: Colors.BLACK,
  },
  iconArrowRightStyle: {
    width: 26,
    height: 26,
    marginRight: 22,
  },
  txtContentStyle: {
    color: Colors.BLACK,
    fontSize: FontSizes.SIZE_16,
    paddingLeft: Spacing.SPACE_12,
    marginTop: 0,
  },
  dotStyle: {
    marginTop: Spacing.SPACE_08,
    width: 8,
    height: 8,
    borderRadius: 20,
    backgroundColor: Colors.PRIMARY_COLOR,
  },
  txtContent: {
    color: Colors.PRIMARY_COLOR,
    fontSize: FontSizes.SIZE_16,
    lineHeight: 24,
    marginBottom: Spacing.SPACE_08,
  },
  titleModal: {
    textAlign: 'center',
  },
  contentContainerStyle: {
    maxHeight: Math.round(DeviceHelper.WINDOW.HEIGHT * 0.7),
  },
});
export default styles;
