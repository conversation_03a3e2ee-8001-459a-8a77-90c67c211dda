import {
  HomeType,
  IAddress,
  IDate,
  IPrice,
  IRelatedTask,
  IService,
  ISO_CODE,
  ITimezone,
  IUser,
} from '@btaskee/design-system';
import { IAddons, ISelectedAC } from '@types';
import { create } from 'zustand';

interface AppState {
  address: IAddress;
  duration: number;
  date: IDate;
  note: string;
  isApplyNoteForAllTask: boolean;
  price: IPrice | null;
  service: IService;
  isoCode: ISO_CODE;
  currency: {
    sign: string;
    code: string;
  };
  timezone: ITimezone;
  paymentMethod: any;
  promotion: any;
  loadingPrice: boolean;
  loadingPostTask: boolean;
  relatedTask: IRelatedTask | null;
  user: IUser | null;
  addons: IAddons[];
  homeType: HomeType;
  dateOptions: IDate[];
  selectedAirConditioner: ISelectedAC[];
  setAddress: (address: IAddress) => void;
  setDuration: (duration: number) => void;
  setDateTime: (date: IDate) => void;
  setNote: (note: string) => void;
  setIsApplyNoteForAllTask: (isApplyNoteForAllTask: boolean) => void;
  setPrice: (price: IPrice | null) => void;
  homeNumber: string;
  setHomeNumber: (homeNumber: string) => void;
  setPaymentMethod: (paymentMethod: any) => void;
  setPromotion: (promotion: any) => void;
  setLoadingPrice: (loadingPrice: boolean) => void;
  setLoadingPostTask: (loadingPostTask: boolean) => void;
  setRelatedTask: (relatedTask: IRelatedTask) => void;
  setUser: (user: IUser) => void;
  setAddons: (addons: IAddons[]) => void;
  setHomeType: (homeType: HomeType) => void;
  setDateOptions: (dateOptions: IDate[]) => void;
  setSelectedAirConditioner: (selectedAirConditioner: ISelectedAC[]) => void;
}

export const useACStore = create<AppState>((set) => ({
  address: {
    city: 'Hồ Chí Minh',
    district: 'Quận 1',
    ward: 'Phường 1',
    street: 'Đường 1',
    number: '1',
    latitude: 10.7769,
    longitude: 106.7099,
  },
  selectedAirConditioner: [],
  duration: 3,
  isAutoChooseTasker: true,
  date: '2025-07-07T07:00:00.000+0000',
  schedule: [],
  isEnabledSchedule: false,
  note: '',
  isApplyNoteForAllTask: false,
  homeNumber: '6tht Floor',
  price: null,
  service: {
    _id: '3T8NhtMJo8mkARqLH',
    icon: 'https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/q6EzbvK9qAsoWLgoS',
    text: {
      vi: 'Vệ sinh máy lạnh',
      en: 'Air-conditioner Service',
      ko: '에어컨 청소',
      th: 'ทำความสะอาดเครื่องปรับอากาศ',
      id: 'Layanan AC',
    },
    status: 'ACTIVE',
    costSuggestion: 150000.0,
    weight: 4.0,
    discountByDuration: [
      {
        duration: 2.0,
        discount: 0.9,
      },
      {
        duration: 3.0,
        discount: 0.85,
      },
      {
        duration: 4.0,
        discount: 0.85,
      },
      {
        duration: 5.0,
        discount: 0.8,
      },
      {
        duration: 6.0,
        discount: 0.8,
      },
      {
        duration: 7.0,
        discount: 0.8,
      },
      {
        duration: 8.0,
        discount: 0.8,
      },
    ],
    discountByDoneTask: [
      {
        number: 0.0,
        discount: 1.0,
      },
      {
        number: 1.0,
        discount: 1.0,
      },
      {
        number: 2.0,
        discount: 1.0,
      },
      {
        number: 3.0,
        discount: 1.0,
      },
      {
        number: 4.0,
        discount: 1.0,
      },
      {
        number: 5.0,
        discount: 1.0,
      },
    ],
    city: [
      {
        name: 'Hồ Chí Minh',
        baseCost: 100000,
      },
    ],
    onlyShowTasker: false,
    detail: {
      city: [
        {
          name: 'Hồ Chí Minh',
          type: [
            {
              name: 'Wall',
              text: {
                vi: 'Treo tường',
                en: 'Wall',
                ko: '벽걸이',
                th: 'ติดผนัง',
              },
              services: [
                {
                  name: 'Cleaning',
                  text: {
                    vi: 'Vệ sinh',
                    en: 'Cleaning',
                    ko: '청소',
                    th: 'ล้างแอร์',
                  },
                  prices: [
                    {
                      HPFrom: null,
                      HPTo: 2.0,
                      price: 180000.0,
                    },
                    {
                      HPFrom: 2.0,
                      HPTo: null,
                      price: 204000.0,
                    },
                  ],
                  discountByQty: [
                    {
                      qty: 1.0,
                      discount: 1.2,
                    },
                    {
                      qty: 2.0,
                      discount: 1.0,
                    },
                    {
                      qty: 3.0,
                      discount: 0.9,
                    },
                    {
                      qty: 4.0,
                      discount: 0.9,
                    },
                  ],
                },
                {
                  name: 'Refill',
                  text: {
                    vi: 'Bơm Gas',
                    en: 'Gas Refill',
                    ko: '가스 펌프',
                    th: 'เติมน้ำยาแอร์',
                  },
                  prices: [
                    {
                      HPFrom: null,
                      HPTo: 2.0,
                      price: 120000.0,
                    },
                    {
                      HPFrom: 2.0,
                      HPTo: null,
                      price: 160000.0,
                    },
                  ],
                },
              ],
            },
            {
              name: 'Portable',
              text: {
                vi: 'Tủ đứng',
                en: 'Portable',
                ko: '휴대용 에어 컨디셔너',
                th: 'เครื่องแอร์แนวตั้ง',
              },
              services: [
                {
                  name: 'Cleaning',
                  text: {
                    vi: 'Vệ sinh',
                    en: 'Cleaning',
                    ko: '청소',
                    th: 'ล้างแอร์',
                  },
                  prices: [
                    {
                      HPFrom: null,
                      HPTo: null,
                      price: 300000.0,
                    },
                  ],
                  discountByQty: [
                    {
                      qty: 1.0,
                      discount: 1.2,
                    },
                    {
                      qty: 2.0,
                      discount: 1.0,
                    },
                    {
                      qty: 3.0,
                      discount: 0.9,
                    },
                    {
                      qty: 4.0,
                      discount: 0.9,
                    },
                  ],
                },
                {
                  name: 'Refill',
                  text: {
                    vi: 'Bơm Gas',
                    en: 'Gas refill',
                    ko: '가스 펌프',
                    th: 'เติมน้ำยาแอร์',
                  },
                  prices: [
                    {
                      HPFrom: null,
                      HPTo: null,
                      price: 240000.0,
                    },
                  ],
                },
              ],
            },
            {
              name: 'Cassette',
              text: {
                vi: 'Âm trần',
                en: 'Cassette',
                ko: '천장형',
                th: 'สี่ทิศทาง',
              },
              services: [
                {
                  name: 'Cleaning',
                  text: {
                    vi: 'Vệ sinh',
                    en: 'Cleaning',
                    ko: '청소',
                    th: 'ล้างแอร์',
                  },
                  prices: [
                    {
                      HPFrom: null,
                      HPTo: 3.0,
                      price: 360000.0,
                    },
                    {
                      HPFrom: 3.0,
                      HPTo: null,
                      price: 540000.0,
                    },
                  ],
                  discountByQty: [
                    {
                      qty: 1.0,
                      discount: 1.2,
                    },
                    {
                      qty: 2.0,
                      discount: 1.0,
                    },
                    {
                      qty: 3.0,
                      discount: 0.9,
                    },
                    {
                      qty: 4.0,
                      discount: 0.9,
                    },
                  ],
                },
                {
                  name: 'Refill',
                  text: {
                    vi: 'Bơm Gas',
                    en: 'Gas refill',
                    ko: '가스 펌프',
                    th: 'เติมน้ำยาแอร์',
                  },
                  prices: [
                    {
                      HPFrom: null,
                      HPTo: 3.0,
                      price: 200000.0,
                    },
                    {
                      HPFrom: 3.0,
                      HPTo: null,
                      price: 240000.0,
                    },
                  ],
                },
              ],
            },
            {
              name: 'Ceilling',
              text: {
                vi: 'Áp trần',
                en: 'Floor/Ceiling',
                ko: '상업용 천장형',
                th: 'แขวน/ตั้งพื้น',
              },
              services: [
                {
                  name: 'Cleaning',
                  text: {
                    vi: 'Vệ sinh',
                    en: 'Cleaning',
                    ko: '청소',
                    th: 'ล้างแอร์',
                  },
                  prices: [
                    {
                      HPFrom: null,
                      HPTo: 5.0,
                      price: 550000.0,
                    },
                    {
                      HPFrom: 5.0,
                      HPTo: null,
                      price: 660000.0,
                    },
                  ],
                  discountByQty: [
                    {
                      qty: 1.0,
                      discount: 1.2,
                    },
                    {
                      qty: 2.0,
                      discount: 1.0,
                    },
                    {
                      qty: 3.0,
                      discount: 0.9,
                    },
                    {
                      qty: 4.0,
                      discount: 0.9,
                    },
                  ],
                },
                {
                  name: 'Refill',
                  text: {
                    vi: 'Bơm Gas',
                    en: 'Gas Refill',
                    ko: '가스 펌프',
                    th: 'เติมน้ำยาแอร์',
                  },
                  prices: [
                    {
                      HPFrom: null,
                      HPTo: 5.0,
                      price: 280000.0,
                    },
                    {
                      HPFrom: 5.0,
                      HPTo: null,
                      price: 300000.0,
                    },
                  ],
                },
              ],
            },
            {
              name: 'Built-in',
              text: {
                vi: 'Giấu trần',
                en: 'Built-in',
                ko: '빌트인',
                th: 'ฝังเพดาน',
              },
              services: [
                {
                  name: 'Cleaning',
                  text: {
                    vi: 'Vệ sinh',
                    en: 'Cleaning',
                    ko: '청소',
                    th: 'ล้างแอร์',
                  },
                  prices: [
                    {
                      HPFrom: null,
                      HPTo: null,
                      price: 200000.0,
                    },
                  ],
                  discountByQty: [
                    {
                      qty: 1.0,
                      discount: 1.2,
                    },
                    {
                      qty: 2.0,
                      discount: 1.0,
                    },
                    {
                      qty: 3.0,
                      discount: 0.9,
                    },
                    {
                      qty: 4.0,
                      discount: 0.9,
                    },
                  ],
                },
                {
                  name: 'Refill',
                  text: {
                    vi: 'Bơm Gas',
                    en: 'Gas Refill',
                    ko: '가스 펌프',
                    th: 'เติมน้ำยาแอร์',
                  },
                  prices: [
                    {
                      HPFrom: null,
                      HPTo: null,
                      price: 200000.0,
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
    },
    priceSetting: {
      costForChooseTasker: 20000.0,
      emergencyTaskWithin: 180.0,
      feeForEmergencyTask: 20000.0,
      feeForWeekend: 0.2,
      feeWeekendApplyForCity: [
        'Hồ Chí Minh',
        'Hà Nội',
        'Hải Phòng',
        'Thừa Thiên Huế',
        'Thanh Hóa',
        'Nghệ An',
        'Bình Định',
      ],
      surgePriceTime: [
        {
          start: 22.0,
          end: 24.0,
          rate: 0.2,
        },
        {
          start: 6.0,
          end: 8.0,
          rate: 0.2,
        },
        {
          start: 19.0,
          end: 22.0,
          rate: 0.2,
        },
      ],
    },
    pauseSetting: {
      isDisabled: false,
      title: {
        vi: 'THÔNG BÁO TẠM NGỪNG PHỤC VỤ',
        en: 'TEMPORARY SUSPENSION OF SERVICE ANNOUNCEMENT',
        ko: 'TEMPORARY SUSPENSION OF SERVICE ANNOUNCEMENT',
        th: 'TEMPORARY SUSPENSION OF SERVICE ANNOUNCEMENT',
      },
      content: {
        vi: 'Nhằm thực hiện các biện pháp phòng chống dịch bệnh Covid-19 theo yêu cầu của Thủ tướng Chính phủ, bTaskee sẽ tạm ngừng phục vụ 15 ngày, kể từ ngày 01/04/2020.\nBắt đầu từ 16/04/2020, bTaskee sẽ hoạt động lại bình thường. Rất xin lỗi quý Khách hàng vì sự bất tiện này.\nTrong thời gian ngừng phục vụ, nếu cần bất kỳ thông tin hay sự hỗ trợ nào, Quý Khách hàng vui lòng liên hệ tổng đài 1900636736.',
        en: 'Following official prevention of coronavirus pandemics measures at the request of the Prime Minister, bTaskee will suspend our services for 15 days from April 1st 2020.\nStarting from April 16th 2020, bTaskee will resume our services again as usual. We apologize to our customers for this inconvenience.\nDuring the service downtime, if you need any information or support, please contact our hotline 1900636736.',
        ko: 'Following official prevention of coronavirus pandemics measures at the request of the Prime Minister, bTaskee will suspend our services for 15 days from April 1st 2020.\nStarting from April 16th 2020, bTaskee will resume our services again as usual. We apologize to our customers for this inconvenience.\nDuring the service downtime, if you need any information or support, please contact our hotline 1900636736.',
        th: 'Following official prevention of coronavirus pandemics measures at the request of the Prime Minister, bTaskee will suspend our services for 15 days from April 1st 2020.\nStarting from April 16th 2020, bTaskee will resume our services again as usual. We apologize to our customers for this inconvenience.\nDuring the service downtime, if you need any information or support, please contact our hotline 1900636736.',
      },
    },
    name: 'AIR_CONDITIONER_SERVICE',
    shortText: {
      vi: 'Vệ sinh máy lạnh',
      en: 'AC Cleaning',
      ko: '에어컨 청소',
      th: 'บริการล้างแอร์',
      id: 'Cuci AC',
    },
    postingLimits: {
      from: '6:00:00',
      to: '23:00:00',
    },
    defaultTaskTime: 14.0,
    group: 'AIR_CONDITIONER_SERVICE',
    onlyShowAsker: false,
    thumbnail:
      'https://btaskee-stag.s3.ap-southeast-1.amazonaws.com/app/asker/services/thumbnails/may-lanh.png',
    relatedServiceIds: [
      'pcZRQ6PqmjrAPe5gt',
      'xTgw3s7tdpJa4JNJj',
      'QFPAZgMSejyMRgNWb',
      '3T8NhtMJo8mkARqLH123',
    ],
    allowedTaskerGender: 'BOTH',
    tetBookingDates: {
      bookTaskTime: {
        fromDate: '2025-01-12T17:00:00.000+0000',
        toDate: '2025-02-07T16:59:00.000+0000',
      },
      fromDate: '2025-01-02T17:00:00.000+0000',
      toDate: '2025-02-07T16:59:00.000+0000',
    },
    isPartner: true,
  },
  timezone: 'Asia/Ho_Chi_Minh',
  isoCode: ISO_CODE.VN,
  dataQuickPostTask: null,
  currency: {
    sign: '₫',
    code: 'VND',
  },
  paymentMethod: {
    name: 'Cash',
    label: 'PAYMENT_METHOD_DIRECT_CASH',
    value: 'CASH',
  },
  promotion: null,
  loadingPrice: false,
  loadingPostTask: false,
  relatedTask: null,
  user: {
    _id: 'x5956c8b1f6f6638252c7330af9cdbe79',
    referralCode: 'iser0m1K',
    status: 'ACTIVE',
    language: 'en',
    name: 'Kaiserr',
    phone: '**********',
    type: 'ASKER',
    countryCode: '+84',
    isoCode: 'VN',
    favouriteTasker: ['x9e13e7f57e7d07e514bf4c33c9c6c8c5'],
    username: '***********',
    isUsedZaloPay: false,
    dob: null,
    fAccountId: 'x31b76fcc2540052220c01db1c1d8b845',
    point: 15.0,
    appVersion: '3.41.0',
    buildNumber: '341007',
    locations: [
      {
        contact: 'Kaiser',
        lng: 105.8501651,
        description: '123',
        shortAddress: '12 Ngõ Gạch',
        phoneNumber: '**********',
        countryCode: '+84',
        lat: 21.0364613,
        _id: 'x627693812a85089085961170de2bc836',
        address:
          'Hà Nội Golden Hotel, Ngõ Gạch, Phố cổ Hà Nội, Hàng Buồm, Hoàn Kiếm, Hà Nội, Việt Nam',
        country: 'VN',
        isAddressMaybeWrong: false,
        isoCode: 'VN',
        city: 'Hà Nội',
        homeType: 'HOME',
        district: 'Hoàn Kiếm',
        isDefault: false,
      },
      {
        _id: 'x6432b6860200baa64e268fc17d0fce81',
        lat: 10.7394465,
        lng: 106.696324,
        country: 'VN',
        city: 'Hồ Chí Minh',
        district: 'Quận 7',
        address:
          'bTaskee, Đường D1, Khu đô thị Him Lam, Tân Hưng, Quận 7, Hồ Chí Minh, Việt Nam',
        contact: 'Kaiser',
        phoneNumber: '**********',
        shortAddress: '69 Đường D1',
        countryCode: '+84',
        isoCode: 'VN',
        homeType: 'HOME',
        description: '123',
      },
      {
        _id: 'x58396af32b3fedf5daf997414428ea08',
        lat: 13.7563309,
        lng: 100.5017651,
        country: 'TH',
        city: 'Bangkok',
        district: 'Phra Nakhon',
        address: 'Bangkok, ประเทศไทย',
        contact: 'Kaiser',
        phoneNumber: '**********',
        shortAddress: 'Bangkok Bangkok',
        countryCode: '+84',
        isoCode: 'TH',
        homeType: 'HOME',
        description: '123',
        isAddressMaybeWrong: true,
      },
    ],
    housekeepingLocations: [
      {
        shortAddress: '33 Ngõ 75 Phố Trần Thái Tông',
        countryCode: '+84',
        phoneNumber: '**********',
        address:
          'Văn phòng bTaskee Hà Nội, Ngõ 75 Phố Trần Thái Tông, Dịch Vọng, Cầu Giấy, Hà Nội, Việt Nam',
        district: 'Cầu Giấy',
        _id: 'xf2f93749112ca1f79a15ce9ba3c6f5ef',
        lng: 105.7908949,
        country: 'VN',
        description: '123',
        homeType: 'HOME',
        isoCode: 'VN',
        lat: 21.0326474,
        locationName: 'room1',
        city: 'Hà Nội',
        contact: 'Kaiser',
      },
      {
        shortAddress: '69 Đường D1',
        city: 'Hồ Chí Minh',
        country: 'VN',
        homeType: 'HOME',
        lat: 10.7394465,
        lng: 106.696324,
        locationName: 'D1',
        countryCode: '+84',
        isoCode: 'VN',
        phoneNumber: '**********',
        _id: 'x0a3f1a65f3c172938e61e26acc4553ab',
        address:
          'bTaskee, Đường D1, Khu đô thị Him Lam, Tân Hưng, Quận 7, Hồ Chí Minh, Việt Nam',
        contact: 'Kaiser',
        description: '123',
        district: 'Quận 7',
      },
    ],
    hospitalLocations: [
      {
        lat: 10.8028161,
        country: 'VN',
        isoCode: 'VN',
        shortAddress: 'bệnh viện Tâm Anh',
        district: 'Tân Bình',
        description: 'bệnh viện Tâm Anh',
        countryCode: '+84',
        phoneNumber: '**********',
        _id: 'x742f2f82db6f87c8930871bcd73ad2c1',
        lng: 106.6680565,
        city: 'Hồ Chí Minh',
        address:
          'Bệnh viện Tâm Anh (Khu F), Đường Phổ Quang, Phường 2, Tân Bình, Hồ Chí Minh, Việt Nam',
      },
    ],
    address:
      'bTaskee, Đường D1, Khu đô thị Him Lam, Tân Hưng, Quận 7, Hồ Chí Minh, Việt Nam',
    cities: [
      {
        city: 'Hồ Chí Minh',
        country: 'VN',
      },
      {
        country: 'VN',
        city: 'Hà Nội',
      },
      {
        country: 'TH',
        city: 'Bangkok',
      },
    ],
    activatedServices: [
      {
        serviceName: 'BEAUTY_CARE',
      },
    ],
    taskNoteByServiceV3: [
      {
        serviceId: '676248cc7a5d862c74c191b9',
        note: 'Hdjdhdhdh',
      },
    ],
    avatar:
      'https://toanphambucket.s3.amazonaws.com/mystore%2Fasker-x5956c8b1f6f6638252c7330af9cdbe79-ublkq',
  },
  setTimezone: (timezone: string) => set({ timezone: timezone }),
  setAddress: (address: IAddress) => set({ address: address }),
  setDuration: (duration: number) => set({ duration: duration }),
  setDateTime: (date: IDate) => set({ date: date }),
  setNote: (note: string) => set({ note: note }),
  setIsApplyNoteForAllTask: (isApplyNoteForAllTask: boolean) =>
    set({ isApplyNoteForAllTask: isApplyNoteForAllTask }),
  setPrice: (price: IPrice | null) => set({ price: price }),
  setCurrency: (currency: { sign: string; code: string }) =>
    set({ currency: currency }),
  setHomeNumber: (homeNumber: string) => set({ homeNumber: homeNumber }),
  setPaymentMethod: (paymentMethod: any) =>
    set({ paymentMethod: paymentMethod }),
  setPromotion: (promotion: any) => set({ promotion: promotion }),
  setLoadingPrice: (loadingPrice: boolean) =>
    set({ loadingPrice: loadingPrice }),
  setLoadingPostTask: (loadingPostTask: boolean) =>
    set({ loadingPostTask: loadingPostTask }),
  setRelatedTask: (relatedTask: IRelatedTask) =>
    set({ relatedTask: relatedTask }),
  setUser: (user: IUser) => set({ user: user }),
  setSelectedAirConditioner: (selectedAirConditioner: ISelectedAC[]) =>
    set({ selectedAirConditioner: selectedAirConditioner }),
}));
