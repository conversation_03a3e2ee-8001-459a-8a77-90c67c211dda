import { DateTimeHelpers, IDate } from '@btaskee/design-system';
import { useACStore } from '@store';
import { IDataACRender } from '@types';
import { cloneDeep } from 'lodash-es';

import { REFILL_GAS_AIR_CONDITIONER } from '../lib/constant';
import { usePostTask } from './usePostTask';

const sumArray = (arrayNumber: number[]) => {
  return arrayNumber.reduce(function (a, b) {
    return a + b;
  }, 0);
};
const calculateACDurationByQuantity = (acData) => {
  const acQuantity = acData.map((item) => item.quantity);
  return Math.ceil((sumArray(acQuantity) * 45) / 60);
};

export const useChangeData = () => {
  const { setDateTime, setPrice, setDuration, setSelectedAirConditioner } =
    useACStore();
  const { getPrice } = usePostTask();

  const onChangeDateTime = (newDate: IDate) => {
    const currentState = useACStore.getState();
    const { address, date } = currentState;

    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);
    const isSame = DateTimeHelpers.checkIsSame({
      firstDate: date,
      secondDate: newDate,
      timezone,
    });
    // check spam, call api with same data
    if (isSame) return null;
    // set new date time
    setDateTime(newDate);
    // get price again
    getPrice();
  };

  const addOnServiceGas = async ({
    dataService,
    quantity = 0,
  }: {
    dataService: IDataACRender;
    quantity: number;
  }) => {
    const currentState = useACStore.getState();
    const { selectedAirConditioner } = currentState;
    const dataAC = cloneDeep(selectedAirConditioner);
    // check exist
    const ACIndex = dataAC.findIndex(
      (ac) =>
        ac?.type?.name === dataService.type?.name &&
        ac?.hp?.to === dataService?.hp?.to &&
        ac?.hp?.from === dataService?.hp?.from,
    );
    // not found AC
    if (ACIndex === -1) {
      return;
    }
    // ok, options
    // add gas
    if (quantity) {
      // only get cleaning and 1 service gas or gas inverter
      dataAC[ACIndex].options = [{ ...dataService?.option, quantity }];
    } else {
      const { options, ...newAC } = dataAC?.[ACIndex];
      // remove gas
      dataAC[ACIndex] = newAC;
    }
    // update all services AC
    await setSelectedAirConditioner(dataAC);
    return getPrice();
  };

  const addService = async ({
    dataService,
    quantity = 0,
  }: {
    dataService: IDataACRender;
    quantity: number;
  }) => {
    const currentState = useACStore.getState();
    const { selectedAirConditioner, date } = currentState;
    const dataAC = cloneDeep(selectedAirConditioner);

    // check exist
    const ACIndex = dataAC.findIndex(
      (ac) =>
        ac?.type?.name === dataService.type.name &&
        ac?.hp?.to === dataService.hp.to &&
        ac?.hp?.from === dataService.hp.from,
    );
    // exist, current quantity is 1 and click button minus, remove AC
    if (ACIndex > -1 && dataAC[ACIndex].quantity === 1 && quantity === -1) {
      dataAC.splice(ACIndex, 1);

      // reset price when remove all AC
      if (dataAC.length === 0) {
        await setPrice(null);
      }
    } else if (ACIndex > -1) {
      // exist, update quantity AC
      dataAC[ACIndex].quantity = dataAC[ACIndex].quantity + quantity;
      // Tim vi tri refill gas
      const refillGasIndex = dataAC?.[ACIndex]?.options?.findIndex(
        (item) => item.name === REFILL_GAS_AIR_CONDITIONER,
      );
      // Neu so lượng máy nhiều hơn số lượng máy đã chọn bơm gas thì set máy đã chọn bơm gas bằng với số lượng máy
      if (
        (dataAC?.[ACIndex].quantity || 0) <=
        (dataAC?.[ACIndex]?.options?.[refillGasIndex]?.quantity || 0)
      ) {
        dataAC[ACIndex].options[refillGasIndex] = {
          ...dataAC[ACIndex]?.options[refillGasIndex],
          quantity: dataAC[ACIndex].quantity,
        };
      }
    } else if (ACIndex === -1) {
      // not exist, add AC
      dataAC.push({ ...dataService, quantity: 1 });
    }

    // set duration for AC
    const duration = calculateACDurationByQuantity(dataAC);
    await setDuration(duration);

    // update all services AC
    await setSelectedAirConditioner(dataAC);

    return getPrice();
  };

  return {
    onChangeDateTime,
    addService,
    addOnServiceGas,
  };
};
