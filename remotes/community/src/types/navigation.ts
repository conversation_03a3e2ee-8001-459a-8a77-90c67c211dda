import { ILocation } from '@btaskee/design-system';
import { NativeStackScreenProps } from '@react-navigation/native-stack';

export type RootStackParamList = {
  ChooseAddress: undefined;
  ConfirmBooking: undefined;
  ChooseDuration: undefined;
  ChooseDateTime?: any;
  EditLocation: {
    location: ILocation;
  };
  AddNewLocation: undefined;
  PostTaskSuccess: undefined;
};

export type RootStackScreenProps<T extends keyof RootStackParamList> =
  NativeStackScreenProps<RootStackParamList, T>;
