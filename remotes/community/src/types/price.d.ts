import { IDate, ITaskPlace } from '@btaskee/design-system';
import { IUser } from '@types/user';

import { IAddons, ISelectedAC } from './service';

export type IParamsGetPrice = {
  task: {
    timezone?: string;
    date: string;
    autoChooseTasker: boolean;
    taskPlace: ITaskPlace;
    homeType?: string;
    duration: number;
    forceTasker?: IUser;
    dateOptions?: IDate[];
    payment?: {
      method: string;
    };
    requirements?: { type: number }[];
    addons?: IAddons[];
    isPremium?: boolean;
    detailAirConditioner?: ISelectedAC[];
  };
  service: {
    _id: string;
  };
  isoCode: string;
};
