import { ApiResultStatus, IRespond } from '@btaskee/design-system';
import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';

const ACCESS_KEY =
  '1JtpyvG4nr7gJfTkWl57JrdwN5RGuunhHGnWEpTPVofIzyqgXmPCus3IdgfpZGan';
const API_URL = 'https://api-testing.beeehive.id.vn';

type FetchOptions = {
  method?: string;
  headers?: Record<string, string>;
  data?: any;
  token?: string;
};

const log = (...args: any) => {
  if (__DEV__) {
    console.log(...args);
  }
};

export const fetchAPI = async <T>(
  url: string,
  options: FetchOptions = {},
): Promise<IRespond<T>> => {
  const { method = 'POST', headers = {}, data, token } = options;

  const config: AxiosRequestConfig = {
    url: `${API_URL}/api/${url}`,
    method,
    headers: {
      'Content-Type': 'application/json',
      ...headers,
      accessKey: ACCESS_KEY,
    },
    data,
  };

  if (token) {
    config.headers = {
      'Content-Type': 'application/json',
      ...headers,
      Authorization: `Bearer ${token}`,
    };
  }

  // Log request
  log('📤 Request:', {
    url,
    method,
    headers: config.headers,
    data: data ? JSON.stringify(data, null, 2) : undefined,
  });

  try {
    const response: AxiosResponse<T> = await axios(config);

    // Log response
    log('📥 Response:', {
      url,
      status: response.status,
      headers: response.headers,
      data: response?.data || null,
    });

    return {
      status: ApiResultStatus.SUCCESS,
      isSuccess: true,
      data: response.data,
    };
  } catch (error: any) {
    log('📥 Error:', {
      url,
      status: error?.response?.status,
      headers: error?.response?.headers,
      data: error?.response?.data || null,
    });
    return {
      status: ApiResultStatus.ERROR,
      isSuccess: false,
      error: error?.response?.data,
    };
  }
};
