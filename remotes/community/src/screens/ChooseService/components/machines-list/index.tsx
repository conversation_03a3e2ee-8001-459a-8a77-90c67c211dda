/**
 * @Filename: air-conditioner/layout.machines-list.js
 * @Description:
 * @CreatedAt: 13/10/2020
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @UpdatedAt: 6/1/2020
 * @UpdatedBy: Toan <PERSON>, HongKhanh
 **/

import React, { useCallback } from 'react';
import { Tab<PERSON>ar, TabView } from 'react-native-tab-view';
import {
  Colors,
  ConditionView,
  CText,
  IService,
  Spacing,
  TouchableOpacity,
} from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

import { getTypeServiceAC } from '../../../../lib/helper';
import { ISelectedAC } from '../../../../types';
import Scene from '../scene';
import styles from './styles';

interface IMachinesListProps {
  service?: IService;
  address?: any;
  selectedAirConditioner?: ISelectedAC[];
  index: number;
  setIndex: (index: number) => void;
  isScrollToBottom?: boolean;
}

const MachinesList = React.memo(
  ({
    service,
    address,
    selectedAirConditioner,
    index,
    setIndex,
    isScrollToBottom,
  }: IMachinesListProps) => {
    const serviceByCity = service?.detail?.city?.find(
      (e) => e.name === address?.city,
    );

    const routes = getTypeServiceAC(serviceByCity?.type);
    // Nếu chỉ có 1 loại máy
    const isOnlyOneType = Boolean(routes.length === 1);

    const renderScene = ({ route }: { route: any }) => {
      return (
        <Scene
          route={route}
          index={index}
          selectedAirConditioner={selectedAirConditioner}
          serviceByCity={serviceByCity}
          maximumPSI={service?.maximumPSI}
          isScrollToBottom={isScrollToBottom}
        />
      );
    };

    const renderTabBarItem = useCallback(
      ({ route, props }: { route: any; props: any }) => {
        // Check if the current tab is focused
        const focused =
          props.navigationState.index ===
          props.navigationState.routes.findIndex(
            (r: any) => r.key === route.key,
          );

        return (
          <TouchableOpacity
            onPress={() => props.jumpTo(route.key)}
            style={[styles.tabStyle, focused && styles.tabStyleActive]}
          >
            <CText style={[styles.label, focused && styles.activeLabel]}>
              {route.title}
            </CText>
          </TouchableOpacity>
        );
      },
      [],
    );

    if (isEmpty(serviceByCity)) {
      return null;
    }

    return (
      <TabView
        swipeEnabled={false}
        navigationState={{ index, routes }}
        renderScene={renderScene}
        onIndexChange={setIndex}
        style={{
          marginTop: isOnlyOneType ? Spacing.SPACE_24 : Spacing.SPACE_0,
        }}
        renderTabBar={(props) => (
          // Nếu chỉ có 1 loại thì k cần hiển thị tabBar
          <ConditionView
            condition={!isOnlyOneType}
            viewTrue={
              <TabBar
                {...props}
                indicatorStyle={styles.backgroundWhite}
                activeColor={Colors.PRIMARY_COLOR}
                tabStyle={styles.tabStyleTabBar}
                style={styles.tabBarStyle}
                inactiveColor={Colors.BLACK}
                contentContainerStyle={styles.contentContainer}
                renderTabBarItem={({ route }) =>
                  renderTabBarItem({ route, props })
                }
              />
            }
          />
        )}
      />
    );
  },
);
export default MachinesList;
