import { Dimensions, StyleSheet } from 'react-native';
import { Colors, FontSizes, Spacing } from '@btaskee/design-system';

const { width } = Dimensions.get('window');
const HEIGHT_FLAG = Math.round(width / 2);

export default StyleSheet.create({
  btnInfo: {
    paddingLeft: Spacing.SPACE_04,
  },
  content: {
    paddingHorizontal: Spacing.SPACE_16,
  },
  left: {
    flex: 1,
    alignItems: 'center',
  },
  group: {
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: Spacing.SPACE_08,
    borderColor: Colors.BORDER_COLOR,
    padding: Spacing.SPACE_08,
    marginTop: Spacing.SPACE_08,
  },
  container: {
    marginTop: '2%',
  },
  txtPanel: {
    marginTop: Spacing.SPACE_16,
    marginBottom: Spacing.SPACE_24,
    fontSize: FontSizes.SIZE_18,
  },
  iconImage: {
    width: 30,
    height: 30,
  },
  txtLabel: {
    fontWeight: 'bold',
    marginLeft: Spacing.SPACE_12,
  },
  blockImage: {
    position: 'absolute',
    top: -HEIGHT_FLAG,
    marginHorizontal: -Spacing.SPACE_16,
    marginBottom: Spacing.SPACE_16,
  },
  blockIcon: {
    marginHorizontal: Spacing.SPACE_04,
    marginBottom: Spacing.SPACE_16,
    flex: 1,
  },
  ImageDescription: {
    width: width,
    height: HEIGHT_FLAG,
  },
  lineInfo: {
    justifyContent: 'center',
    alignItems: 'stretch',
    flexDirection: 'row',
  },
  iconStar: {
    width: Spacing.SPACE_20,
    height: Spacing.SPACE_20,
  },
  contentPremiumStyle: {
    // marginBottom: constant.MARGIN.large,
  },
  containerModal: {
    margin: 0,
    padding: 0,
  },
  scrollArea: {
    paddingBottom: Spacing.SPACE_24,
    marginBottom: Spacing.SPACE_24,
  },
  blockCancel: {
    position: 'absolute',
    top: 15,
    right: 15,
  },
  iconCancel: {
    width: Spacing.SPACE_24,
    height: Spacing.SPACE_24,
  },
  iconMoreInformation: {
    width: Spacing.SPACE_16,
    height: Spacing.SPACE_16,
  },
  titleStyle: {
    textAlign: 'center',
    fontSize: FontSizes.SIZE_18,
    paddingHorizontal: Spacing.SPACE_16,
  },
  textStyle: {
    textAlign: 'center',
  },
  boxNote: {
    marginVertical: Spacing.SPACE_16,
    padding: Spacing.SPACE_16,
    backgroundColor: Colors.YELLOW_1,
    borderRadius: Spacing.SPACE_08,
    borderColor: Colors.ORANGE,
  },
});
