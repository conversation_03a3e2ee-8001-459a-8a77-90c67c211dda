import { StyleSheet } from 'react-native';
import { Colors, DeviceHelper, Spacing } from '@btaskee/design-system';

export default StyleSheet.create({
  boxNoteBuiltIn: {
    margin: Spacing.SPACE_16,
    backgroundColor: Colors.LIGHT_ORANGE_4,
    paddingVertical: Spacing.SPACE_08,
    paddingHorizontal: Spacing.SPACE_16,
    borderRadius: 12,
  },
  containerScroll: {
    flexGrow: 1,
    paddingBottom: '40%',
    backgroundColor: Colors.WHITE,
  },
  headerImage: {
    width: DeviceHelper.WINDOW.WIDTH,
    height: DeviceHelper.WINDOW.WIDTH / 2,
    // marginTop: normalize(NORMALIZE_SIZE.SIZE_16),
  },
  wrapTypeAC: {
    paddingHorizontal: Spacing.SPACE_16,
  },
  txtNoteBuiltIn: {
    color: Colors.PRIMARY_COLOR,
    marginLeft: Spacing.SPACE_08,
  },
  imgIconNote: {
    width: 32,
    height: 32,
  },
});
