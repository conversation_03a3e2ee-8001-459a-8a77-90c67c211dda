/**
 * @Filename: air-conditioner/layout/index.js
 * @Description:
 * @CreatedAt:  14/10/2020
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @UpdatedAt: 8/12/2020
 * @UpdatedBy: DucAnh
 **/

import React from 'react';
import { TouchableWithoutFeedback } from 'react-native';
import {
  AlertHolder,
  AnimationHelpers,
  BlockView,
  checkSupportCity,
  CText,
  HomeType,
  IService,
  NotSupportCity,
  PriceButton,
  useI18n,
} from '@btaskee/design-system';
import { useACStore } from '@store';
import { cloneDeep, isEmpty } from 'lodash-es';

import { useAppNavigation, usePostTask } from '@hooks';
import { RouteName } from '@navigation/RouteName';

import { HeaderAC } from '../../components/header';
import { Recap } from '../../components/recap';
import { IAddons, IDataACRender, IParamsRefillGas } from '../../types';
import { ApartmentOptional } from './components/apartment-option';
import MachinesList from './components/machines-list';
import styles from './styles';

export type IAirConditionerProps = {
  addService?: ({
    dataService,
    quantity,
  }: {
    dataService: IDataACRender;
    quantity: number;
  }) => void;
  resetState?: () => void;
  service?: IService;
  addOnServiceGas?: (dataGas: IParamsRefillGas, quantity?: number) => void;
  previousServiceId?: IService['_id'];
  // setStepPostTask?: (step: TRACKING_STEP) => void;
};
export const ChooseService = ({
  resetState,
  addOnServiceGas,
  previousServiceId,
}: // setStepPostTask,
IAirConditionerProps) => {
  const { t } = useI18n('airConditioner');
  const { t: tCommon } = useI18n('common');
  const {
    address,
    homeType,
    addons,
    service,
    setAddons,
    selectedAirConditioner,
    price,
  } = useACStore();
  const { getPrice } = usePostTask();

  const navigation = useAppNavigation();
  // const isFocused = useIsFocused();

  const [isShow, setIsShow] = React.useState(false);
  const [index, setIndex] = React.useState(0);
  const [isScrollToBottom, setIsScrollToBottom] = React.useState(false);

  const addonsApartmentByService = service?.addons?.find(
    (e: IAddons) => e?.name === HomeType.Apartment,
  );

  // const handleActionStep2 = useCallback(
  //   (action: TRACKING_ACTION) => {
  //     if (!isFocused) {
  //       return null;
  //     }
  //     const selectedClone = cloneDeep(selectedAirConditioner);
  //     trackingServiceClick({
  //       screenName: TrackingScreenNames.DetailInformation,
  //       serviceName: service?.name,
  //       action,
  //       isTetBooking: service?.isTet,
  //       additionalInfo: {
  //         devices: selectedClone?.map((item: ISelectedAC) => {
  //           return {
  //             hp: item?.hp,
  //             quantity: item?.quantity,
  //             type: item?.type?.name,
  //             options: item?.options?.map((option: any) => {
  //               return {
  //                 name: option?.name,
  //                 quantity: option?.quantity,
  //               };
  //             }),
  //           };
  //         }),
  //       },
  //     });
  //   },
  //   [selectedAirConditioner, service?.name, service?.isTet, isFocused],
  // );

  // useEffect(() => {
  //   const unsubscribe = navigation.addListener('beforeRemove', () => {
  //     handleActionStep2(TRACKING_ACTION.Back);
  //   });

  //   return unsubscribe; // Cleanup listener khi component unmount
  // }, [navigation, handleActionStep2]);

  const changeApartmentOption = async (isChooseApartment: boolean) => {
    const addonsService = service?.addons;
    const addonsPostTask = cloneDeep(addons) || [];
    const addonsApartment = addonsPostTask?.find(
      (e) => e?.name === HomeType.Apartment,
    );

    // add addons for apartment
    if (isChooseApartment && !addonsApartment) {
      const addonsApartmentService = addonsService?.find(
        (e) => e?.name === HomeType.Apartment,
      );
      !isEmpty(addonsApartmentService) &&
        addonsPostTask.push(addonsApartmentService);
    }

    // remove addons for apartment
    if (!isChooseApartment && addonsApartment) {
      addonsPostTask.splice(addonsPostTask.indexOf(addonsApartment), 1);
    }

    // set addons and get price
    await setAddons(addonsPostTask);
    return await getPrice?.(service?.name);
  };

  React.useEffect(() => {
    // Did mount here
    // Check selected service difference with previous service: reset states before begining
    // The same service: keep state for user continue the booking
    if (previousServiceId && previousServiceId !== service?._id) {
      resetState?.();
    }
    navigation.setOptions({
      headerShown: false,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  React.useEffect(() => {
    if (homeType === HomeType.Apartment && addonsApartmentByService) {
      changeApartmentOption(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [homeType]);

  const handlePress = () => {
    // trackingServiceClick({
    //   screenName: TrackingScreenNames.DetailInformationProcessIntro,
    //   serviceName: service?.name,
    //   action: !isShow ? TRACKING_ACTION.Open : TRACKING_ACTION.Close,
    //   isTetBooking: service?.isTet,
    // });
    AnimationHelpers.runLayoutAnimation();
    setIsShow(!isShow);
  };

  const _getPrice = () => {
    getPrice?.(service?.name);
  };

  const checkApartment = () => {
    // handleActionStep2(TRACKING_ACTION.Next);
    const addonsApartment = addons?.find(
      (e: any) => e?.name === HomeType.Apartment,
    );

    // Chỉ hiển thị thông thông báo chọn căn hộ khi trong service có addons Apartment
    if (!addonsApartment && addonsApartmentByService) {
      return AlertHolder.alert.open({
        title: t('APARTMENT_TITLE'),
        message: <ApartmentOptional />,
        actions: [
          {
            text: tCommon('BTN_BACK'),
            onPress: () => AlertHolder.alert.close(),
            style: 'cancel',
          },
          {
            text: tCommon('CONFIRM'),
            onPress: () => _onConfirmed(),
          },
        ],
      });
    }
    _onConfirmed();
  };

  const _onConfirmed = () => {
    isShow && handlePress();
    navigation.navigate(RouteName.ChooseDateTime);
  };

  const handleTapTypeAC = (number: number) => {
    setIndex(number);
    setIsScrollToBottom(false);
  };

  // Check support city
  if (!checkSupportCity(service?.city, address?.city)) {
    return <NotSupportCity />;
  }

  return (
    <BlockView style={styles.container}>
      <BlockView
        flex
        style={styles.content}
      >
        <HeaderAC />
        <CText
          bold
          style={styles.txtChooseType}
        >
          {t('POST_TASK_AC_CHOOSE_TYPE')}
        </CText>
        <MachinesList
          address={address}
          service={service}
          selectedAirConditioner={selectedAirConditioner}
          index={index}
          setIndex={handleTapTypeAC}
          isScrollToBottom={isScrollToBottom}
        />
        <BlockView
          absolute={isShow}
          style={styles.wrapper}
          backgroundColor="rgba(0,0,0,0.65)"
          zIndex={4}
        >
          <TouchableWithoutFeedback onPress={handlePress}>
            <BlockView
              flex
              zIndex={2}
            />
          </TouchableWithoutFeedback>
          <BlockView zIndex={2}>
            <PriceButton
              pricePostTask={price}
              testID={'btnNextStep2'}
              onPress={checkApartment}
              HeaderComponent={
                <Recap
                  onPress={handlePress}
                  isShow={isShow}
                  setIndex={setIndex}
                  hideModal={handlePress}
                  scrollToBottom={setIsScrollToBottom}
                />
              }
            />
          </BlockView>
        </BlockView>
      </BlockView>
    </BlockView>
  );
};
