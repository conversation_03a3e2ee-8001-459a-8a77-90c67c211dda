/**
 * @Filename: post-task-step-3/layout/index.js
 * @Description:
 * @CreatedAt: 16/9/2020
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @UpdatedAt: 25/1/2021
 * @UpdatedBy: <PERSON><PERSON>, <PERSON><PERSON>, HongKhanh
 **/

import React from 'react';
import { ScrollView, View } from 'react-native';
import {
  BlockView,
  CText,
  FastImage,
  Icon,
  IconProps,
  Spacing,
  TouchableOpacity,
  useI18n,
} from '@btaskee/design-system';

// import { useACStore } from '@store';
// import { get } from 'lodash-es';
import { useAppNavigation } from '@hooks';
import { introHeader } from '@images';
import { RouteName } from '@navigation/RouteName';

import styles from './styles';
// import { trackingServiceView } from '@tracking/index';
// import { TrackingScreenNames } from '@tracking/types';
type IAddress = {
  lat: number;
  lng: number;
  country: string;
  city: string;
  district: string;
};
const IntroItem = ({
  icon,
  title,
  value,
}: {
  icon: IconProps['name'];
  title: string;
  value: string;
}) => {
  const { t } = useI18n('airConditioner');

  return (
    <BlockView style={styles.wrapItemNote}>
      <Icon
        style={styles.imageIcon}
        resizeMode={'cover'}
        name={icon}
        size={Spacing.SPACE_40}
      />

      <BlockView style={styles.wrapTxtNote}>
        <CText
          bold
          style={styles.txt_note}
        >
          {title}
        </CText>
        <CText style={styles.txtValue}>{t(value)}</CText>
      </BlockView>
    </BlockView>
  );
};

export const IntroService = (props) => {
  const { t } = useI18n('airConditioner');
  const navigation = useAppNavigation();
  // const { setAddress, user, setHomeNumber } = useACStore();

  // const { setFirstOpenAirConditioner, setHomeType } = props;

  // const entryPoint = useAppSelector(PostTaskSelector.entryPoint);
  // const entryPointRoute = route?.params?.entryPoint;

  // React.useEffect(() => {
  //   trackingServiceView({
  //     screenName: TrackingScreenNames.ServiceIntroduction,
  //     serviceName: SERVICES.AIR_CONDITIONER,
  //     entryPoint: entryPointRoute || entryPoint,
  //   });
  // }, [entryPointRoute, entryPoint]);

  // const setAddressToReducer = async (address: IAddress = {}) => {
  //   await setAddress({
  //     lat: address.lat,
  //     lng: address.lng,
  //     country: address.country,
  //     city: address.city,
  //     district: address.district,
  //     address: address.address,
  //     contact: address.contact,
  //     phoneNumber: address.phoneNumber,
  //     shortAddress: address.shortAddress,
  //     countryCode: address.countryCode,
  //     isAddressMaybeWrong: Boolean(address?.isAddressMaybeWrong),
  //   });
  //   await setHomeType(address.homeType);
  //   await setHomeNumber(address.description);
  //   return null;
  // };

  const onSubmit = async () => {
    // Set first open, and no show this intro again
    // setFirstOpenAirConditioner(false);

    // set address default
    // get first address
    // let addressList = get(user, 'locations', []) || [];
    // console.log('🚀 ~ onSubmit ~ addressList:', addressList);
    // addressList = addressList.filter((e) => {
    //   return e.isoCode === user?.isoCode;
    // });
    // // for old user, user already has an address
    // // go to post task step 2

    // if (addressList && addressList.length > 0) {
    //   // use default address
    //   let address = addressList.find((item) => item?.isDefault === true);
    //   if (!address) {
    //     address = addressList[0];
    //   }
    //   // await setAddressToReducer(address);
    //   return navigation.replace(RouteName.ChooseAddress, {
    //     // entryPoint: TrackingScreenNames.ServiceIntroduction,
    //   });
    // }
    // console.log('🚀 ~ onSubmit ~ addressList:', addressList);

    return navigation.navigate(RouteName.ChooseAddress, {
      // entryPoint: TrackingScreenNames.ServiceIntroduction,
    });
    // for new user, user does not have an address
    // not address history, go to Map to select location
    // navigation.replace(RouteName.Map, {
    //   redirectedFrom: RouteName.PostTaskStep2,
    //   // paramsNavigate: { entryPoint: TrackingScreenNames.ServiceIntroduction },
    //   callback: (address) => {
    //     setAddressToReducer(address);
    //   },
    // });
  };

  return (
    <BlockView
      inset={'bottom'}
      style={styles.container}
    >
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.contentContainerStyle}
      >
        <BlockView style={styles.wrap_image}>
          <FastImage
            style={styles.imageStyle}
            // resizeMode={'cover'}
            source={introHeader}
          />
        </BlockView>
        <CText
          bold
          style={styles.txt_serviceName}
        >
          {t('AC_INTRO_TITLE')}
        </CText>

        <BlockView row>
          <Icon
            name="icStar"
            style={styles.iconStar}
            size={Spacing.SPACE_20}
          />
          <BlockView flex>
            <CText style={styles.txt_note}>{t('AC_INTRO_CONTENT')}</CText>
          </BlockView>
        </BlockView>

        <BlockView style={styles.wrapBottom}>
          <IntroItem
            icon={'icIntroStar'}
            title={t('AC_INTRO_PRO_TITLE')}
            value={t('AC_INTRO_PRO_CONTENT')}
          />
          <IntroItem
            icon={'icIntroSafe'}
            title={t('AC_INTRO_SAFE_TITLE')}
            value={t('AC_INTRO_SAFE_CONTENT')}
          />
          <IntroItem
            icon={'icIntroPhone'}
            title={t('AC_INTRO_READY_TITLE')}
            value={t('AC_INTRO_READY_CONTENT')}
          />
          <IntroItem
            icon={'icIntroHeart'}
            title={t('AC_INTRO_PRICE_TITLE')}
            value={t('AC_INTRO_PRICE_CONTENT')}
          />
        </BlockView>
      </ScrollView>

      <TouchableOpacity
        style={styles.wrap_bottom}
        onPress={() => onSubmit()}
      >
        <CText
          bold
          style={styles.txtSubmit}
        >
          {t('INTRO_START_EXPERIENCE')}
        </CText>
      </TouchableOpacity>
    </BlockView>
  );
};
