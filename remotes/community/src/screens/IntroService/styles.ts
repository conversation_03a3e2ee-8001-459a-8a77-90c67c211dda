import { Dimensions, StyleSheet } from 'react-native';
import {
  BorderRadius,
  Colors,
  FontSizes,
  Spacing,
} from '@btaskee/design-system';

const { width } = Dimensions.get('window');

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.WHITE,
  },
  containerTop: {},
  imageStyle: {
    height: width * 0.75,
    width: width,
  },
  wrap_image: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  txt_serviceName: {
    marginTop: Spacing.SPACE_24,
    fontSize: FontSizes.SIZE_16,
    textAlign: 'center',
    marginBottom: 14,
  },
  txt_note: {
    color: Colors.BLACK,
    lineHeight: Spacing.SPACE_24,
  },
  txt_QA: {
    color: Colors.SECONDARY_COLOR,
  },
  txt_title: {
    color: Colors.BLACK_2,
    fontSize: FontSizes.SIZE_14,
  },
  wrap_note: {
    marginTop: Spacing.SPACE_08,
  },
  wrapTxtNote: {
    flex: 1,
  },
  wrapItemNote: {
    flexDirection: 'row',
    marginTop: Spacing.SPACE_16,
  },
  wrapBottom: {
    marginTop: Spacing.SPACE_08,
  },
  titleBtn: {
    fontSize: FontSizes.SIZE_14,
    fontWeight: '600', // android
    fontFamily: 'Montserrat-Bold', // ios
  },
  wrap_bottom: {
    padding: Spacing.SPACE_16,
    margin: Spacing.SPACE_16,
    borderTopWidth: 1,
    borderTopColor: Colors.BORDER_COLOR,
    backgroundColor: Colors.SECONDARY_COLOR,
    borderRadius: BorderRadius.RADIUS_08,
    justifyContent: 'center',
    alignItems: 'center',
  },
  wrap_QA: {
    backgroundColor: Colors.LIGHT_GREY_2,
    padding: Spacing.SPACE_12,
    borderRadius: BorderRadius.RADIUS_08,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  wrap_QABottom: {
    backgroundColor: Colors.LIGHT_GREY_2,
    padding: Spacing.SPACE_12,
    borderRadius: BorderRadius.RADIUS_08,
    marginTop: Spacing.SPACE_08,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  wrap_icon: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingLeft: Spacing.SPACE_04,
  },
  txtSubmit: {
    color: Colors.WHITE,
  },
  imageIcon: {
    marginRight: Spacing.SPACE_16,
  },
  txtValue: {
    color: Colors.BLACK,
    lineHeight: Spacing.SPACE_24,
    marginTop: 2,
  },
  iconStar: {
    marginTop: 5,
    marginRight: 10,
  },
  contentContainerStyle: {
    paddingHorizontal: Spacing.SPACE_16,
    paddingBottom: '20%',
  },
});
