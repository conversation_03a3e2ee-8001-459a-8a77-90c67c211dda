import { StyleSheet } from 'react-native';
import { Colors, FontSizes, Spacing } from '@btaskee/design-system';

export default StyleSheet.create({
  panel: {
    marginTop: Spacing.SPACE_32,
    marginBottom: Spacing.SPACE_16,
    justifyContent: 'space-between',
  },
  txtPanel: {
    fontSize: FontSizes.SIZE_16,
  },
  subPanel: {
    marginBottom: 10,
    color: Colors.BLACK,
  },
  txtVLabel: {
    width: '35%',
    paddingRight: 10,
    color: Colors.GREY,
  },
  txtValue: {
    width: '65%',
    textAlign: 'right',
  },
  group: {
    flexDirection: 'row',
    paddingVertical: 5,
    // alignItems: 'center',
  },
  wrapDetail: {
    marginTop: Spacing.SPACE_08,
  },
});
