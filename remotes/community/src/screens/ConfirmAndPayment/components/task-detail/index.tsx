/**
 * @Filename: task-detail/index.js
 * @Description:
 * @CreatedAt:  18/9/2020
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @UpdatedAt: 1/12/2020
 * @UpdatedBy: HongKhan<PERSON>, <PERSON><PERSON>
 **/

import React from 'react';
import {
  BlockView,
  Card,
  Colors,
  CText,
  Spacing,
  useI18n,
} from '@btaskee/design-system';
import { useACStore } from '@store';

import DetailACService from '../air-conditioner-service';
import WorkingTime from '../working-time';
import styles from './styles';

const TaskDetail = () => {
  const { t } = useI18n('common');

  const currentState = useACStore.getState();
  const { note, date, duration, timezone } = currentState;

  const shouldRenderNote = React.useMemo(() => {
    if (!note) {
      return null;
    }
    return (
      <BlockView
        row
        margin={{ top: Spacing.SPACE_08 }}
        padding={{ top: Spacing.SPACE_08 }}
        border={{ top: { width: 1, color: Colors.BORDER_LIGHT_GRAY } }}
      >
        <CText style={styles.txtVLabel}>{t('LABEL_NOTE_FOR_TASKER')}</CText>
        <CText
          testID={'taskNote'}
          style={styles.txtValue}
        >
          {note}
        </CText>
      </BlockView>
    );
  }, [note, t]);

  return (
    <BlockView>
      <BlockView style={styles.panel}>
        <CText
          bold
          style={styles.txtPanel}
        >
          {t('TASK_INFO')}
        </CText>
      </BlockView>
      <Card style={{ marginTop: Spacing.SPACE_16 }}>
        <WorkingTime
          date={date}
          duration={duration}
          timezone={timezone}
        />
        <BlockView style={styles.wrapDetail}>
          <CText
            bold
            style={styles.subPanel}
          >
            {t('TASK_DETAIL')}
          </CText>
          <DetailACService />
          {shouldRenderNote}
        </BlockView>
      </Card>
    </BlockView>
  );
};

export default TaskDetail;
