/**
 * @Filename: post-task-step-4/layout/price.js
 * @Description:
 * @CreatedAt: 18/9/2020
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @UpdatedAt: 3/11/2020
 * @UpdatedBy: HongKhanh, <PERSON><PERSON>
 **/

import React from 'react';

import { BlockView, CText } from '@component';
import { formatMoney, getCurrency } from '@helper';
import { useAppSelector } from '@hooks/app-redux';
import { useTranslations } from '@hooks/useTranslations';
import { PostTaskSelector } from '@reducer/post-task/post-task.selector';

import styles from './styles';

const Price = () => {
  const { t } = useTranslations();
  const price = useAppSelector(PostTaskSelector.price);

  let originPriceText = '';
  const priceText = `${t('COST_AND_CURRENCY', {
    cost: formatMoney(price.finalCost),
    currency: getCurrency(price, 1),
  })}`;
  // with promotion
  if (price && price.cost > price.finalCost) {
    originPriceText = `${t('COST_AND_CURRENCY', {
      cost: formatMoney(price.cost),
      currency: getCurrency(price, 1),
    })}`;
  }

  if (!price) {
    return null;
  }

  return (
    <BlockView row style={styles.pricePanel}>
      <BlockView>
        <CText bold h4 style={styles.txtTotal}>
          {t('TOTAL')}
        </CText>
      </BlockView>
      <BlockView>
        {originPriceText ? (
          <CText testID="originPrice" style={styles.txtPromotion}>
            {originPriceText}
          </CText>
        ) : null}
        <CText testID={'price'} bold h4 style={styles.txtPrice}>
          {priceText}
        </CText>
      </BlockView>
    </BlockView>
  );
};

export default Price;
