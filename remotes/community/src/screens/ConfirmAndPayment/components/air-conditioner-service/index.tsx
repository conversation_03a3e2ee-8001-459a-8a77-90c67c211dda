import React from 'react';
import {
  BlockView,
  Colors,
  ConditionView,
  CText,
  formatMoney,
  getTextWithLocale,
  SizedBox,
  Spacing,
  useI18n,
} from '@btaskee/design-system';
import { useACStore } from '@store';
import { IDetailAC } from '@types';
import { isEmpty } from 'lodash-es';

import { getUnitACByIsoCode } from '../../../../lib/helper';
import styles from './styles';

const UNIT = 1;

const DetailACService = () => {
  const { t } = useI18n('common');
  const currentState = useACStore.getState();
  const { isoCode, selectedAirConditioner } = currentState;

  const renderServicesAC = (
    optionAC?: IDetailAC['options'],
    quantity?: number,
    title?: string,
    isLast?: boolean,
  ) => {
    if (!quantity) {
      return null;
    }

    return (
      <BlockView>
        <BlockView style={styles.group}>
          <CText style={styles.txtVLabel}>{t('AMOUNT')}</CText>
          <CText
            testID={`${title}Services`}
            style={styles.txtValue}
          >
            {quantity}
          </CText>
        </BlockView>
        <ConditionView
          condition={isEmpty(optionAC)}
          viewFalse={
            <BlockView style={styles.group}>
              <CText style={styles.txtVLabel}>
                {t('AIR_CONDITIONER.GAS_REFILL')}
              </CText>
              <CText
                testID={`${title}GasServices`}
                style={styles.txtValue}
              >
                {optionAC?.[0]?.quantity}
              </CText>
            </BlockView>
          }
        />
        <ConditionView
          condition={!isLast}
          viewTrue={
            <SizedBox
              height={1}
              color={Colors.BORDER_LIGHT_GRAY}
              margin={{
                top: Spacing.SPACE_04,
                bottom: Spacing.SPACE_08,
              }}
            />
          }
        />
      </BlockView>
    );
  };

  const getTitleAC = (ac?: IDetailAC) => {
    let capacity = '';
    const title = getTextWithLocale(ac?.type?.text);
    if ((ac?.hp?.from || 0) < (ac?.hp?.to || 0)) {
      capacity = t('CAPACITY_LESS_THAN', {
        t1: ac?.hp?.to,
        t2: getUnitACByIsoCode(isoCode),
      });
    }
    if ((ac?.hp?.from || 0) > (ac?.hp?.to || 0)) {
      capacity = t('CAPACITY_GREATER_THAN', {
        t1: ac?.hp?.from,
        t2: getUnitACByIsoCode(isoCode),
      });
    }
    if (ac?.hp?.from && ac?.hp?.to) {
      capacity = `${t('POST_TASK_STEP_2.AIR_CONDITION_TITLE', {
        from: formatMoney(ac?.hp?.from || 0),
        to: formatMoney(ac?.hp?.to || 0),
        unit: getUnitACByIsoCode(isoCode),
      })}`;
    }
    return { title, capacity };
  };

  if (!selectedAirConditioner || isEmpty(selectedAirConditioner)) {
    return null;
  }

  return selectedAirConditioner.map((ac, index) => {
    const isLast = index === selectedAirConditioner.length - UNIT;
    const labelTypeAC = getTitleAC(ac);
    return (
      <BlockView key={index}>
        <BlockView
          row
          jBetween
          horizontal
        >
          <CText
            flex={1}
            bold
            testID={labelTypeAC.title || ''}
            style={styles.txtAC}
          >
            {labelTypeAC.title}
          </CText>
          <CText
            flex={2}
            right
            bold
            testID={labelTypeAC.capacity}
            color={Colors.GREY}
          >
            {labelTypeAC.capacity}
          </CText>
        </BlockView>
        {renderServicesAC(ac?.options, ac.quantity, labelTypeAC?.title, isLast)}
      </BlockView>
    );
  });
};
export default DetailACService;
