import React from 'react';
import {
  BlockView,
  CText,
  DateWithGMT,
  DurationWithGMT,
  ITimezone,
  TypeFormatDate,
  useI18n,
} from '@btaskee/design-system';

import styles from './styles';

const WorkingTime = ({
  date,
  duration,
  timezone,
}: {
  date?: string;
  duration: number;
  timezone: ITimezone;
}) => {
  const { t } = useI18n('common');

  return (
    <BlockView>
      <CText
        testID="timeToWork"
        bold
        style={styles.subPanel}
      >
        {t('TIME_TO_WORK')}
      </CText>

      <BlockView>
        <BlockView style={styles.group}>
          <CText style={styles.txtVLabel}>{t('WOKING_DAY')}</CText>
          <DateWithGMT
            testID={'workingDay'}
            timezone={timezone}
            date={date}
            typeFormat={TypeFormatDate.DateTimeFullWithDay}
            style={styles.txtValue}
          />
        </BlockView>
      </BlockView>

      <BlockView style={styles.group}>
        <CText style={styles.txtVLabel}>{t('WORK_IN')}</CText>
        <DurationWithGMT
          testID={'duration'}
          style={styles.txtValue}
          timezone={timezone}
          date={date}
          duration={duration}
        />
      </BlockView>
    </BlockView>
  );
};
export default WorkingTime;
