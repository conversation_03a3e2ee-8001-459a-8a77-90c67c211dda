/**
 * @Filename: post-task-step-4/layout/index.js
 * @Description:
 * @CreatedAt: 18/9/2020
 * @Author: Duc<PERSON>nh
 * @UpdatedAt: 1/12/2020
 * @UpdatedBy: HongKhan<PERSON>, <PERSON><PERSON>
 **/
import React, { useRef, useState } from 'react';
import { ScrollView } from 'react-native';
import {
  AlertHolder,
  BlockView,
  CModal,
  CModalHandle,
  Colors,
  CText,
  DatePicker,
  DateTimeHelpers,
  FontSizes,
  LocationPostTask,
  PaymentMethod,
  Spacing,
  TimePicker,
  TouchableOpacity,
  useI18n,
} from '@btaskee/design-system';
import { useACStore } from '@store';

import { useAppNavigation, usePostTask } from '@hooks';

import BookingButton from '../../components/booking-button';
// import PaymentMethod from './components/payment-method';
import TaskDetail from './components/task-detail';
// import { TRACKING_ACTION } from '@tracking/types';
import styles from './styles';

export const ConfirmAndPayment = (props) => {
  const { t } = useI18n('common');

  const { getPrice, postTask } = usePostTask();
  const currentState = useACStore.getState();
  const {
    setAddress,
    service,
    // setService,
    date,
    setDateTime,
    settingSystem,
    address,
    homeNumber,
    paymentMethod,
    promotion,
    setPromotion,
    price,
  } = currentState;

  const navigation = useAppNavigation();
  // const { setPaymentMethodWhenBooking } = useBookTask();
  // const { trackingNextBackActionPostTaskStep4 } = useAppTracking();

  const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);
  const [isTet, setIsTet] = useState(service?.isTet);
  const [selectedDate, setSelectedDate] = useState(date);
  const modalRef = useRef<CModalHandle | null>(null);
  // const refModalLogin = useRef<Modalize | null>(null);

  const removeTetInService = () => {
    // Clear isTet in service
    delete service.isTet;
    // setService(service);
    setIsTet(null);
    // setPaymentMethodWhenBooking();
  };

  const _changeToRegularBooking = () => {
    // Require change date if the choosen date is over regular range
    const maxDate = DateTimeHelpers.toDayTz({ timezone })
      .add(6, 'days')
      .endOf('date');
    const isAfter = DateTimeHelpers.checkIsAfter({
      timezone,
      firstDate: date,
      secondDate: maxDate,
    });
    if (isAfter) {
      // Set default date is 2PM tomorrow
      const tomorrow = DateTimeHelpers.toDayTz({ timezone })
        .add(1, 'day')
        .hour(14)
        .startOf('hours');
      setSelectedDate(tomorrow.toDate());

      // Show change new date modal
      modalRef?.current?.open && modalRef?.current?.open();
    } else {
      removeTetInService();
    }
  };

  // update date time when user change
  const onChangeDateTime = (dateObj) => {
    // check spam, call api with same data
    const isSame = DateTimeHelpers.checkIsSame({
      timezone,
      firstDate: date,
      secondDate: dateObj,
    });
    if (isSame) return null;

    setSelectedDate(dateObj);
  };

  const _changeNewDate = async () => {
    await setDateTime(selectedDate, service);
    // recaculate duration and estimated time, only Home Cooking service
    getPrice(service?.name);
    removeTetInService();
  };

  const removePromotion = async () => {
    await props.setPromotion(null);
    getPrice(service?.name);
  };

  const _resetStateAfterPTSuccess = () => {
    // resetAllState();
    // resetStateStep2();
    // resetStateStep4();
  };

  // const _openModalLogin = () => {
  //   refModalLogin?.current?.open && refModalLogin?.current?.open();
  // };

  const postTaskCallBack = () => {};

  const getOutstandingPayment = async () => {
    // Check exist userId
    // if (!global.userId) {
    //   return;
    // }
    // // Fetch the Outstanding payment debt
    // const outstandingDebt = await getOutstandingPaymentDebt({});
    // if (outstandingDebt?.isSuccess && outstandingDebt?.data?.length > 0) {
    //   // If user has any outstanding payment, redirect user to out standing payment debt page
    //   return navigation.navigate(RouteName.OutstandingPayment, {
    //     outstanding: outstandingDebt.data,
    //   });
    // }
    // Do nothing
  };
  const _onPosTask = async () => {
    // trackingNextBackActionPostTaskStep4({
    //   serviceName: SERVICES.AIR_CONDITIONER,
    //   action: TRACKING_ACTION.Next,
    // });

    const response = await postTask(postTaskCallBack);
    if (response && response?.code === 'OUTSTANDING_PAYMENT_STATUS_NEW') {
      AlertHolder?.alert?.open({
        title: t('DIALOG_TITLE_INFORMATION'),
        message: t('OUTSTANDING_PAYMENT_STATUS_NEW'),
        actions: [
          {
            text: t('PAYMENT_TOP_UP'),
            onPress: () => {
              getOutstandingPayment();
            },
          },
        ],
      });
      return;
    }
  };

  const _callback = async (userData) => {
    // Set info user address
    await setAddress({
      ...address,
      contact: userData?.name,
      phoneNumber: userData?.phone,
      countryCode: userData?.countryCode,
    });
    // post task
    await _onPosTask();

    // Reset state after post task
    // _resetStateAfterPTSuccess();

    // get data new task in activity tab
    // await props.getDataUpcoming();
    // return null;
  };

  return (
    <BlockView
      inset={'top'}
      style={styles.container}
    >
      <ScrollView
        scrollIndicatorInsets={{ right: 1 }}
        testID="scrollViewStep4"
        contentContainerStyle={styles.content}
      >
        <LocationPostTask
          address={address}
          homeNumber={homeNumber}
          setAddress={setAddress}
        />

        <TaskDetail />

        <PaymentMethod
          isTet={isTet}
          removePromotion={removePromotion}
          paymentMethod={paymentMethod}
          promotion={promotion}
          setPromotion={setPromotion}
        />
        {isTet ? (
          <TouchableOpacity
            style={styles.btn}
            onPress={() => _changeToRegularBooking()}
          >
            <CText
              center
              bold
              style={{
                color: Colors.SECONDARY_COLOR,
                fontSize: FontSizes.SIZE_16,
              }}
            >
              {t('TET_BOOKING_TO_NORMAL_TASK')}
            </CText>
            <CText
              center
              size={FontSizes.SIZE_16}
              margin={{ top: Spacing.SPACE_04 }}
            >
              {t('TET_BOOKING_TO_NORMAL_TASK_DESCRIPTION')}
            </CText>
          </TouchableOpacity>
        ) : null}
        {isTet ? ( // Change Tet booking to regular booking
          <CModal
            hideButtonClose
            ref={modalRef}
            title={t('TET_BOOKING_TO_NOMAL_NOTE_TITLE')}
            actions={[
              {
                text: t('TET_BOOKING_TO_NOMAL_NOTE_DONE'),
                onPress: _changeNewDate,
                disabled: DateTimeHelpers.checkIsSame({
                  timezone,
                  firstDate: date,
                  secondDate: selectedDate,
                }),
              },
            ]}
          >
            <CText>{t('TET_BOOKING_TO_NORMAL_TASK_CHANGE_DATE')}</CText>
            <BlockView>
              <DatePicker
                title={t('STEP_4_UPDATE_CALENDAR_TITLE')}
                value={selectedDate}
                onChange={onChangeDateTime}
                settingSystem={settingSystem}
                timezone={timezone}
              />

              <TimePicker
                noShowTitle={false}
                title={t('STEP_4_UPDATE_TIME_TITLE')}
                value={selectedDate}
                onChange={onChangeDateTime}
                settingSystem={settingSystem}
                timezone={timezone}
              />
            </BlockView>
          </CModal>
        ) : null}
      </ScrollView>
      <BookingButton
        price={price}
        navigation={navigation}
        onPostTask={_onPosTask}
      />
      {/* <ModalLogin
        ref={refModalLogin}
        callback={_callback} // call when login success
        routeName={{ routeName: 'Tab_Activity', screen: 'Tab_Upcoming' }} // After sign in success will navigate to this route
      /> */}
    </BlockView>
  );
};
