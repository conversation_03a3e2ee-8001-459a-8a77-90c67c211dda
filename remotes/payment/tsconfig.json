{"extends": "@react-native/typescript-config/tsconfig.json", "compilerOptions": {"baseUrl": ".", "paths": {"@components": ["src/components"], "@components/*": ["src/components/*"], "@configs": ["src/configs"], "@types": ["src/types"], "@types/*": ["src/types/*"], "@assets/*": ["src/lib/assets/*"], "@images": ["src/lib/assets/images"], "@helper": ["src/lib/helper"], "@helper/*": ["src/lib/helper/*"], "@lib/*": ["src/lib/*"], "@src/*": ["src/*"], "@screens": ["src/screens"], "@screens/*": ["src/screens/*"], "@store": ["src/store"], "@hooks": ["src/hooks"], "@hooks/*": ["src/hooks/*"], "@navigation/*": ["src/navigation/*"], "@i18n": ["src/i18n"], "@apis": ["src/apis"]}}}