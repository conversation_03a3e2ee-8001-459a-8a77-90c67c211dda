import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ertRef, <PERSON><PERSON>t } from '@btaskee/design-system';
import { NavigationContainer } from '@react-navigation/native';

import MainNavigator from '@navigation/MainNavigator';

const App = () => {
  return (
    <>
      <NavigationContainer>
        <MainNavigator />
      </NavigationContainer>
      <CAlert ref={(ref: AlertRef) => AlertHolder.setAlert(ref)} />
    </>
  );
};

export default App;
