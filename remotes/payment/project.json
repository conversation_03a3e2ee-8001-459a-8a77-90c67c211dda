{"name": "payment", "targets": {"start": {"executor": "nx:run-commands", "options": {"command": "yarn start", "cwd": "remotes/payment"}}, "install": {"executor": "nx:run-commands", "options": {"command": "yarn install", "cwd": "remotes/payment"}}, "build": {"executor": "nx:run-commands", "options": {"command": "yarn build", "cwd": "remotes/payment"}}, "deploy": {"executor": "nx:run-commands", "options": {"command": "yarn deploy", "cwd": "remotes/payment"}}, "reset": {"executor": "nx:run-commands", "options": {"command": "yarn reset", "cwd": "remotes/payment"}}}, "tags": ["scope:payment"]}