import { getSharedDependencies } from '@btaskee/sdk';
import * as Repack from '@callstack/repack';
import { ReanimatedPlugin } from '@callstack/repack-plugin-reanimated';
import rspack from '@rspack/core';
import path from 'node:path';
import { fileURLToPath } from 'node:url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Rspack configuration enhanced with Re.Pack defaults for React Native.
 *
 * Learn about Rspack configuration: https://rspack.dev/config/
 * Learn about Re.Pack configuration: https://re-pack.dev/docs/guides/configuration
 */

export default (env) => {
  const { mode } = env;
  const isDev = mode === 'development';

  return {
    mode,
    context: __dirname,
    entry: './index.js',
    experiments: {
      incremental: mode === 'development',
    },
    resolve: {
      ...Repack.getResolveOptions(),
      alias: {
        '@components': path.resolve(__dirname, 'src/components'),
        '@configs': path.resolve(__dirname, 'src/configs'),
        '@types': path.resolve(__dirname, 'src/types'),
        '@assets': path.resolve(__dirname, 'src/lib/assets'),
        '@images': path.resolve(__dirname, 'src/lib/assets/images'),
        '@helper': path.resolve(__dirname, 'src/lib/helper'),
        '@lib': path.resolve(__dirname, 'src/lib'),
        '@src': path.resolve(__dirname, 'src'),
        '@store': path.resolve(__dirname, 'src/store'),
        '@screens': path.resolve(__dirname, 'src/screens'),
        '@hooks': path.resolve(__dirname, 'src/hooks'),
        '@navigation': path.resolve(__dirname, 'src/navigation'),
        '@i18n': path.resolve(__dirname, 'src/i18n'),
        '@apis': path.resolve(__dirname, 'src/apis'),
      },
    },
    output: {
      path: path.resolve(__dirname, 'dist'),
      uniqueName: 'sas-payment',
    },
    module: {
      rules: [
        ...Repack.getJsTransformRules(),
        ...Repack.getAssetTransformRules({ inline: true }),
      ],
    },
    plugins: [
      new Repack.RepackPlugin(),
      new ReanimatedPlugin(),
      new Repack.plugins.ModuleFederationPluginV2({
        name: 'payment',
        filename: 'payment.container.js.bundle',
        dts: false,
        exposes: {
          './MainNavigator': './src/navigation/MainNavigator.tsx',
        },
        remotes: {},
        shared: getSharedDependencies({ eager: isDev }),
      }),
      // silence missing @react-native-masked-view optionally required by @react-navigation/elements
      new rspack.IgnorePlugin({
        resourceRegExp: /^@react-native-masked-view/,
      }),
    ],
  };
};
