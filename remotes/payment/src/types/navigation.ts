import { NativeStackScreenProps } from '@react-navigation/native-stack';

import { ILocation } from './index';

export type RootStackParamList = {
  CleaningChooseAddress: undefined;
  CleaningConfirmBooking: undefined;
  CleaningChooseDuration: undefined;
  CleaningChooseDateTime?: any;
  CleaningEditLocation: {
    location: ILocation;
  };
  AddNewLocation: undefined;
  CleaningPostTaskSuccess: undefined;
};

export type RootStackScreenProps<T extends keyof RootStackParamList> =
  NativeStackScreenProps<RootStackParamList, T>;
