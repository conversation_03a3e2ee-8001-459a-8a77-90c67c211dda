import { IDate } from '@btaskee/design-system';
import { IUser } from '@types/user';

// eslint-disable-next-line no-restricted-imports
import { IAddons } from '@components/optional';

export type ICurrency = {
  sign: string;
  code: string;
};

export type IPrice = {
  baseCost?: number;
  cost?: number;
  finalCost?: number;
  increaseReasons?: string[];
  decreasedReasons?: string[];
  duration?: number;
  date?: string;
  currency?: ICurrency;
  reason?: string;
  isIncrease?: boolean;
  transportFee?: number;
  depositMoney?: number;
  totalArea?: number;
  promotionBy?: string;
  newFinalCost?: number;
  vat?: number;
  totalCost?: number;
  subTasksCostDetail?: null;
  dateOptions?: string[];
  excludeTaskerFee?: string[];
  timezone?: string;
  collectionDate?: string;
  isCanGetDistance?: boolean;
};

export type IParamsGetPrice = {
  task: {
    timezone?: string;
    date: string;
    autoChooseTasker: boolean;
    taskPlace: ITaskPlace;
    homeType?: string;
    duration: number;
    forceTasker?: IUser;
    dateOptions?: IDate[];
    payment?: {
      method: string;
    };
    requirements?: { type: number }[];
    addons?: IAddons[];
    isPremium?: boolean;
  };
  service: {
    _id: string;
  };
  isoCode: string;
};
