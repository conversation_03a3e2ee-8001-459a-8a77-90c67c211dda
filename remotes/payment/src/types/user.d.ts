import {
  CountryCode,
  GENDER,
  HomeType,
  ISO_CODE,
  LOCALES,
  RANK_NAME,
  USER_STATUS,
} from '@btaskee/design-system';

import { IObjectText } from '.';

export type IUser = {
  _id?: string;
  avatar?: string;
  cities?: {
    city?: string;
    country?: ISO_CODE;
  }[];
  countryCode?: CountryCode;
  fAccountId?: string;
  gender?: GENDER;
  isoCode?: ISO_CODE;
  language?: LOCALES;
  lastPostedTask?: string;
  hospitalLocations?: IUserLocation[];
  locations?: IUserLocation[];
  homeMovingLocations?: Omit<IUserLocation, 'homeType'>[];
  name?: string;
  phone?: string;
  point?: number;
  rankInfo?: {
    point?: number;
    rankName?: RANK_NAME;
    text?: IObjectText;
  };
  referralCode?: string;
  status?: USER_STATUS;
  taskDone?: number;
  type?: string;
  voiceCallToken?: {
    status?: USER_STATUS;
    token?: string;
  };
  voiceCallTokenV2?: {
    status?: USER_STATUS;
    token?: string;
  };
  isEco?: boolean;
  housekeepingLocations?: IUserLocation[];
  // TASKER
  avgRating?: number;
  isPremiumTasker?: boolean;
  isBusinessMember?: boolean;
  isBusiness?: boolean;
  isTesterCommunity?: boolean;
  taskNoteByServiceV3?: ITaskNoteByService[];
  token?: string;
};

export type ITaskNoteByService = {
  note?: string;
  serviceId?: string;
};

export type IUserLocation = {
  _id?: string;
  address?: string;
  city?: string;
  contact?: string;
  country?: ISO_CODE;
  countryCode?: CountryCode;
  description?: string;
  district?: string;
  homeType?: HomeType;
  isoCode?: ISO_CODE;
  lat?: number;
  lng?: number;
  phoneNumber?: string;
  shortAddress?: string;
  subDistrict?: string;
  isAddressMaybeWrong?: boolean;
  houseNumber?: string;
  locationName?: string;
  isDefault?: boolean;
};
