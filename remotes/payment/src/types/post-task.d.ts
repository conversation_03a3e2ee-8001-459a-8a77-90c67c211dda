import { CountryCode, IDate, ISO_CODE } from '@btaskee/design-system';

import { IService } from './service';

export type IAddress = {
  address?: string;
  city?: string;
  contact?: string;
  country?: ISO_CODE;
  countryCode?: CountryCode;
  description?: string;
  district?: string;
  homeType?: string;
  lat?: number;
  lng?: number;
  phoneNumber?: string;
  shortAddress?: string;
  isAddressMaybeWrong?: boolean;
};

export type IRelatedTask = {
  relatedTaskId?: string;
  service?: IService;
  bookingId?: string;
  date?: IDate;
  address?: IAddress;
  limitDate?: number;
  servicesPosted?: IService[];
};

export type IParamsCheckTaskSameTime = {
  taskDate: string;
  serviceId: string;
};
