import { IObjectText } from './index';

export interface Addon {
  cost: number;
  name: string;
  text: IObjectText;
}

export interface RelatedService {
  _id: string;
  name: string;
  text: IObjectText;
  thumbnail: string;
}

export interface Requirement {
  duration: number;
  icon: string;
  text: IObjectText;
  type: number;
}

export interface WorkToDo {
  en: string;
  id: string;
  ko: string;
  th: string;
  vi: string;
}

export interface WorkingProcessDetail {
  name: string;
  text: IObjectText;
  workToDo: WorkToDo[];
}

export interface WorkingProcessV2 {
  detail: WorkingProcessDetail[];
}

export interface IService {
  _id: string;
  addons: Addon[];
  city: string[];
  defaultTaskTime: number;
  icon: string;
  name: string;
  optional: {
    isAutoChooseTaskerEnabled: boolean;
  };
  postingLimits: {
    from: string;
    to: string;
  };
  premiumOptions: {
    applyForCities: string[];
    status: string;
  };
  priceSetting: {
    costForChooseTasker: number;
  };
  relatedServices: RelatedService[];
  requirements: Requirement[];
  shortText: IObjectText;
  status: string;
  text: IObjectText;
  thumbnail: string;
  tip: number;
  weight: number;
  workingProcessV2: WorkingProcessV2;
  isTet?: boolean;
}
