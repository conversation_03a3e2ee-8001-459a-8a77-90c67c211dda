import { IDate, IUser } from '@btaskee/design-system';

// eslint-disable-next-line no-restricted-imports
import { IAddons } from '@components/optional';

import { IService } from './service';

export interface ILocation {
  lat: number;
  lng: number;
  country: string;
  city: string;
  district: string;
  address: string;
  contact?: string;
  phoneNumber?: string;
  shortAddress: string;
  countryCode?: string;
  isAddressMaybeWrong?: boolean;
  description?: string;
  homeType?: string;
}

export interface IDeviceInfo {
  systemVersion?: string;
  appVersion?: string;
  buildNumber?: string;
  brand?: string;
  model?: string;
  timezone?: string;
  isTablet?: boolean;
  baseOs?: string;
}

export interface IDataBooking {
  address?: string;
  locations?: ILocation[];
  user?: IUser;
  service?: IService;
  homeNumber?: string;
  homeType?: string;
  finalCost?: {
    amount: number;
  };
  // Additional properties
  contactName?: string;
  lat?: number;
  lng?: number;
  phone?: string;
  countryCode?: string;
  description?: string;
  askerId?: string;
  autoChooseTasker?: boolean;
  date?: string;
  timezone?: string;
  deviceInfo?: IDeviceInfo;
  duration?: number;
  houseNumber?: string;
  isSendToFavTaskers?: boolean;
  isoCode?: string;
  payment?: any;
  serviceId?: string;
  taskPlace: {
    city?: string;
    country?: string;
    district?: string;
    isAddressMaybeWrong?: boolean;
  };
  updateTaskNoteToUser?: boolean;
  shortAddress?: string;
  isTetBooking?: boolean;
  taskNote?: string;
  requirements?: {
    type: number;
  }[];
  pet?: any;
  promotion?: any;
  isPremium?: boolean;
  gender?: string;
  forceTasker?: {
    taskerId?: string;
    isResent?: boolean;
  };
  dateOptions?: IDate[];
  addons?: IAddons[];
  source?: {
    from?: string;
    taskId?: string;
  };
  weekday?: number[];
}

export type ICity = {
  key?: string;
  name?: string;
  status?: string;
  timezone?: string;
  district?: IDistrict[];
};

export type IDistrict = {
  key?: string;
  name?: string;
};

export type Maybe<T> = T | null | undefined;

export interface IObjectText {
  vi: string;
  en: string;
  ko?: string;
  th?: string;
  id?: string;
}
