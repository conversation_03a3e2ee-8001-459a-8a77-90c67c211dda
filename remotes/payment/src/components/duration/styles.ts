import { StyleSheet } from 'react-native';
import { Colors, FontSizes, Spacing } from '@btaskee/design-system';

export default StyleSheet.create({
  borderActive: {
    borderColor: Colors.PRIMARY_COLOR,
  },
  textActive: {
    color: Colors.PRIMARY_COLOR,
  },
  textDisabled: {
    color: Colors.LIGHT_GRAY,
  },
  txtArea: {
    color: Colors.GREY,
  },
  txtDuration: {
    marginBottom: Spacing.SPACE_08,
    fontSize: FontSizes.SIZE_16,
  },
  wrapButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.SPACE_16,
  },
  leftContent: {
    flex: 1,
  },
});
