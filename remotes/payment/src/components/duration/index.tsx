/**
 * Duration component for selecting service duration
 * Displays a list of duration options with corresponding area and room information
 */
import React, { useCallback, useMemo } from 'react';
import { StyleProp, ViewStyle } from 'react-native';
import {
  BlockView,
  Card,
  CText,
  Spacing,
  TouchableOpacity,
} from '@btaskee/design-system';
import { getDurationByCountry } from '@helper';

import { useI18n } from '@hooks';

import styles from './styles';

type DurationProps = {
  duration?: number;
  minDuration?: number;
  onChange: (duration: number) => void;
  disabled?: boolean;
  durationStyle?: StyleProp<ViewStyle>;
  trackingChangeDuration?: ({
    oldDuration,
    newDuration,
  }: {
    oldDuration?: number;
    newDuration?: number;
  }) => void;
};

const Duration = ({
  duration,
  minDuration,
  onChange,
  disabled,
  durationStyle,
  trackingChangeDuration,
}: DurationProps) => {
  const { t } = useI18n();

  const durationList = useMemo(() => {
    const DURATIONS = getDurationByCountry();
    return DURATIONS.map((dur) => ({
      ...dur,
      rooms: t(dur.rooms, { t: dur.duration }),
    }));
  }, [t]);

  const handleClick = useCallback(
    (newDuration: number) => () => {
      trackingChangeDuration?.({ oldDuration: duration, newDuration });
      onChange?.(newDuration);
    },
    [duration, onChange, trackingChangeDuration],
  );

  const checkActive = useCallback(
    (newDuration: number) => Boolean(duration && duration === newDuration),
    [duration],
  );

  if (!durationList?.length) return null;

  return durationList.map((item, index) => {
    const isActive = checkActive(item.duration);
    const isDisabled =
      disabled || (minDuration && minDuration > item?.duration);

    return (
      <TouchableOpacity
        key={index}
        activeOpacity={0.6}
        disabled={Boolean(isDisabled)}
        onPress={handleClick(item.duration)}
        testID={`chooseDuration-${item.duration}`}
        style={[{ marginBottom: Spacing.SPACE_16 }, durationStyle]}
      >
        <Card
          row
          center
          style={[styles.wrapButton, isActive ? styles.borderActive : {}]}
        >
          <BlockView style={styles.leftContent}>
            <CText
              bold
              style={[
                styles.txtDuration,
                isDisabled ? styles.textDisabled : {},
                isActive ? styles.textActive : {},
              ]}
            >
              {t('NUMBER_OF_HOURS', { t: item.duration })}
            </CText>
            <CText style={styles.txtArea}>
              {t('DURATION_TEXT', {
                t1: item?.area,
                t2: item?.rooms,
              })}
            </CText>
          </BlockView>
          <BlockView />
        </Card>
      </TouchableOpacity>
    );
  });
};

export default React.memo(Duration);
