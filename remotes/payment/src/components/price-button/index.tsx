/**
 * PriceButton Component
 *
 * A button component that displays price information and a call-to-action.
 * It shows the final price, original price (if discounted), and a button label.
 * The component also supports loading state and can be disabled.
 */
import React, { useCallback, useEffect, useMemo, useRef } from 'react';
import {
  ActivityIndicator,
  Animated,
  TouchableWithoutFeedback,
  ViewStyle,
} from 'react-native';
import {
  AnimationHelpers,
  BlockView,
  Colors,
  CText,
  formatMoney,
  INCREASE_FEE_KEY,
  SERVICES,
  usePostTaskStore,
} from '@btaskee/design-system';
import { find, get, isEmpty } from 'lodash-es';

import { useI18n } from '@hooks';

import styles from './styles';

interface PriceButtonProps {
  isLoading?: boolean;
  IDOriginPrice?: string;
  fromScreen?: string;
  pricePostTask?: any;
  priceUpdate?: any;
  costDetail?: any;
  isChangeBackgroundWhenDisabled?: boolean;
  isDisabled?: boolean;
  IDPrice?: string;
  address?: any;
  curtainLessBasePrice?: any;
  HeaderComponent?: React.ReactNode;
  nextButtonContainer?: ViewStyle;
  isHideVat?: boolean;
  onPress?: () => void;
  testID?: string;
}

/**
 * PriceButton component that displays price information and a call-to-action button
 */
const PriceButton: React.FC<PriceButtonProps> = (props) => {
  const {
    isLoading,
    IDOriginPrice,
    fromScreen,
    pricePostTask,
    priceUpdate,
    costDetail,
    isChangeBackgroundWhenDisabled,
    isDisabled,
    IDPrice,
    HeaderComponent = null,
    nextButtonContainer,
    isHideVat,
    onPress,
    testID,
  } = props;

  const { t } = useI18n();
  const { currency } = usePostTaskStore();

  // Animation values for slide-in effect
  const slideAnim = useRef(new Animated.Value(300)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;

  // Start animation when component mounts
  useEffect(() => {
    // Run layout animation for smooth appearance
    AnimationHelpers.runLayoutAnimation('easeInEaseOut', 500);

    // Use the enhanced animation helper for slide-in with fade
    // Using a longer duration (800ms) for a more noticeable animation
    AnimationHelpers.slideInUpWithFade(slideAnim, fadeAnim).start();
  }, [slideAnim, fadeAnim]);

  // Determine which price to use based on screen context
  let price = pricePostTask;
  if (fromScreen === 'TASK_UPDATE') {
    price = priceUpdate;
  }

  // If data task not change, get price from task
  if (!price && fromScreen === 'TASK_UPDATE') {
    price = costDetail;
  }

  /**
   * Handle button click
   */
  const handleClick = useCallback(() => {
    onPress && onPress();
  }, [onPress]);

  /**
   * Renders additional price information for laundry service
   */
  const renderSubPriceLaundry = useCallback(() => {
    if (fromScreen && fromScreen === SERVICES.LAUNDRY) {
      const increaseReasons = get(price, 'increaseReasons', []);
      const emergency = find(increaseReasons, {
        key: INCREASE_FEE_KEY.EMERGENCY,
      });
      return (
        <BlockView left>
          <CText style={styles.txtSubPriceLaundry}>
            {t('INCLUDE_FEE_SHIP')}
          </CText>
          {emergency ? (
            <CText
              style={styles.txtSubPriceLaundry}
              testID={'lbEmergencyFee'}
            >
              {t('LAUNDRY_EMERGENCY_FEE', {
                t1: formatMoney(emergency.value),
                t2: currency?.sign,
              })}
            </CText>
          ) : null}
        </BlockView>
      );
    }
    return null;
  }, [fromScreen, price, currency, t]);

  /**
   * Renders the price button with all its content
   */
  const renderPriceButton = useMemo(() => {
    if (!price && fromScreen !== 'TASK_UPDATE') {
      return null;
    }

    let priceText = '';
    let originPriceText = '';
    // Default VAT cost deduction is 0
    let vatCostDeduction = 0;
    const disabledButton = Boolean(
      isLoading || (price && price.error) || isDisabled,
    );

    /**
     * If VAT should be hidden, calculate the deduction amount
     * The finalCost already includes VAT
     * The VAT amount to deduct is in the vat field of price
     * The pre-VAT amount equals finalCost minus vatCostDeduction
     */
    if (isHideVat) {
      vatCostDeduction = price?.vat;
    }

    // Format the final price text
    if (price?.finalCost) {
      priceText = `${t('COST_AND_CURRENCY', {
        cost: formatMoney(price.finalCost - vatCostDeduction),
        currency: currency?.sign,
      })}`;
    }

    // Add duration if available
    if (price?.duration) {
      priceText += `/${price.duration}h`;
    }

    // Format original price if there's a promotion
    if (price && price.cost > price.finalCost) {
      originPriceText = `${t('COST_AND_CURRENCY', {
        cost: formatMoney(price.cost - vatCostDeduction),
        currency: currency?.sign,
      })}`;
    }

    // Determine button title based on screen context
    let titleButton = t('NEXT');
    if (fromScreen === 'TASK_UPDATE') {
      titleButton = t('UPDATE');
    }

    return (
      <Animated.View
        style={[
          styles.container,
          nextButtonContainer,
          {
            transform: [{ translateY: slideAnim }],
            opacity: fadeAnim,
          },
        ]}
      >
        {HeaderComponent}
        <BlockView inset={'bottom'}>
          <TouchableWithoutFeedback
            disabled={disabledButton}
            onPress={handleClick}
          >
            <BlockView
              left
              style={[
                styles.btn,
                isChangeBackgroundWhenDisabled &&
                disabledButton &&
                styles.btnDisabled,
              ]}
            >
              <BlockView
                center
                row
              >
                <BlockView style={styles.left}>
                  {isLoading ? (
                    <BlockView style={styles.loading}>
                      <ActivityIndicator color={Colors.WHITE} />
                    </BlockView>
                  ) : (
                    <BlockView>
                      {originPriceText ? (
                        <CText
                          style={styles.txtOriginPrice}
                          testID={
                            IDOriginPrice ? IDOriginPrice : 'lbOriginPrice'
                          }
                        >
                          {originPriceText}
                        </CText>
                      ) : null}
                      <CText
                        bold
                        style={styles.txtPrice}
                        testID={IDPrice ? IDPrice : 'lbPrice'}
                      >
                        {priceText}
                      </CText>
                    </BlockView>
                  )}
                </BlockView>
                <BlockView style={styles.right}>
                  <CText
                    style={styles.txtNext}
                    testID={testID}
                  >
                    {titleButton}
                  </CText>
                </BlockView>
              </BlockView>
              {renderSubPriceLaundry()}
            </BlockView>
          </TouchableWithoutFeedback>
        </BlockView>
      </Animated.View>
    );
  }, [
    price,
    fromScreen,
    isLoading,
    isDisabled,
    isHideVat,
    t,
    nextButtonContainer,
    slideAnim,
    fadeAnim,
    HeaderComponent,
    handleClick,
    isChangeBackgroundWhenDisabled,
    IDOriginPrice,
    IDPrice,
    testID,
    renderSubPriceLaundry,
    currency?.sign,
  ]);

  // Check if promotion discount 100% cost, show price
  if (isEmpty(price) || !price?.finalCost) {
    return null;
  }

  return renderPriceButton;
};

export default React.memo(PriceButton);
