import React from 'react';
import { <PERSON>tar, BlockView, CText, FontSizes } from '@btaskee/design-system';
import { IUser } from '@types';

import { useI18n } from '@hooks';

import styles from './styles';

const TaskerFavoriteForRebook = ({ forceTasker }: { forceTasker: IUser }) => {
  const { t } = useI18n();
  return (
    <BlockView>
      <CText
        h4
        bold
      >
        {t('FAV_TASKER.TASKER')}
      </CText>
      <BlockView row>
        <Avatar
          size={50}
          avatar={forceTasker?.avatar}
          isPremiumTasker={forceTasker?.isPremiumTasker}
        />
        <BlockView style={styles.wrapInfo}>
          <CText size={FontSizes.SIZE_16}>{forceTasker?.name}</CText>
        </BlockView>
      </BlockView>
    </BlockView>
  );
};
export default TaskerFavoriteForRebook;
