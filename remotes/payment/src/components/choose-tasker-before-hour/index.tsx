import React, { memo } from 'react';
import {
  BEFORE_HOUR_POST_TASK_CHOOSE_TASKER,
  BlockView,
  Markdown,
  Spacing,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';

import { styles } from './styles';

/**
 * Component that displays a message about choosing a tasker before a specific hour
 * Uses markdown to format the text with the hour parameter
 */
export const ChooseTaskerBeforeHour = memo(() => {
  const { t } = useI18n();

  return (
    <BlockView
      padding={{ horizontal: Spacing.SPACE_16 }}
      margin={{ top: Spacing.SPACE_16 }}
    >
      <Markdown
        text={t('POST_TASK_BEFORE_HOUR', {
          hour: BEFORE_HOUR_POST_TASK_CHOOSE_TASKER,
        })}
        paragraphStyle={styles.markdownText}
      />
    </BlockView>
  );
});
