import React from 'react';
import { icArrowUp, iconArrowDown } from '@assets/images';
import {
  AnimationHelpers,
  BlockView,
  Colors,
  ConditionView,
  CText,
  FontSizes,
  IconImage,
  Spacing,
  TouchableOpacity,
} from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

import { useI18n } from '@hooks';

import PaymentDetailDateOptions from '../payment-detail-date-options';
import WarningTaskDateOptions from '../warning-task-date-options';

export const PaymentDetailStep4WithDateOptions = ({
  dateOptions,
  timezone,
  paymentMethod,
}: {
  dateOptions: any;
  timezone: string;
  paymentMethod: any;
}) => {
  const { t } = useI18n();

  const [isShow, setIsShow] = React.useState(true);

  const handlePress = () => {
    AnimationHelpers.runLayoutAnimation();
    setIsShow(!isShow);
  };

  if (isEmpty(dateOptions)) return null;

  return (
    <BlockView margin={{ top: Spacing.SPACE_28, bottom: Spacing.SPACE_08 }}>
      <TouchableOpacity onPress={handlePress}>
        <BlockView
          row
          jBetween
        >
          <CText
            bold
            style={{ fontSize: FontSizes.SIZE_16 }}
          >
            {t('TITLE_PAYMENT_DETAIL')}
          </CText>
          <IconImage
            source={isShow ? icArrowUp : iconArrowDown}
            color={Colors.BLACK}
            size={18}
          />
        </BlockView>
      </TouchableOpacity>
      <ConditionView
        condition={isShow}
        viewTrue={
          <>
            {dateOptions?.map((item: any, index: number) => (
              <PaymentDetailDateOptions
                testID={`step4DateOptions_${index}`}
                key={`step4DateOptions_${index}`}
                title={t('FAV_TASKER.OPTION_TIME', { stt: index + 1 })}
                value={item}
                timezone={timezone}
              />
            ))}
          </>
        }
      />
      <ConditionView
        condition={dateOptions.length > 1}
        viewTrue={<WarningTaskDateOptions paymentMethod={paymentMethod} />}
      />
    </BlockView>
  );
};
