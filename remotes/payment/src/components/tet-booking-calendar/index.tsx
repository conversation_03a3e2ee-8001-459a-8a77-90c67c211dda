/**
 * TET Booking Calendar Component
 *
 * A specialized calendar component for handling TET holiday bookings.
 * Allows users to select dates within a specific booking period for TET holiday services.
 * Includes lunar calendar information and special date handling.
 */
import React, { useCallback, useMemo } from 'react';
import { CalendarList, CalendarListProps } from 'react-native-calendars';
import { iconWarning } from '@assets/images';
import {
  BlockView,
  Colors,
  ConditionView,
  CText,
  DateTimeHelpers,
  ESTIMATED_TIME_POST_TASK_MINUTES,
  FastImage,
  FontSizes,
  getTextWithLocale,
  IDate,
  ITimezone,
  MIN_POST_TASK_TIME,
  NATIONAL_HOLIDAYS,
  PrimaryButton,
  roundOfNumberMinutes,
  Spacing,
  TouchableOpacity,
  TypeFormatDate,
} from '@btaskee/design-system';
import { getLunaHoliday } from '@helper';
import LocaleConfig from '@lib/configs/locale-config';
import { get, isEmpty } from 'lodash-es';

import { useI18n } from '@hooks';

import { styles, theme } from './styles';

const TET_DATE = {
  day: 1,
  month: 1,
};

interface TetBookingCalendarProps {
  date?: IDate;
  service?: any;
  onChangeDateTime?: (date: IDate, timezone: ITimezone) => void;
  settingSystem?: any;
  onRemoveServiceTet?: () => void;
  onChooseDate?: () => void;
  timezone: ITimezone;
}

/**
 * TET Booking Calendar component for selecting dates during TET holiday period
 */
const TetBookingCalendar: React.FC<TetBookingCalendarProps> = (props) => {
  const { t, i18n } = useI18n();

  // Set locale for Calendar component
  LocaleConfig.defaultLocale = i18n.language;

  const {
    date,
    service,
    onChangeDateTime,
    settingSystem,
    onRemoveServiceTet,
    onChooseDate,
    timezone,
  } = props;

  // Using null instead of undefined for configSpecialPreBooking
  const configSpecialPreBooking = null;

  const [selectedDate, setSelectedDate] = React.useState<IDate | undefined>(
    date,
  );
  const [isChangeBooking, setIsChangeBooking] = React.useState<boolean>(false);
  const [isClickDisabled, setIsClickDisabled] = React.useState<boolean>(false);

  const minute = roundOfNumberMinutes(DateTimeHelpers.getMinute({ timezone }));

  // Get minPostTaskTime from settingSystem with fallback
  const minPostTaskTime =
    get(settingSystem, 'minPostTaskTime', MIN_POST_TASK_TIME) +
    ESTIMATED_TIME_POST_TASK_MINUTES;

  // Calculate minimum date/time to post task
  const minDateTimePostTask = DateTimeHelpers.toDayTz({ timezone })
    .minute(minute)
    .add(minPostTaskTime, 'minute')
    .startOf('minute');

  const tetBookingEndDate = get(
    service,
    'tetBookingDates.bookTaskTime.toDate',
    null,
  );
  const tetBookingFromDate = get(
    service,
    'tetBookingDates.bookTaskTime.fromDate',
    null,
  );

  // Prepare marked dates for calendar
  const markedDate = useMemo(() => {
    if (!date || !timezone) return {};

    const result: Record<string, any> = {};
    const keyMarkedDate = DateTimeHelpers.formatToString({
      timezone,
      date,
      typeFormat: TypeFormatDate.IsoDate,
    });

    result[keyMarkedDate] = {
      selected: true,
      marked: true,
      selectedColor: Colors.PRIMARY_COLOR,
      dotColor: Colors.PRIMARY_COLOR,
    };

    return result;
  }, [date, timezone]);

  /**
   * Calculates the number of months difference between two dates
   * @param fromDate - Start date
   * @param toDate - End date
   * @returns The absolute number of months between dates
   */
  const getDiffMonths = useCallback(
    (fromDate: IDate, toDate: IDate): number => {
      if (!fromDate || !toDate || !timezone) return 0;

      // Calculate difference with fraction
      const diff = DateTimeHelpers.toDateTz({ date: fromDate, timezone })
        .startOf('month')
        .diff(
          DateTimeHelpers.toDateTz({ date: toDate, timezone }).startOf('month'),
          'month',
          true,
        );

      return Math.abs(diff);
    },
    [timezone],
  );

  /**
   * Handles date change and passes to parent component
   * @param value - The selected date
   */
  const onChangeValue = useCallback(
    (value: IDate): void => {
      if (!value || !timezone || !onChangeDateTime) return;

      const dateFormat = DateTimeHelpers.formatToString({
        timezone,
        date: value,
      });
      onChangeDateTime(dateFormat, timezone);
    },
    [onChangeDateTime, timezone],
  );

  /**
   * Handles form submission after date selection
   */
  const onSubmit = useCallback(async (): Promise<void> => {
    if (!selectedDate) return;

    if (isChangeBooking) {
      await onRemoveServiceTet?.();
    }

    onChangeValue(selectedDate);
    onChooseDate?.();
  }, [
    isChangeBooking,
    onChangeValue,
    onChooseDate,
    onRemoveServiceTet,
    selectedDate,
  ]);

  /**
   * Number of months to display before the selected date
   */
  const numPastScroll = useMemo((): number => {
    if (!selectedDate || !timezone) return 0;
    return getDiffMonths(selectedDate, DateTimeHelpers.toDayTz({ timezone }));
  }, [getDiffMonths, selectedDate, timezone]);

  /**
   * Number of months to display after the selected date
   */
  const numFeatureScroll = useMemo((): number => {
    if (!selectedDate || !tetBookingEndDate) return 0;
    return getDiffMonths(tetBookingEndDate, selectedDate);
  }, [getDiffMonths, selectedDate, tetBookingEndDate]);

  /**
   * Checks if a date should be disabled in the calendar
   * @param dateFormat - The date to check
   * @returns True if the date should be disabled
   */
  const _checkDisabled = useCallback(
    (dateFormat: IDate): boolean => {
      if (!dateFormat || !timezone || !tetBookingFromDate || !tetBookingEndDate)
        return true;

      // Check if date is before today
      const isBefore = DateTimeHelpers.checkIsBefore({
        timezone,
        firstDate: dateFormat,
        unit: 'day',
      });

      // Check if date is after booking start date
      const isAfter = DateTimeHelpers.checkIsAfter({
        timezone,
        firstDate: tetBookingFromDate,
        secondDate: dateFormat,
        unit: 'day',
      });

      // Check if date is after booking end date
      const isAfterTet = DateTimeHelpers.checkIsAfter({
        timezone,
        firstDate: dateFormat,
        secondDate: tetBookingEndDate,
        unit: 'day',
      });

      return Boolean(isBefore || isAfter || isAfterTet);
    },
    [tetBookingEndDate, tetBookingFromDate, timezone],
  );

  /**
   * Handles date selection in the calendar
   * @param dateFormat - The selected date
   */
  const _onChooseDate = useCallback(
    (dateFormat: IDate): void => {
      if (!dateFormat || !timezone) return;

      if (_checkDisabled(dateFormat)) {
        setIsClickDisabled(true);
        return;
      }

      setIsClickDisabled(false);
      isChangeBooking && setIsChangeBooking(false);

      let newDate = dateFormat;

      // Check if chosen date is before minimum post task time
      const isSameOrBefore = DateTimeHelpers.checkIsSameOrBefore({
        timezone,
        firstDate: dateFormat,
        secondDate: minDateTimePostTask,
      });

      if (isSameOrBefore) {
        // Round to 5 minutes
        const roundMinutes = roundOfNumberMinutes(
          DateTimeHelpers.getMinute({ timezone, date: minDateTimePostTask }),
        );

        newDate = minDateTimePostTask.minute(roundMinutes).toDate();
      }

      // Check if date is more than 2 days in the future
      const isAfter = DateTimeHelpers.checkIsAfter({
        timezone,
        firstDate: DateTimeHelpers.toDayTz({ timezone })
          .add(2, 'day')
          .endOf('date'),
        secondDate: newDate,
      });

      if (isAfter) {
        setIsChangeBooking(true);
      }

      setSelectedDate(newDate);
    },
    [_checkDisabled, isChangeBooking, minDateTimePostTask, timezone],
  );

  /**
   * Custom day component renderer for the calendar
   */
  const _renderDay: CalendarListProps['dayComponent'] = useCallback(
    ({ date: dateOfCalendar, state }) => {
      if (!dateOfCalendar || !timezone || !props.date) return null;

      const isLunaHoliday =
        getLunaHoliday(dateOfCalendar?.dateString, timezone) ===
        NATIONAL_HOLIDAYS.find(
          (item) => item.day === TET_DATE.day && item.month === TET_DATE.month,
        )?.info;

      const dateFormat = DateTimeHelpers.toDateTz({
        timezone,
        date: dateOfCalendar?.timestamp,
      })
        .set('hour', DateTimeHelpers.getHour({ timezone, date: props.date }))
        .set(
          'minute',
          DateTimeHelpers.getMinute({ timezone, date: props.date }),
        );

      let dayBgColor = Colors.WHITE;
      let txtDayColor = Colors.BLACK;

      // Check if this is the currently selected day
      const isSameCurrentDay = DateTimeHelpers.checkIsSame({
        timezone,
        firstDate: selectedDate,
        secondDate: dateFormat,
        unit: 'day',
      });

      // Only allow booking from 2 days in the future
      const isAfterDay = DateTimeHelpers.checkIsSameOrAfter({
        timezone,
        firstDate: DateTimeHelpers.toDayTz({ timezone }).add(2, 'day'),
        secondDate: dateFormat,
        unit: 'day',
      });

      if (isAfterDay) {
        txtDayColor = Colors.WHITE;
      }

      if (isSameCurrentDay) {
        dayBgColor = Colors.SECONDARY_COLOR;
        txtDayColor = Colors.WHITE;
      }

      const onPress = () => _onChooseDate(dateFormat);

      // State represents disabled days
      if (state) {
        return null;
      }

      return (
        <TouchableOpacity
          onPress={onPress}
          style={styles.dayContainer}
          disabled={isAfterDay}
        >
          <BlockView
            style={[styles.boxDayTop, { backgroundColor: dayBgColor }]}
          >
            <CText
              bold
              center
              style={[{ color: txtDayColor }]}
            >
              {dateOfCalendar?.day}
            </CText>
          </BlockView>
          <BlockView
            center
            style={styles.boxDayBottom}
          >
            <CText
              color={isLunaHoliday ? Colors.RED : Colors.GREY}
              size={FontSizes.SIZE_12}
            >
              {getLunaHoliday(dateOfCalendar?.dateString, timezone)}
            </CText>
          </BlockView>
        </TouchableOpacity>
      );
    },
    [_onChooseDate, props.date, selectedDate, timezone],
  );

  return (
    <BlockView flex>
      <BlockView flex>
        <CalendarList
          calendarHeight={0}
          futureScrollRange={numFeatureScroll}
          pastScrollRange={numPastScroll}
          calendarStyle={{
            marginLeft: -Spacing.SPACE_16,
          }}
          current={DateTimeHelpers.formatToString({
            timezone,
            date,
            typeFormat: TypeFormatDate.IsoDate,
          })}
          minDate={
            tetBookingFromDate
              ? DateTimeHelpers.formatToString({
                timezone,
                date: tetBookingFromDate,
                typeFormat: TypeFormatDate.IsoDate,
              })
              : undefined
          }
          maxDate={
            tetBookingEndDate
              ? DateTimeHelpers.formatToString({
                timezone,
                date: tetBookingEndDate,
                typeFormat: TypeFormatDate.IsoDate,
              })
              : undefined
          }
          markedDates={markedDate}
          theme={theme}
          dayComponent={_renderDay}
        />
      </BlockView>
      <ConditionView
        condition={isEmpty(configSpecialPreBooking?.appConfig)}
        viewTrue={
          <BlockView style={styles.boxNote}>
            <BlockView style={styles.containerNote}>
              <FastImage
                style={styles.imageWarning}
                source={iconWarning}
                tintColor={Colors.ORANGE}
              />
              <BlockView flex>
                <CText style={styles.txtInfo}>
                  {t('PRE_BOOKING_WARNING_BOOKING_DATE', {
                    eventName: getTextWithLocale(
                      configSpecialPreBooking?.appConfig?.banner?.title?.text,
                    ),
                    startDate: tetBookingFromDate
                      ? DateTimeHelpers.formatToString({
                        timezone,
                        date: tetBookingFromDate,
                        typeFormat: TypeFormatDate.DateMonthDay,
                      })
                      : '',
                    endDate: tetBookingEndDate
                      ? DateTimeHelpers.formatToString({
                        timezone,
                        date: tetBookingEndDate,
                        typeFormat: TypeFormatDate.DateMonthYearFull,
                      })
                      : '',
                  })}
                </CText>
              </BlockView>
            </BlockView>
            <PrimaryButton
              title={t('OK')}
              onPress={onSubmit}
            />
          </BlockView>
        }
      />
    </BlockView>
  );
};

export default React.memo(TetBookingCalendar);
