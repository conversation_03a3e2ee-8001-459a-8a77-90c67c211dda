/**
 * Styles for the TET Booking Calendar component
 *
 * Contains both StyleSheet definitions and theme configuration for the calendar
 */
import { Dimensions, StyleSheet } from 'react-native';
import {
  BorderRadius,
  Colors,
  FontSizes,
  Spacing,
} from '@btaskee/design-system';

const { width } = Dimensions.get('window');

// Calculate day width based on screen width
const DAY_WIDTH = width / 11;

/**
 * StyleSheet for TET Booking Calendar component
 */
export const styles = StyleSheet.create({
  titleStyle: {
    fontWeight: '600',
    fontFamily: 'Montserrat-Bold',
    fontSize: 16,
  },
  buttonStyle: {
    minHeight: 48,
  },
  imageWarning: {
    width: 28,
    height: 28,
    margin: Spacing.SPACE_04,
  },
  containerNote: {
    flexDirection: 'row',
    borderRadius: BorderRadius.RADIUS_08,
    borderColor: Colors.ORANGE,
    borderWidth: 1,
    backgroundColor: Colors.ORANGE_2,
    padding: Spacing.SPACE_08,
    marginBottom: Spacing.SPACE_04,
    alignItems: 'center',
  },
  txtInfo: {
    marginHorizontal: Spacing.SPACE_04,
    lineHeight: 20,
  },
  boxNote: {
    backgroundColor: Colors.WHITE,
  },
  dayContainer: {
    width: DAY_WIDTH,
    justifyContent: 'center',
  },
  boxDayTop: {
    width: DAY_WIDTH,
    height: DAY_WIDTH,
    borderRadius: DAY_WIDTH / 2,
    alignItems: 'center',
    justifyContent: 'center',
  },
  boxDayBottom: {},
});

/**
 * Theme configuration for the react-native-calendars component
 */
export const theme = {
  monthTextColor: Colors.BLACK,
  textMonthFontWeight: 'bold',
  textDayFontSize: FontSizes.SIZE_16,
  textMonthFontSize: 20,
  todayTextColor: Colors.PRIMARY_COLOR,
  selectedDayBackgroundColor: Colors.SECONDARY_COLOR,
  arrowColor: Colors.SECONDARY_COLOR,
  'stylesheet.calendar.header': {
    dayHeader: {
      width: 32,
      fontSize: FontSizes.SIZE_12,
      marginTop: 2,
      marginBottom: 7,
      textAlign: 'center',
      color: Colors.BLACK_4,
      fontWeight: 'bold',
    },
  },
};
