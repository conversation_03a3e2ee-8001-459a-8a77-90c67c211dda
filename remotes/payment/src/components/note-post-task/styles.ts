/**
 * Styles for the TaskNoteForPostTask component
 */
import { Dimensions, StyleSheet } from 'react-native';
import { Colors, FontSizes, Spacing } from '@btaskee/design-system';

const { height } = Dimensions.get('window');
const HEIGHT_INPUT = Math.round(height / 4);

export const styles = StyleSheet.create({
  container: {
    paddingHorizontal: Spacing.SPACE_16,
  },
  txtDescription: {
    color: Colors.BLACK,
    marginBottom: Spacing.SPACE_08,
  },
  txtNoteForTasker: {
    color: Colors.BLACK,
    fontSize: FontSizes.SIZE_20,
    marginBottom: Spacing.SPACE_12,
  },
  containerStyle: {
    paddingHorizontal: 0,
  },
  txtTitleCheckBox: {
    color: Colors.BLACK,
    fontSize: FontSizes.SIZE_14,
    marginLeft: Spacing.SPACE_08,
  },
  touchableRememberNote: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: Spacing.SPACE_08,
  },
  inputStyle: {
    height: HEIGHT_INPUT,
    paddingTop: Spacing.SPACE_16,
    paddingRight: Spacing.SPACE_16,
  },
  checkboxImage: {
    width: 24,
    height: 24,
  },
});
