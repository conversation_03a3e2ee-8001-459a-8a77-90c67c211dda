/**
 * TaskNoteForPostTask Component
 *
 * A component that allows users to enter notes for a task and optionally apply the note to all tasks.
 * It provides a text input area with a checkbox option to remember the note for future tasks.
 */
import React, { useCallback, useEffect, useMemo, useRef } from 'react';
import { ScrollView, ViewStyle } from 'react-native';
// Import image
import { iconCheckBox, iconCheckBoxChecked } from '@assets/images';
import {
  AnimationHelpers,
  BlockView,
  CText,
  CTextInput,
  FastImage,
  SERVICES,
  TouchableOpacity,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';

// Import component
import { styles } from './styles';

const MAX_HEIGHT = 32;
let isShowCheckbox = false;
let animationting = false;

interface Service {
  name: string;
  _id: string;
}

interface TaskNoteByService {
  serviceId: string;
  note: string;
}

interface User {
  taskNoteByServiceV3?: TaskNoteByService[];
}

interface TaskNoteForPostTaskProps {
  value?: string;
  isApplyNoteForAllTask?: boolean;
  service?: Service;
  user?: User;
  defaultValue?: string;
  editTask?: boolean;
  autoFocus?: boolean;
  testID?: string;
  containerStyle?: ViewStyle;
  bounces?: boolean;
  isTaskUpdate?: boolean;
  setNote: (note: string) => void;
  setNoteForAllTask?: (value: boolean) => void;
  setDisablePriceButton?: (value: boolean) => void;
}

const TaskNoteForPostTask: React.FC<TaskNoteForPostTaskProps> = (props) => {
  const { t } = useI18n();

  const [note, setNote] = React.useState(props.value || '');
  const [height, setHeight] = React.useState(0);
  const [isRememberNote, setRememberNote] = React.useState(
    Boolean(props.isApplyNoteForAllTask),
  );
  const [taskNoteDefault, setTaskNoteDefault] = React.useState<
    string | undefined
  >();

  const inputRef = useRef<CTextInput>(null);
  const timeoutId = useRef<NodeJS.Timeout | null>(null);

  /**
   * Determines the title and description text based on the service type
   */
  const dataRender = useMemo(() => {
    const data = new Map([
      [
        SERVICES.CHILD_CARE,
        {
          title: t('CHILD_CARE_NOTE_TITLE'),
          description: t('TASK_NOTE_DESCRIPTION_CHILD_CARE'),
        },
      ],
      [
        SERVICES.CHILD_CARE_SUBSCRIPTION,
        {
          title: t('CHILD_CARE_NOTE_TITLE'),
          description: t('TASK_NOTE_DESCRIPTION_CHILD_CARE'),
        },
      ],
    ]);

    const serviceName = props?.service?.name as SERVICES;
    return data.get(serviceName)
      ? data.get(serviceName)
      : {
          title: t('LABEL_NOTE_FOR_TASKER'),
          description: t('TASK_NOTE_DESCRIPTION'),
        };
  }, [props?.service?.name, t]);

  /**
   * Sets the default task note from user preferences or default value
   */
  const _setTaskNoteDefault = useCallback(() => {
    if (props.defaultValue && props.editTask) {
      // save task note to reducer
      props.setNote(props.defaultValue);
      setNote(props.defaultValue);
      setTaskNoteDefault(props.defaultValue);
      return;
    }

    const { user, service } = props;
    if (!user || !service) return;

    const taskNoteByServiceV3 = user.taskNoteByServiceV3;
    if (!taskNoteByServiceV3 || !Array.isArray(taskNoteByServiceV3)) return;

    const noteService = taskNoteByServiceV3.find(
      (item) => item.serviceId === service._id,
    );
    if (noteService?.note) {
      // save task note to reducer
      props.setNote(noteService.note);
      setNote(noteService.note);
      setTaskNoteDefault(noteService.note);
    }
  }, [props]);

  useEffect(() => {
    _setTaskNoteDefault();
    return () => {
      if (timeoutId.current) {
        clearTimeout(timeoutId.current);
      }
    };
  }, [_setTaskNoteDefault]);

  useEffect(() => {
    if (props?.autoFocus && inputRef.current) {
      inputRef.current.focus();
    }
  }, [props?.autoFocus]);

  /**
   * Saves the note to the reducer
   */
  const setNoteReducer = useCallback(() => {
    if (note?.trim()) {
      props.setNote(note.trim());
    }
  }, [note, props]);

  /**
   * Shows the checkbox with animation
   */
  const showCheckboxWithAnimation = useCallback(() => {
    AnimationHelpers.runLayoutAnimation();
    isShowCheckbox = true;
    animationting = true;
  }, []);

  /**
   * Hides the checkbox with animation
   */
  const hideCheckboxWithAnimation = useCallback(() => {
    AnimationHelpers.runLayoutAnimation();
    isShowCheckbox = false;
    animationting = true;
  }, []);

  /**
   * Handles text changes in the note input
   * @param text - The new text value
   */
  const _onChangeText = useCallback(
    (text: string) => {
      // check note from task update
      if (
        props.editTask &&
        props.setDisablePriceButton &&
        props.defaultValue !== undefined
      ) {
        props.setDisablePriceButton(text === props.defaultValue);
      }

      // if text task note default is changed, show checkBox task note
      if (text !== taskNoteDefault) {
        setHeight(MAX_HEIGHT);
      } else {
        hideCheckboxWithAnimation();
      }

      setNote(text);
      props.setNote(text);

      // show check box
      if (text && text.length > 0 && !isShowCheckbox && !animationting) {
        showCheckboxWithAnimation();
      } else if (text.length === 0) {
        // hide checkbox
        hideCheckboxWithAnimation();
      }
    },
    [
      props,
      taskNoteDefault,
      hideCheckboxWithAnimation,
      showCheckboxWithAnimation,
    ],
  );

  /**
   * Handles checkbox toggle
   */
  const _onCheck = useCallback(() => {
    const newValue = !isRememberNote;
    setRememberNote(newValue);
    if (props.setNoteForAllTask) {
      props.setNoteForAllTask(newValue);
    }
  }, [isRememberNote, props.setNoteForAllTask]);

  /**
   * Determines whether to render the checkbox based on current state
   */
  const shouldRenderCheckBox = useMemo(() => {
    // Height is 0 or task update mode, hide checkbox
    if (height <= 0 || props?.isTaskUpdate) {
      return null;
    }

    const checkBoxImage = props.isApplyNoteForAllTask
      ? iconCheckBoxChecked
      : iconCheckBox;

    return (
      <TouchableOpacity
        testID="checkBoxNoteForAllTask"
        onPress={_onCheck}
        style={styles.touchableRememberNote}
        hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
      >
        <TouchableOpacity onPress={_onCheck}>
          <FastImage
            source={checkBoxImage}
            style={styles.checkboxImage}
          />
        </TouchableOpacity>
        <CText style={styles.txtTitleCheckBox}>
          {t('TITLE_CHECKBOX_TASK_NOTE')}
        </CText>
      </TouchableOpacity>
    );
  }, [props.isApplyNoteForAllTask, height, _onCheck, t]);

  return (
    <ScrollView
      style={[styles.container, props?.containerStyle]}
      bounces={props?.bounces !== false}
      scrollIndicatorInsets={{ right: 1 }}
    >
      <BlockView>
        <CText
          testID="scrollStep3"
          bold
          style={styles.txtNoteForTasker}
        >
          {dataRender?.title}
        </CText>
        <CText style={styles.txtDescription}>{dataRender?.description}</CText>
        <CTextInput
          testID={props?.testID || 'taskNote'}
          ref={inputRef}
          inputStyle={styles.inputStyle}
          placeholder={t('DISINFECTION_SERVICE_NOTE_CONTENT')}
          multiline
          maxLength={400}
          returnKeyType="default"
          onChangeText={_onChangeText}
          value={note}
          containerStyle={styles.containerStyle}
          onSubmitEditing={setNoteReducer}
          onBlur={setNoteReducer}
          textAlignVertical="top"
        />
      </BlockView>
      {shouldRenderCheckBox}
    </ScrollView>
  );
};

export default React.memo(TaskNoteForPostTask);
