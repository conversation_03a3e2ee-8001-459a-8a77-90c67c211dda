/**
 * Styles for the RepeatWeekly component
 */
import { StyleSheet } from 'react-native';
import { Colors, DeviceHelper, Spacing } from '@btaskee/design-system';

// Calculate the size of each day button based on screen width
export const SIZE_ITEM = Math.round(DeviceHelper.WINDOW.WIDTH / 9.5);

export const styles = StyleSheet.create({
  // Button styles
  btnInfo: {
    paddingLeft: 5,
    paddingRight: 10,
  },

  // Layout styles
  left: {
    alignItems: 'center',
  },
  wrap_weekly: {
    justifyContent: 'space-between',
  },

  // Text styles
  txtDay: {
    color: Colors.BLACK,
    fontSize: 13,
  },

  // Container styles
  wrapper: {
    borderWidth: 1,
    borderColor: Colors.BORDER_COLOR,
    justifyContent: 'space-between',
    padding: Spacing.SPACE_12,
    borderRadius: 6,
    backgroundColor: Colors.WHITE,
    marginTop: Spacing.SPACE_24,
    flexDirection: 'row',
  },

  // Active state styles
  textActive: {
    color: Colors.WHITE,
  },
  backgroundActive: {
    backgroundColor: Colors.PRIMARY_COLOR,
  },

  // Day button styles
  wrapperButton: {
    height: SIZE_ITEM,
    width: SIZE_ITEM,
    borderRadius: SIZE_ITEM / 2,
    overflow: 'hidden',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.BORDER_COLOR,
  },

  // Title text style
  txtWeekly: {
    color: Colors.BLACK,
    fontSize: 14,
  },

  // Main container style
  container: {
    marginHorizontal: Spacing.SPACE_16,
    marginTop: Spacing.SPACE_24,
  },

  // Icon styles
  icCalendarSchedule: {
    width: 24,
    height: 24,
    marginRight: 10,
  },
});
