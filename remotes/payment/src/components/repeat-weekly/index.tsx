/**
 * RepeatWeekly Component
 *
 * A component that allows users to select days of the week for recurring tasks.
 * It displays a toggle switch and day selection buttons when enabled.
 */
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { Animated, TextStyle, ViewStyle } from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome5';
import { iconCalendarSchedule } from '@assets/images';
import {
  AlertHolder,
  AnimationHelpers,
  BlockView,
  Colors,
  Switch,
  CText,
  DateTimeHelpers,
  FastImage,
  TouchableOpacity,
  TypeFormatDate,
} from '@btaskee/design-system';

import { MarkdownList } from '@components';
import { useI18n } from '@hooks';

import { styles } from './styles';

interface RepeatWeeklyProps {
  weeklyRepeater?: number[];
  isEnabled?: boolean;
  isHideWeekly?: boolean;
  disabled?: boolean;
  timezone?: string;
  title?: string;
  titleStyle?: TextStyle;
  contentStyle?: ViewStyle;
  style?: ViewStyle;
  message?: string[];
  onChange?: (days: number[]) => void;
  setEnabled: (enabled: boolean) => Promise<void>;
}

const daysOfWeek = [0, 1, 2, 3, 4, 5, 6]; // by locale, Sunday is 0 and Saturday is 6

/**
 * RepeatWeekly component for selecting recurring days of the week
 */
const RepeatWeekly: React.FC<RepeatWeeklyProps> = (props) => {
  const { t } = useI18n();
  const [selectedValue, setSelectedValue] = useState<number[]>(
    props?.weeklyRepeater || [],
  );
  const [isVisible, setIsVisible] = useState<boolean>(
    Boolean(props?.isEnabled),
  );

  // Simple fade animation
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (props?.weeklyRepeater) {
      setSelectedValue(props?.weeklyRepeater);
    }
  }, [props?.weeklyRepeater]);

  // Handle visibility changes with simple fade animation
  useEffect(() => {
    if (isVisible) {
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 400,
        useNativeDriver: true,
      }).start();
    } else {
      fadeAnim.setValue(0);
    }
  }, [isVisible, fadeAnim]);

  /**
   * Handles day selection
   */
  const handleClick = useCallback(
    (day: number) => () => {
      // Simple layout animation
      AnimationHelpers.runLayoutAnimation('easeInEaseOut');

      setSelectedValue((prevSelected) => {
        const newSelected = [...prevSelected];
        const index = newSelected.indexOf(day);

        if (index > -1) {
          newSelected.splice(index, 1);
        } else {
          newSelected.push(day);
        }

        const sortedSelected = newSelected.sort();
        props?.onChange?.(sortedSelected);
        return sortedSelected;
      });
    },
    [props?.onChange],
  );

  /**
   * Shows an alert explaining weekly repeating
   */
  const showAlertQuestion = useCallback(() => {
    return AlertHolder.alert.open({
      title: t('WEEKLY_REPEATER_IT_MEAN_TITLE'),
      message: <MarkdownList data={props?.message} />,
      actions: [
        {
          text: t('CANCEL'),
          style: 'cancel',
          onPress: async () => {
            await props?.setEnabled(false);
            setIsVisible(false);
          },
        },
        { text: t('OK') },
      ],
    });
  }, [t, props?.message, props.setEnabled]);

  /**
   * Shows an informational alert about weekly repeating
   */
  const showIntro = useCallback(() => {
    AlertHolder.alert.open({
      title: t('WEEKLY_REPEATER_IT_MEAN_TITLE'),
      message: <MarkdownList data={props?.message} />,
      actions: [{ text: t('UNDERSTOOD') }],
    });
  }, [t, props?.message]);

  /**
   * Handles the switch toggle
   */
  const handleSwitchChange = useCallback(
    async (checked: boolean) => {
      // Simple layout animation
      AnimationHelpers.runLayoutAnimation('easeInEaseOut', 400);

      await props?.setEnabled(checked);
      setIsVisible(checked);
      if (checked) {
        showAlertQuestion();
      }
    },
    [props?.setEnabled, showAlertQuestion],
  );

  /**
   * Renders the toggle switch
   */
  const renderSwitch = useMemo(() => {
    if (props?.isHideWeekly) {
      return null;
    }
    return (
      <Switch
        testID="cbWeeklyRepeater"
        value={isVisible}
        onValueChange={handleSwitchChange}
      />
    );
  }, [props?.isHideWeekly, isVisible, handleSwitchChange]);

  /**
   * Renders the day selection buttons
   */
  const shouldRenderDayOfWeek = useMemo(() => {
    if (!isVisible) {
      return null;
    }

    return (
      <Animated.View
        style={[styles.wrapper, props?.contentStyle, { opacity: fadeAnim }]}
      >
        {daysOfWeek.map((day, index) => {
          const active = selectedValue.includes(day);

          return (
            <TouchableOpacity
              key={index}
              disabled={props?.disabled}
              testID={`DayOfWeek${index}`}
              onPress={handleClick(day)}
            >
              <BlockView
                style={[
                  styles.wrapperButton,
                  active ? styles.backgroundActive : {},
                ]}
              >
                <CText
                  bold
                  style={[styles.txtDay, active ? styles.textActive : {}]}
                >
                  {DateTimeHelpers.formatToString({
                    timezone: props?.timezone,
                    date: DateTimeHelpers.toDayTz({
                      timezone: props?.timezone,
                    }).day(day),
                    typeFormat: TypeFormatDate.DayAbbreviated,
                  })}
                </CText>
              </BlockView>
            </TouchableOpacity>
          );
        })}
      </Animated.View>
    );
  }, [
    isVisible,
    selectedValue,
    props?.timezone,
    props?.disabled,
    props?.contentStyle,
    handleClick,
    fadeAnim,
  ]);

  /**
   * Renders the title and info button
   */
  const shouldRenderButtonShowInfo = useMemo(() => {
    return (
      <BlockView
        row
        style={styles.left}
      >
        <FastImage
          source={iconCalendarSchedule}
          style={styles.icCalendarSchedule}
        />
        <CText
          style={[props?.titleStyle ? props?.titleStyle : styles.txtWeekly]}
        >
          {props?.title ? props?.title : t('POST_TASK_CHECKBOX_REPEAT')}
        </CText>

        {!props?.isHideWeekly && (
          <TouchableOpacity
            testID="whatIsWeekly"
            onPress={showIntro}
            style={styles.btnInfo}
            hitSlop={{ top: 5, bottom: 5, left: 5, right: 5 }}
          >
            <Icon
              name="question-circle"
              size={12}
              color={Colors.SECONDARY_COLOR}
            />
          </TouchableOpacity>
        )}
      </BlockView>
    );
  }, [props?.isHideWeekly, props?.title, props?.titleStyle, showIntro, t]);

  return (
    <BlockView style={[styles.container, props?.style]}>
      <BlockView
        row
        style={styles.wrap_weekly}
      >
        {shouldRenderButtonShowInfo}
        {renderSwitch}
      </BlockView>
      {shouldRenderDayOfWeek}
    </BlockView>
  );
};

export default React.memo(RepeatWeekly);
