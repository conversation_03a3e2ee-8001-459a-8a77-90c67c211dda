/**
 * ModalChooseSchedule Component
 *
 * A modal component that allows users to select time slots from a tasker's available schedule.
 * It displays available time slots organized by time of day (morning, afternoon, evening)
 * and allows users to select up to a maximum number of options.
 */
import React, {
  createRef,
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from 'react';
import { ScrollView } from 'react-native';
import {
  icArrowLeft,
  icArrowRight,
  icClock,
  iconCalendar1,
  iconCloseModal,
} from '@assets/images';
import {
  AnimationHelpers,
  BlockView,
  CModal,
  CModalHandle,
  Colors,
  ConditionView,
  CText,
  DateTimeHelpers,
  DeviceHelper,
  FastImage,
  HitSlop,
  IDate,
  ITimezone,
  PostTaskHelpers,
  TouchableOpacity,
  TypeFormatDate,
} from '@btaskee/design-system';
import { useI18n } from '@src/hooks';
import { capitalize, debounce, isEmpty } from 'lodash-es';

import styles from './styles';

const MAX_OPTIONS = 3;

/**
 * Enum representing different times of day for scheduling
 */
enum TimeOfDay {
  MORNING = 'morning',
  AFTERNOON = 'afternoon',
  EVENING = 'evening',
}

/**
 * Interface for schedule data organized by time of day
 */
interface ISchedule {
  [TimeOfDay.MORNING]?: IDate[];
  [TimeOfDay.AFTERNOON]?: IDate[];
  [TimeOfDay.EVENING]?: IDate[];
}

/**
 * Interface for a selected time slot
 */
export interface SelectedTime {
  date: IDate;
}

/**
 * Interface for the modal ref methods
 */
export interface ModalChooseScheduleRef {
  open: (isConflictTime?: boolean) => void;
  close: () => void;
}

/**
 * Props for the ModalChooseSchedule component
 */
interface IModalScheduleProps {
  dateDefault: IDate;
  timezone: ITimezone;
  onConfirmSchedule?: (timeAskerSelected: SelectedTime[]) => void;
}

/**
 * Props for the ItemSchedule component
 */
interface ItemScheduleProps {
  schedule?: IDate[];
  title: string;
  disableBorderBottom?: boolean;
  timeAskerSelected: SelectedTime[];
  timezone: ITimezone;
  onChooseTime?: (time: IDate) => void;
}

/**
 * Props for the ItemScheduleSelected component
 */
interface ItemScheduleSelectedProps {
  timeSelected: IDate;
  timezone: ITimezone;
  removeTime: (time: IDate) => void;
}

/**
 * Component to display available time slots for a specific time of day
 */
const ItemSchedule: React.FC<ItemScheduleProps> = ({
  schedule,
  title,
  disableBorderBottom,
  timeAskerSelected = [],
  timezone,
  onChooseTime,
}) => {
  if (isEmpty(schedule)) {
    return null;
  }

  return (
    <BlockView
      style={[styles.wrapSchedule, !disableBorderBottom && styles.borderBottom]}
    >
      <CText bold>{title}</CText>
      <BlockView
        row
        style={styles.containerTime}
      >
        {schedule?.map((date) => {
          const isActive = timeAskerSelected.find((item) =>
            DateTimeHelpers.checkIsSame({
              timezone,
              firstDate: item?.date,
              secondDate: date,
            }),
          );

          return (
            <TouchableOpacity
              onPress={() =>
                onChooseTime?.(
                  DateTimeHelpers.formatToString({
                    timezone,
                    date,
                  }),
                )
              }
              key={String(date)}
              style={[styles.wrapTime, !!isActive && styles.timeActive]}
            >
              <CText color={isActive ? Colors.WHITE : Colors.BLACK}>
                {DateTimeHelpers.formatToString({
                  timezone,
                  date,
                  typeFormat: TypeFormatDate.TimeHourMinute,
                })}
              </CText>
            </TouchableOpacity>
          );
        })}
      </BlockView>
    </BlockView>
  );
};

/**
 * Component to display a selected time slot with details
 */
const ItemScheduleSelected: React.FC<ItemScheduleSelectedProps> = ({
  timeSelected,
  timezone,
  removeTime,
}) => {
  // Fixed duration value - could be moved to props if needed
  const duration = 3;

  const durationFormat = useMemo(
    () =>
      PostTaskHelpers.formatHourDuration({
        timezone,
        duration,
        taskDate: timeSelected,
      }),
    [timezone, timeSelected],
  );

  const formattedDate = useMemo(
    () =>
      capitalize(
        DateTimeHelpers.formatToString({
          timezone,
          date: timeSelected,
          typeFormat: TypeFormatDate.DateFullWithDay,
        }),
      ),
    [timezone, timeSelected],
  );

  const gmt = useMemo(
    () => DateTimeHelpers.getGMTByCompareTzDefault(timezone),
    [timezone],
  );

  const taskTime = useMemo(() => {
    let time = `${durationFormat.from} - ${durationFormat.to}`;
    if (gmt) {
      time = `${time}\n${gmt}`;
    }
    return time;
  }, [durationFormat, gmt]);

  return (
    <BlockView
      row
      jBetween
      key={String(timeSelected)}
      style={styles.containerScheduleSelected}
    >
      <BlockView flex>
        <BlockView
          row
          jBetween
        >
          <BlockView
            style={styles.wrapTaskTime}
            row
          >
            <FastImage
              source={icClock}
              style={styles.icon}
            />
            <CText style={styles.txtTaskTime}>{taskTime}</CText>
          </BlockView>
          <BlockView
            style={styles.wrapTaskTime}
            row
          >
            <FastImage
              source={iconCalendar1}
              style={styles.icon}
            />
            <CText style={styles.txtTaskTime}>{formattedDate}</CText>
          </BlockView>
        </BlockView>
      </BlockView>
      <BlockView center>
        <TouchableOpacity
          onPress={() => removeTime(timeSelected)}
          style={styles.iconCloseContainer}
        >
          <FastImage
            tintColor={Colors.WHITE}
            source={iconCloseModal}
            style={styles.icon}
          />
        </TouchableOpacity>
      </BlockView>
    </BlockView>
  );
};

/**
 * Main modal component for choosing schedule
 */
const ModalChooseSchedule = forwardRef<
  ModalChooseScheduleRef,
  IModalScheduleProps
>(({ dateDefault, timezone, onConfirmSchedule }, ref) => {
  const modalRef = createRef<CModalHandle>();
  const { t } = useI18n();

  const [timeAskerSelected, setTimeAskerSelected] = useState<SelectedTime[]>(
    [],
  );
  const [date, setDate] = useState<IDate>(dateDefault);
  const [scheduleOfTasker, setScheduleOfTasker] = useState<ISchedule>({});

  /**
   * Handles confirmation of selected time slots
   */
  const onConfirmSelectTime = useCallback(
    debounce(() => {
      onConfirmSchedule?.(timeAskerSelected);
    }, 400),
    [onConfirmSchedule, timeAskerSelected],
  );

  /**
   * Closes the modal
   */
  const _handleClose = useCallback(() => {
    modalRef?.current?.close?.();
  }, [modalRef]);

  /**
   * Opens the modal
   */
  const _handleOpen = useCallback(() => {
    modalRef?.current?.open?.();
  }, [modalRef]);

  /**
   * Exposes methods to parent component via ref
   */
  useImperativeHandle(ref, () => ({
    // When Tasker has a time conflict, reset DateOptions so Asker can choose a new time
    open(isConflictTime: boolean = false) {
      if (isConflictTime) {
        setTimeAskerSelected([]);
      }
      _handleOpen();
    },
    close() {
      _handleClose();
    },
  }));

  /**
   * Handles selection of a time slot
   */
  const onChooseTime = useCallback(
    (time: IDate) => {
      AnimationHelpers.runLayoutAnimation();

      // Check if time is already selected
      const timeIndex = timeAskerSelected.findIndex((item) =>
        DateTimeHelpers.checkIsSame({
          timezone,
          firstDate: item?.date,
          secondDate: time,
        }),
      );

      // If already selected, remove it
      if (timeIndex > -1) {
        setTimeAskerSelected((result) => {
          return result.filter(
            (item) =>
              !DateTimeHelpers.checkIsSame({
                timezone,
                firstDate: item?.date,
                secondDate: time,
              }),
          );
        });
        return;
      }

      // If max options reached, don't add more
      if (timeAskerSelected.length >= MAX_OPTIONS) {
        return;
      }

      // Add the new time
      setTimeAskerSelected((result) => [...result, { date: time }]);
    },
    [timeAskerSelected, timezone],
  );

  /**
   * Fetches available time slots for a given date
   */
  const getFreeTime = useCallback(
    async (dateTime: IDate) => {
      // Mock implementation - would be replaced with actual API call
      // Example of how the real implementation would work:
      /*
      const params = {
        lat: address?.lat,
        lng: address?.lng,
        taskerId: forceTasker?._id,
        taskDate: DateTimeHelpers.formatToString({ timezone, date: dateTime }),
        serviceId: service?._id,
        duration: duration,
        timezone,
      };
      const result = await getTaskerFreeTime(params);
      if (result?.isSuccess) {
        setScheduleOfTasker(result?.data || {});
      }
      */

      // For now, set mock data
      setScheduleOfTasker({
        morning: [],
        afternoon: [],
        evening: [],
      });
    },
    [timezone],
  );

  /**
   * Initialize with default date
   */
  useEffect(() => {
    if (dateDefault) {
      setDate(dateDefault);
      getFreeTime(dateDefault);
    }
  }, [dateDefault, getFreeTime]);

  /**
   * Navigate to previous day
   */
  const onBackDay = useCallback(() => {
    const backDay = DateTimeHelpers.toDateTz({ timezone, date }).subtract(
      1,
      'day',
    );
    getFreeTime(backDay);
    setDate(backDay);
  }, [date, getFreeTime, timezone]);

  /**
   * Navigate to next day
   */
  const onNextDay = useCallback(() => {
    const nextDay = DateTimeHelpers.toDateTz({ timezone, date }).add(1, 'day');
    getFreeTime(nextDay);
    setDate(nextDay);
  }, [date, getFreeTime, timezone]);

  /**
   * Removes a selected time slot
   */
  const removeTime = useCallback(
    (time: IDate) => {
      AnimationHelpers.runLayoutAnimation();

      setTimeAskerSelected((result) => {
        return result.filter(
          (item) =>
            !DateTimeHelpers.checkIsSame({
              timezone,
              firstDate: item?.date,
              secondDate: time,
            }),
        );
      });
    },
    [timezone],
  );

  /**
   * Formatted date for display
   */
  const formattedDate = useMemo(
    () =>
      capitalize(
        DateTimeHelpers.formatToString({
          timezone,
          date,
          typeFormat: TypeFormatDate.DateFullWithDay,
        }),
      ),
    [date, timezone],
  );

  /**
   * Determines if back navigation should be enabled
   */
  const canGoBack = useMemo(
    () =>
      DateTimeHelpers.diffDate({ timezone, firstDate: date, unit: 'days' }) > 0,
    [date, timezone],
  );

  /**
   * Determines if forward navigation should be enabled
   */
  const canGoForward = useMemo(
    () =>
      DateTimeHelpers.diffDate({ timezone, firstDate: date, unit: 'days' }) < 6,
    [date, timezone],
  );

  return (
    <CModal
      hideButtonClose
      ref={modalRef}
      avoidKeyboard
      title={t('FAV_TASKER.TASKER_WORKING_SCHEDULE')}
      actions={[
        {
          text: t('CLOSE'),
          style: 'cancel',
          onPress: _handleClose,
        },
        {
          text: t('CONFIRM'),
          onPress: onConfirmSelectTime,
          disabled: isEmpty(timeAskerSelected),
        },
      ]}
    >
      <ScrollView
        bounces={false}
        style={{ maxHeight: DeviceHelper.WINDOW.HEIGHT * 0.65 }}
        showsVerticalScrollIndicator={false}
      >
        <BlockView
          flex
          row
          style={styles.header}
        >
          <BlockView
            center
            style={styles.wrapIcon}
          >
            <ConditionView
              condition={canGoBack}
              viewTrue={
                <TouchableOpacity
                  onPress={onBackDay}
                  hitSlop={HitSlop.MEDIUM}
                >
                  <FastImage
                    source={icArrowLeft}
                    style={styles.icon}
                  />
                </TouchableOpacity>
              }
            />
          </BlockView>

          <BlockView center>
            <CText bold>{formattedDate}</CText>
          </BlockView>

          <BlockView
            center
            style={styles.wrapIcon}
          >
            <ConditionView
              condition={canGoForward}
              viewTrue={
                <TouchableOpacity
                  onPress={onNextDay}
                  hitSlop={HitSlop.MEDIUM}
                >
                  <FastImage
                    source={icArrowRight}
                    style={styles.icon}
                  />
                </TouchableOpacity>
              }
            />
          </BlockView>
        </BlockView>

        <BlockView style={styles.scheduleContainer}>
          <ConditionView
            condition={Boolean(scheduleOfTasker?.morning?.length)}
            viewTrue={
              <ItemSchedule
                timeAskerSelected={timeAskerSelected}
                onChooseTime={onChooseTime}
                schedule={scheduleOfTasker?.morning}
                title={t('FAV_TASKER.SCHEDULE_OF_MORNING')}
                timezone={timezone}
              />
            }
          />
          <ConditionView
            condition={Boolean(scheduleOfTasker?.afternoon?.length)}
            viewTrue={
              <ItemSchedule
                timeAskerSelected={timeAskerSelected}
                onChooseTime={onChooseTime}
                schedule={scheduleOfTasker?.afternoon}
                title={t('FAV_TASKER.SCHEDULE_OF_AFTERNOON')}
                timezone={timezone}
              />
            }
          />
          <ConditionView
            condition={Boolean(scheduleOfTasker?.evening?.length)}
            viewTrue={
              <ItemSchedule
                timeAskerSelected={timeAskerSelected}
                onChooseTime={onChooseTime}
                schedule={scheduleOfTasker?.evening}
                title={t('FAV_TASKER.SCHEDULE_OF_EVENING')}
                timezone={timezone}
                disableBorderBottom
              />
            }
          />
        </BlockView>

        <ConditionView
          condition={Boolean(timeAskerSelected.length)}
          viewTrue={
            <>
              {timeAskerSelected?.map((item) => (
                <ItemScheduleSelected
                  key={String(item?.date)}
                  timeSelected={item?.date}
                  timezone={timezone}
                  removeTime={removeTime}
                />
              ))}
            </>
          }
        />
      </ScrollView>
    </CModal>
  );
});

export default ModalChooseSchedule;
