import React from 'react';
import { BlockView, CText } from '@btaskee/design-system';

import { useI18n } from '@hooks';

import styles from '../../screens/confirm-and-payment/styles';
import Detail from './detail';

const TaskDetail = () => {
  const { t } = useI18n();

  return (
    <BlockView>
      <BlockView style={styles.panel}>
        <CText
          h4
          testID="infoTask"
          bold
          style={styles.txtPanel}
        >
          {t('TASK_INFO')}
        </CText>
      </BlockView>
      <Detail />
    </BlockView>
  );
};

export default TaskDetail;
