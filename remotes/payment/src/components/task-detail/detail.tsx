/**
 * Component that displays detailed information about the cleaning task
 * including duration, workload, and other service-specific details.
 */
import React, { useMemo } from 'react';
import {
  BlockView,
  Card,
  ConditionView,
  CText,
  usePostTaskStore,
} from '@btaskee/design-system';
import { getDurationByCountry } from '@helper';
import { get, isEmpty } from 'lodash-es';

import { useI18n } from '@hooks';
import styles from '@screens/confirm-and-payment/styles';

import TaskerFavoriteForRebook from '../tasker-favorite-for-rebook';
import Note from './note';
import WorkingTime from './working-time';

/**
 * Displays detailed information about the cleaning task
 */
const Detail: React.FC = () => {
  const { t, i18n } = useI18n();
  const {
    forceTasker,
    date,
    requirements,
    duration,
    note,
    pet,
    isPremium,
    gender,
    timezone,
    schedule,
    isEnabledSchedule,
  } = usePostTaskStore();

  const DURATIONS = getDurationByCountry();

  /**
   * Renders workload information based on selected duration
   */
  const shouldRenderWorkload = useMemo(() => {
    if (!duration) return null;

    const workload = DURATIONS.find((e: any) => e.duration === duration);
    if (!workload) {
      return null;
    }

    return (
      <BlockView style={styles.group}>
        <CText style={styles.txtVLabel}>{t('WORKLOAD')}</CText>
        <CText style={styles.txtValue}>
          {`${workload?.area ?? ''} / ${t(workload?.rooms ?? '', {
            t: workload?.duration,
          })}`}
        </CText>
      </BlockView>
    );
  }, [duration, t, DURATIONS]);

  /**
   * Renders pet information if applicable
   */
  const shouldRenderPet = useMemo(() => {
    if (pet && Array.isArray(pet) && pet.length > 0) {
      const pets = pet
        .map((pe) => {
          if (pe?.name) {
            return t(pe.name);
          }
          if (pe?.other) {
            return pe.other;
          }
          return '';
        })
        .filter(Boolean)
        .join(', ');
      if (!pets) return null;
      return (
        <BlockView style={styles.group}>
          <CText style={styles.txtVLabel}>{t('HAVE_PET')}</CText>
          <CText
            testID="havePet"
            style={styles.txtValue}
            numberOfLines={1}
          >
            {pets}
          </CText>
        </BlockView>
      );
    }
    return null;
  }, [pet, t]);

  /**
   * Renders gender preference if applicable
   */
  const shouldRenderGender = useMemo(() => {
    if (!gender) return null;

    return (
      <BlockView style={styles.group}>
        <CText style={styles.txtVLabel}>{t('GENDER')}</CText>
        <CText style={styles.txtValue}>{t(gender)}</CText>
      </BlockView>
    );
  }, [gender, t]);
  const shouldRenderAddOnService = useMemo(() => {
    if (
      requirements &&
      Array.isArray(requirements) &&
      requirements.length > 0
    ) {
      const locale = i18n.language || 'vi';
      const addOnText = requirements
        .map((ser) => get(ser, `text[${locale}]`, ''))
        .filter(Boolean)
        .join(', ');
      if (!addOnText) return null;
      return (
        <BlockView style={styles.group}>
          <CText style={styles.txtVLabel}>{t('ADD_ON_SERVICE')}</CText>
          <CText
            testID="onService"
            numberOfLines={2}
            style={styles.txtValue}
          >
            {addOnText}
          </CText>
        </BlockView>
      );
    }
    return null;
  }, [requirements, i18n.language, t]);
  /**
   * Renders premium service information if applicable
   */
  const shouldRenderIsPremium = useMemo(() => {
    if (isPremium === true) {
      return (
        <BlockView style={styles.group}>
          <CText style={styles.txtVLabel}>{t('IS_PREMIUM')}</CText>
          <CText
            testID="isPremium"
            style={styles.txtValue}
          >
            {t('TASK_PREMIUM')}
          </CText>
        </BlockView>
      );
    }
    return null;
  }, [isPremium, t]);

  return (
    <Card>
      <ConditionView
        condition={!!forceTasker && !isEmpty(forceTasker)}
        viewTrue={
          <BlockView style={styles.wrapTaskerFavorite}>
            <TaskerFavoriteForRebook forceTasker={forceTasker} />
          </BlockView>
        }
      />
      <WorkingTime
        date={date ?? ''}
        schedule={schedule ?? []}
        duration={duration ?? 0}
        isEnableSchedule={!!isEnabledSchedule}
        timezone={timezone ?? ''}
      />

      <BlockView style={styles.wrapDetail}>
        <CText
          bold
          style={styles.subPanel}
        >
          {t('TASK_DETAIL')}
        </CText>
        {shouldRenderWorkload}
        {shouldRenderIsPremium}
        {shouldRenderAddOnService}
        {shouldRenderPet}
        {shouldRenderGender}
        <Note note={note ?? ''} />
      </BlockView>
    </Card>
  );
};

export default Detail;
