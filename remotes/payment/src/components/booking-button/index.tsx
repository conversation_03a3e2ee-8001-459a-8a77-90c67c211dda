/**
 * Component that renders the booking button at the bottom of the confirm booking screen.
 * Handles authentication state and booking actions.
 */
import React, { useCallback, useMemo } from 'react';
import { useAuth } from '@btaskee/auth-store';
import {
  BlockView,
  PrimaryButton,
  SizedBox,
  Spacing,
} from '@btaskee/design-system';
import { useFocusEffect } from '@react-navigation/native';
import { IPrice } from '@types';

import { useI18n } from '@hooks';

import Price from './price';
import { styles } from './styles';

interface BookingButtonProps {
  isLoading?: boolean;
  onPostTask: () => void;
  price: IPrice | null;
  navigation: any; // TODO: Replace with proper navigation type
}

/**
 * Button component for confirming booking with price display
 */
const BookingButton: React.FC<BookingButtonProps> = ({
  onPostTask,
  price,
  navigation,
}) => {
  const { t } = useI18n();
  const { token, userId, rehydrate } = useAuth();

  // Refresh auth state when screen is focused
  useFocusEffect(
    useCallback(() => {
      if (rehydrate) {
        rehydrate();
      }
    }, [rehydrate]),
  );

  /**
   * Determines the button text based on authentication state
   */
  const titleBookingButton = useMemo(() => {
    if (!token || !userId) {
      return t('SIGN_UP_NOW');
    }
    return t('BUTTON_BOOKING');
  }, [token, userId, t]);

  /**
   * Handles button press - either navigates to auth or proceeds with booking
   */
  const onPressBookingButton = useCallback(() => {
    if (!token || !userId) {
      navigation.navigate('Auth', {
        isGoBack: true,
      });
      return;
    }
    onPostTask();
  }, [token, userId, navigation, onPostTask]);

  return (
    <BlockView
      inset={['bottom']}
      style={styles.container}
    >
      <Price price={price} />
      <SizedBox height={Spacing.SPACE_16} />
      <PrimaryButton
        testID="btnBooking"
        title={titleBookingButton}
        onPress={onPressBookingButton}
      />
    </BlockView>
  );
};

export default BookingButton;
