import { StyleSheet } from 'react-native';
import { Colors, Spacing } from '@btaskee/design-system';

export const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: Colors.WHITE,
    paddingHorizontal: Spacing.SPACE_16,
    borderTopWidth: 1,
    borderTopColor: Colors.BORDER_COLOR,
  },
  pricePanel: {
    marginTop: Spacing.SPACE_12,
    paddingHorizontal: Spacing.SPACE_16,
    justifyContent: 'space-between',
  },
  txtPrice: {
    textAlign: 'right',
    color: Colors.BLACK,
  },
  txtPromotion: {
    textDecorationColor: Colors.PRIMARY_COLOR,
    textDecorationLine: 'line-through',
    color: Colors.PRIMARY_COLOR,
    textAlign: 'right',
  },
  txtTotal: {
    color: Colors.BLACK,
  },
});
