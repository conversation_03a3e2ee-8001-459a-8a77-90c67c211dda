import React from 'react';
import {
  BlockView,
  CText,
  formatMoney,
  usePostTaskStore,
} from '@btaskee/design-system';
import {} from '@helper';
import { IPrice } from '@types';

import { useI18n } from '@hooks';

import { styles } from './styles';

interface PriceProps {
  price: IPrice | null;
}

const Price: React.FC<PriceProps> = ({ price }) => {
  const { t } = useI18n();
  const { currency } = usePostTaskStore();

  if (!price) {
    return null;
  }

  const priceText = t('COST_AND_CURRENCY', {
    cost: formatMoney(price.finalCost),
    currency: currency?.sign,
  });

  const hasPromotion = (price?.cost || 0) > (price?.finalCost || 0);

  const originPriceText = hasPromotion
    ? t('COST_AND_CURRENCY', {
        cost: formatMoney(price.cost),
        currency: currency?.sign,
      })
    : '';

  return (
    <BlockView
      row
      style={styles.pricePanel}
    >
      <BlockView>
        <CText
          bold
          h4
          style={styles.txtTotal}
        >
          {t('TOTAL')}
        </CText>
      </BlockView>
      <BlockView>
        {hasPromotion && (
          <CText
            testID="originPrice"
            style={styles.txtPromotion}
          >
            {originPriceText}
          </CText>
        )}
        <CText
          testID="price"
          bold
          h4
          style={styles.txtPrice}
        >
          {priceText}
        </CText>
      </BlockView>
    </BlockView>
  );
};

export default Price;
