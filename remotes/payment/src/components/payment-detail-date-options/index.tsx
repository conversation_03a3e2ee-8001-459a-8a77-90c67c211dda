import React from 'react';
import {
  BlockView,
  BorderRadius,
  Colors,
  ConditionView,
  CText,
  formatMoney,
  getFinalCost,
  ITimezone,
  SizedBox,
  Spacing,
  TypeFormatDate,
  usePostTaskStore,
} from '@btaskee/design-system';

import { DateWithGMT } from '@components';
import { useI18n } from '@hooks';

import { RowInfo } from '../row-info';

const PaymentDetailDateOptions = ({
  title,
  value,
  testID,
  timezone,
}: {
  testID?: string;
  title?: string;
  value?: any;
  timezone: ITimezone;
}) => {
  const { t } = useI18n();
  const { currency } = usePostTaskStore();

  const finalCost = getFinalCost(value?.costDetail);
  const promotionCost = (value?.costDetail?.cost || 0) - finalCost;

  return (
    <BlockView
      testID={testID}
      backgroundColor={Colors.BG_COLOR}
      margin={{ top: Spacing.SPACE_16 }}
      padding={{
        horizontal: Spacing.SPACE_16,
        top: Spacing.SPACE_16,
        bottom: Spacing.SPACE_04,
      }}
      border={{ width: 1, color: Colors.BORDER_COLOR }}
      radius={BorderRadius.RADIUS_08}
    >
      <BlockView
        row
        jBetween
      >
        <CText
          bold
          color={Colors.PRIMARY_COLOR}
        >
          {title}
        </CText>
      </BlockView>
      <RowInfo
        label={t('COUNTDOWN_DAY')}
        value={
          <DateWithGMT
            timezone={timezone}
            date={value?.eatingTime || value?.date}
            typeFormat={TypeFormatDate.DateTimeFullWithDay}
            style={{ color: Colors.GREY }}
          />
        }
        styleInfo={{ color: Colors.GREY }}
      />
      <RowInfo
        label={t('PRICE_SERVICE')}
        value={t('COST_AND_CURRENCY', {
          cost: formatMoney(value?.costDetail?.cost),
          currency: currency.sign,
        })}
        styleInfo={{ color: Colors.GREY }}
      />
      <ConditionView
        condition={promotionCost > 0}
        viewTrue={
          <RowInfo
            label={t('FAV_TASKER.VOUCHER_DISCOUNT')}
            value={t('COST_AND_CURRENCY', {
              cost: `-${formatMoney(promotionCost)}`,
              currency: currency.sign,
            })}
            styleInfo={{ color: Colors.GREY }}
          />
        }
      />
      <SizedBox
        height={1}
        color={Colors.BORDER_COLOR}
      />
      <RowInfo
        label={t('TOTAL_PAYMENT')}
        value={t('COST_AND_CURRENCY', {
          cost: formatMoney(finalCost),
          currency: currency.sign,
        })}
        styleInfo={{ color: Colors.GREEN }}
      />
    </BlockView>
  );
};

export default PaymentDetailDateOptions;
