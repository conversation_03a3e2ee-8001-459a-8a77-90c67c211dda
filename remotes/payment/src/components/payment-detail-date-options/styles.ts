import { StyleSheet } from 'react-native';
import {
  BorderRadius,
  Colors,
  DeviceHelper,
  FontFamily,
  FontSizes,
  Spacing,
} from '@btaskee/design-system';

export default StyleSheet.create({
  container: {
    padding: Spacing.SPACE_16,
  },
  imageStyle: {
    width: DeviceHelper.WINDOW.WIDTH / 3,
    height: DeviceHelper.WINDOW.WIDTH / 3,
  },
  contentTxt: {
    color: Colors.BLACK,
    textAlign: 'center',
  },
  txtBtn: {
    fontWeight: 'bold',
    fontFamily: FontFamily.FONT_BOLD,
    fontSize: FontSizes.SIZE_14,
  },
  btn: {
    padding: Spacing.SPACE_04,
  },
  wrapTime: {
    paddingVertical: Spacing.SPACE_04,
    paddingHorizontal: Spacing.SPACE_16,
    borderRadius: BorderRadius.RADIUS_16,
    backgroundColor: Colors.GREY_2,
    marginBottom: Spacing.SPACE_16,
    alignItems: 'center',
    marginHorizontal: Spacing.SPACE_08,
  },
  containerTime: {
    marginTop: Spacing.SPACE_16,
    flexWrap: 'wrap',
  },
  scheduleContainer: {
    // backgroundColor: 'red',
    paddingHorizontal: Spacing.SPACE_16,
    borderRadius: BorderRadius.RADIUS_08,
    borderWidth: 1,
    borderColor: Colors.BORDER_COLOR,
  },
  timeActive: {
    backgroundColor: Colors.SECONDARY_COLOR,
  },
  wrapSchedule: {
    marginTop: Spacing.SPACE_08,
  },
  borderBottom: {
    borderBottomColor: Colors.BORDER_COLOR,
    borderBottomWidth: 1,
  },
  icon: {
    width: 20,
    height: 20,
  },
  header: {
    backgroundColor: Colors.GREY_3,
    marginBottom: Spacing.SPACE_24,
    borderRadius: BorderRadius.RADIUS_08,
    justifyContent: 'space-between',
  },

  wrapIcon: {
    width: 45,
    height: 45,
  },

  containerScheduleSelected: {
    backgroundColor: Colors.GREY_2,
    padding: Spacing.SPACE_16,
    marginTop: Spacing.SPACE_16,
    borderRadius: BorderRadius.RADIUS_08,
  },
  iconCloseContainer: {
    backgroundColor: Colors.GREY,
    borderRadius: BorderRadius.RADIUS_16,
    marginLeft: Spacing.SPACE_16,
  },
  wrapTaskTime: {
    alignItems: 'center',
    marginTop: Spacing.SPACE_04,
    // width: '50%',
  },
  txtTaskTime: {
    marginLeft: Spacing.SPACE_04,
  },
});
