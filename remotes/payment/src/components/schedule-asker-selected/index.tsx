import React, { useCallback, useMemo } from 'react';
import {
  BlockView,
  Colors,
  CText,
  DateTimeHelpers,
  FontSizes,
  ITimezone,
  TouchableOpacity,
  TypeFormatDate,
} from '@btaskee/design-system';
import { capitalize, isEmpty } from 'lodash-es';

import { useI18n } from '@hooks';

import { styles } from './styles';

interface ScheduleItem {
  date: Date;
}

interface ScheduleAskerSelectedProps {
  timezone: ITimezone;
  scheduleSelected?: ScheduleItem[];
  onChangeSchedule: () => void;
}

const ScheduleAskerSelected: React.FC<ScheduleAskerSelectedProps> = ({
  timezone,
  scheduleSelected = [],
  onChangeSchedule,
}) => {
  const { t } = useI18n();
  // Fixed duration value - could be moved to props if needed in the future
  const duration = 3;

  // Memoize the GMT value to avoid recalculating for each schedule item
  const gmt = useMemo(
    () => DateTimeHelpers.getGMTByCompareTzDefault(timezone),
    [timezone],
  );

  // Handle schedule change with useCallback to prevent unnecessary re-renders
  const handleChangeSchedule = useCallback(() => {
    if (onChangeSchedule) {
      onChangeSchedule();
    }
  }, [onChangeSchedule]);

  // Early return if no schedules are selected
  if (isEmpty(scheduleSelected)) return null;

  return (
    <>
      <BlockView style={styles.container}>
        {scheduleSelected.map((item, index) => {
          if (!item?.date) return null;

          // Format the hour text with timezone
          let hourTxt = capitalize(
            DateTimeHelpers.formatToString({
              timezone,
              date: item.date,
              typeFormat: TypeFormatDate.TimeHourMinute,
            }) || '',
          );

          // Calculate and format when tasker will arrive
          let hourTaskerArrive = capitalize(
            DateTimeHelpers.formatToString({
              timezone,
              date: DateTimeHelpers.toDateTz({
                timezone,
                date: item.date,
              })?.subtract(duration, 'hour'),
              typeFormat: TypeFormatDate.TimeHourMinute,
            }) || '',
          );

          // Add GMT information if available
          if (gmt) {
            hourTxt = `${hourTxt} ${gmt}`;
            hourTaskerArrive = `${hourTaskerArrive} ${gmt}`;
          }

          const isNotLastItem = index < scheduleSelected.length - 1;

          return (
            <BlockView
              key={`schedule-item-${index}`}
              style={isNotLastItem ? styles.borderBottom : undefined}
            >
              <CText
                bold
                color={Colors.PRIMARY_COLOR}
                size={FontSizes.SIZE_14}
              >
                {t('FAV_TASKER.OPTION_TIME', { stt: index + 1 })}
              </CText>
              <BlockView
                style={styles.content}
                jBetween
                row
              >
                <CText color={Colors.GREY}>{t('COUNTDOWN_DAY')}</CText>
                <CText>
                  {capitalize(
                    DateTimeHelpers.formatToString({
                      timezone,
                      date: item.date,
                      typeFormat: TypeFormatDate.DateFullWithDay,
                    }) || '',
                  )}
                </CText>
              </BlockView>
              <BlockView
                style={styles.content}
                jBetween
                row
              >
                <CText color={Colors.GREY}>{t('COUNTDOWN_HOUR')}</CText>
                <CText>{hourTxt}</CText>
              </BlockView>
            </BlockView>
          );
        })}
      </BlockView>
      <TouchableOpacity
        onPress={handleChangeSchedule}
        style={styles.btnChange}
      >
        <CText color={Colors.SECONDARY_COLOR}>
          {t('FAV_TASKER.CHANGE_OPTION_TIME')}
        </CText>
      </TouchableOpacity>
    </>
  );
};

export default React.memo(ScheduleAskerSelected);
