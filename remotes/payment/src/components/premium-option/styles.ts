/**
 * Styles for the PremiumOptional component
 */
import { StyleSheet } from 'react-native';
import { Colors, DeviceHelper, Spacing } from '@btaskee/design-system';

export default StyleSheet.create({
  btnInfo: {
    top: -Spacing.SPACE_04,
    paddingLeft: Spacing.SPACE_04,
    paddingRight: Spacing.SPACE_08,
  },
  content: {
    position: 'relative',
    maxHeight: Math.round(DeviceHelper.WINDOW.HEIGHT * 0.6),
  },
  left: {
    flex: 1,
    alignItems: 'center',
  },
  group: {
    alignItems: 'center',
    backgroundColor: Colors.LIGHT_ORANGE_4,
    borderColor: Colors.PRIMARY_COLOR,
    borderWidth: 1,
    borderRadius: Spacing.SPACE_12,
    padding: Spacing.SPACE_16,
  },
  container: {
    marginTop: '2%',
  },
  iconImage: {
    width: Spacing.SPACE_32,
    height: Spacing.SPACE_32,
  },
  txtLabel: {
    fontWeight: 'bold',
    marginLeft: Spacing.SPACE_16,
  },
  containerModal: {
    margin: 0,
    padding: 0,
  },
});
