/**
 * Styles for the ModalDescriptionPremium component
 */
import { StyleSheet } from 'react-native';
import { DeviceHelper, Spacing } from '@btaskee/design-system';

// Calculate height for premium image banner
const HEIGHT_FLAG = DeviceHelper.WINDOW.WIDTH / 2;

export default StyleSheet.create({
  content: {
    position: 'relative',
    maxHeight: Math.round(DeviceHelper.WINDOW.HEIGHT * 0.6),
  },
  txtPanel: {
    marginTop: Spacing.SPACE_16,
    marginBottom: Spacing.SPACE_24,
    fontSize: Spacing.SPACE_24,
  },
  blockImage: {
    position: 'absolute',
    top: -HEIGHT_FLAG,
    marginHorizontal: -Spacing.SPACE_08,
    marginBottom: Spacing.SPACE_16,
  },
  blockIcon: {
    marginHorizontal: Spacing.SPACE_08,
    marginBottom: Spacing.SPACE_16,
    flex: 1,
  },
  ImageDescription: {
    width: DeviceHelper.WINDOW.WIDTH,
    height: HEIGHT_FLAG,
  },
  lineInfo: {
    justifyContent: 'center',
    alignItems: 'stretch',
    flexDirection: 'row',
  },
  iconStar: {
    width: Spacing.SPACE_24,
    height: Spacing.SPACE_24,
  },
  contentPremiumStyle: {
    flex: 12,
    fontSize: Spacing.SPACE_16,
    marginBottom: Spacing.SPACE_24,
  },
  scrollArea: {
    paddingBottom: Spacing.SPACE_24,
    marginBottom: Spacing.SPACE_24,
  },
  blockCancel: {
    position: 'absolute',
    top: 15,
    right: 15,
  },
  iconCancel: {
    width: Spacing.SPACE_24,
    height: Spacing.SPACE_24,
  },
});
