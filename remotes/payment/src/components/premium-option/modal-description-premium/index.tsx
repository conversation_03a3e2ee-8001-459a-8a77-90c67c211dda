/**
 * ModalDescriptionPremium Component
 *
 * A component that displays detailed information about premium service options.
 * It shows different content based on the user's country (isoCode).
 */
import React, { useMemo } from 'react';
import { ScrollView } from 'react-native';
import {
  iconCloseModal,
  iconStarOrange,
  imagePremiumDetail,
} from '@assets/images';
import {
  BlockView,
  CText,
  FastImage,
  ISO_CODE,
  Spacing,
  TouchableOpacity,
  usePostTaskStore,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';

import styles from './styles';

interface IModalContentProps {
  _handleClose: () => void;
}

/**
 * ModalDescriptionPremium component that displays detailed information
 * about premium service options with country-specific content.
 */
export const ModalDescriptionPremium: React.FC<IModalContentProps> = ({
  _handleClose,
}) => {
  const { t } = useI18n();
  const { isoCode } = usePostTaskStore();

  /**
   * Render premium content description based on country code
   */
  const shouldRenderInfoModal = useMemo(() => {
    // Define content descriptions for different countries
    const PREMIUM_CONTENT_DESCRIPTION = {
      [ISO_CODE.VN]: [t('PREMIUM_DETAIL_2'), t('PREMIUM_DETAIL_3')],
      [ISO_CODE.TH]: [
        t('PREMIUM.DESCRIPTION_1'),
        t('PREMIUM.DESCRIPTION_2'),
        t('PREMIUM.DESCRIPTION_3'),
      ],
      [ISO_CODE.ID]: [
        t('PREMIUM.DESCRIPTION_1'),
        t('PREMIUM.DESCRIPTION_2'),
        t('PREMIUM.DESCRIPTION_3'),
      ],
      [ISO_CODE.MY]: [
        t('PREMIUM.DESCRIPTION_1'),
        t('PREMIUM.DESCRIPTION_2'),
        t('PREMIUM.DESCRIPTION_3'),
      ],
    };

    // Use default content if country code doesn't match
    const contentDescription =
      PREMIUM_CONTENT_DESCRIPTION?.[isoCode] ||
      PREMIUM_CONTENT_DESCRIPTION[ISO_CODE.VN];

    // Map content to UI elements
    return contentDescription.map((text) => (
      <BlockView
        flex
        margin={{ bottom: Spacing.SPACE_12 }}
        key={text}
        testID={text}
        row
        style={styles.lineInfo}
      >
        <FastImage
          source={iconStarOrange}
          style={styles.iconStar}
        />
        <CText
          flex
          margin={{ left: Spacing.SPACE_08 }}
        >
          {text}
        </CText>
      </BlockView>
    ));
  }, [isoCode, t]);

  return (
    <BlockView style={styles.content}>
      <BlockView style={styles.blockImage}>
        <FastImage
          resizeMode="cover"
          source={imagePremiumDetail}
          style={styles.ImageDescription}
        />
        <TouchableOpacity
          testID="btnCancelPremium"
          onPress={_handleClose}
          style={styles.blockCancel}
          hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
        >
          <BlockView>
            <FastImage
              resizeMode="cover"
              source={iconCloseModal}
              style={styles.iconCancel}
            />
          </BlockView>
        </TouchableOpacity>
      </BlockView>
      <ScrollView
        showsVerticalScrollIndicator={false}
        style={styles.scrollArea}
        testID="scrollViewPremium"
      >
        <CText
          h4
          bold
          style={styles.txtPanel}
        >
          {t('PREMIUM_DETAIL_1')}
        </CText>
        <BlockView>{shouldRenderInfoModal}</BlockView>
      </ScrollView>
    </BlockView>
  );
};
