/**
 * PremiumOptional Component
 *
 * A component that allows users to toggle premium service options.
 * It displays a premium service option with a description modal that can be opened.
 */
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { TextStyle, ViewStyle } from 'react-native';
import { iconPremiumOptional, icQuestion } from '@assets/images';
import {
  AnimationHelpers,
  BlockView,
  CModal,
  CModalHandle,
  Colors,
  CText,
  FastImage,
  IconImage,
  Switch,
  TouchableOpacity,
  usePostTaskStore,
} from '@btaskee/design-system';
import { includes, isEmpty } from 'lodash-es';

import { useI18n } from '@hooks';

import { ModalDescriptionPremium } from './modal-description-premium';
import styles from './styles';

interface IPremiumProps {
  isPremium?: boolean;
  onChangePremium: (value: boolean) => void;
  txtPropStyle?: TextStyle;
  style?: ViewStyle;
}

/**
 * PremiumOptional component that displays a premium service option toggle
 * with a description modal that can be opened for more information.
 */
const PremiumOptional: React.FC<IPremiumProps> = (props) => {
  const { service, address } = usePostTaskStore();
  const { t } = useI18n();

  const modalRef = useRef<CModalHandle>(null);
  const [isPremium, setIsPremium] = useState(Boolean(props.isPremium));

  /**
   * Update local state when props change
   */
  useEffect(() => {
    if (isPremium !== props?.isPremium) {
      setIsPremium(Boolean(props.isPremium));
    }
  }, [props.isPremium, isPremium]);

  /**
   * Hide premium description modal
   */
  const handleClose = useCallback(() => {
    modalRef?.current?.close?.();
  }, []);

  /**
   * Show premium description modal
   */
  const handleOpen = useCallback(() => {
    modalRef?.current?.open?.();
  }, []);

  /**
   * Handle premium toggle switch change
   */
  const onChangeSwitch = useCallback(
    (checked: boolean) => {
      AnimationHelpers.runLayoutAnimation();
      setIsPremium(checked);
      props.onChangePremium(checked);
      // Uncomment to automatically open modal when premium is selected
      // if (checked) {
      //   handleOpen();
      // }
    },
    [props.onChangePremium],
  );

  // Get cities where premium option is available
  const applyForCities = service?.premiumOptions?.applyForCities || [];

  // Don't render if premium is not available for the current city
  if (!includes(applyForCities, address?.city)) {
    return null;
  }

  // Don't render if premium options are not configured or address is missing
  if (isEmpty(service?.premiumOptions) || isEmpty(address)) {
    return null;
  }

  return (
    <>
      <BlockView style={[styles.container, props?.style]}>
        <BlockView>
          <CText
            bold
            style={props.txtPropStyle}
          >
            {t('PREMIUM_TITLE_OPTIONAL')}
          </CText>
        </BlockView>
        <BlockView style={styles.content}>
          <BlockView
            row
            style={styles.group}
          >
            <BlockView
              row
              style={styles.left}
            >
              <BlockView>
                <FastImage
                  source={iconPremiumOptional}
                  style={styles.iconImage}
                />
              </BlockView>
              <CText
                testID="txtChoosePremiumOptional"
                bold
                style={styles.txtLabel}
              >
                {t('PREMIUM_CONTENT_OPTIONAL')}
              </CText>
              <TouchableOpacity
                testID="choosePremiumDescription"
                onPress={handleOpen}
                style={styles.btnInfo}
                hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
              >
                <IconImage
                  source={icQuestion}
                  size={16}
                  color={Colors.SECONDARY_COLOR}
                />
              </TouchableOpacity>
            </BlockView>
            <Switch
              testID="choosePremium"
              value={isPremium}
              onValueChange={onChangeSwitch}
            />
          </BlockView>
        </BlockView>
      </BlockView>
      <CModal
        ref={modalRef}
        hideButtonClose
        contentContainerStyle={styles.containerModal}
      >
        <ModalDescriptionPremium _handleClose={handleClose} />
      </CModal>
    </>
  );
};

export default React.memo(PremiumOptional);
