import React, { useMemo } from 'react';
import { ScrollView } from 'react-native';
import {
  BlockView,
  BorderRadius,
  Colors,
  ConditionView,
  CText,
  DeviceHelper,
  FastImage,
  FontSizes,
  Spacing,
} from '@btaskee/design-system';
import { CLEANING_TOOLS } from '@lib/configs';
import { useI18n } from '@src/hooks';

import { iconPremiumOptional } from '@images';

import styles from './styles';

/**
 * Props for the ModalListTools component
 */
interface ModalListToolsProps {
  isPremiumSelected?: boolean;
  isSupportPremium?: boolean;
}

/**
 * Component that displays a grid of cleaning tools in a modal
 * Shows premium tools based on premium selection status
 */
export const ModalListTools: React.FC<ModalListToolsProps> = ({
  isPremiumSelected = false,
  isSupportPremium = false,
}) => {
  const { t } = useI18n();

  // Process cleaning tools into rows of 3 items for grid display
  // Filter out premium tools if premium is not supported
  const listToolsConverted = useMemo(() => {
    const cleaningTools = isSupportPremium
      ? CLEANING_TOOLS
      : CLEANING_TOOLS.filter((item) => !item.isPremium);

    const rows = [];
    for (let i = 0; i < cleaningTools.length; i += 3) {
      rows.push(cleaningTools.slice(i, i + 3));
    }
    return rows;
  }, [isSupportPremium]);

  // Calculate maximum height for the scrollview based on device height
  const maxHeight = useMemo(
    () => Math.round(DeviceHelper.WINDOW.HEIGHT * 0.6),
    [],
  );

  return (
    <BlockView
      maxHeight={maxHeight}
      margin={{ top: Spacing.SPACE_16 }}
    >
      <ScrollView showsVerticalScrollIndicator={false}>
        <BlockView
          alignSelf="center"
          margin={{ top: Spacing.SPACE_16 }}
        >
          {listToolsConverted.map((row, rowIndex) => (
            <BlockView
              row
              key={`row${rowIndex}`}
            >
              {row.map((item) => {
                const isOnlyPremium = item.isPremium && !isPremiumSelected;
                const testID = isOnlyPremium
                  ? `disabled_tool_premium_${item?.name}`
                  : `enabled_tool_premium_${item?.name}`;

                return (
                  <BlockView
                    testID={testID}
                    key={item?.name}
                  >
                    <BlockView
                      margin={DeviceHelper.WINDOW.WIDTH * 0.01}
                      padding={DeviceHelper.WINDOW.WIDTH * 0.02}
                      radius={BorderRadius.RADIUS_08}
                      border={{
                        width: 1,
                        color: isOnlyPremium
                          ? Colors.PRIMARY_COLOR
                          : Colors.UNDERLAY_COLOR,
                      }}
                    >
                      <FastImage
                        source={item?.image}
                        style={styles.toolImage}
                      />
                    </BlockView>
                    <ConditionView
                      condition={isOnlyPremium}
                      viewTrue={
                        <BlockView
                          row
                          style={styles.wrapPremium}
                        >
                          <FastImage
                            source={iconPremiumOptional}
                            style={styles.icon}
                          />
                          <CText
                            size={FontSizes.SIZE_12}
                            color={Colors.PRIMARY_COLOR}
                          >
                            {t('POST_TASK_STEP_2.PREMIUM')}
                          </CText>
                        </BlockView>
                      }
                    />
                  </BlockView>
                );
              })}
            </BlockView>
          ))}
        </BlockView>
      </ScrollView>
    </BlockView>
  );
};

export default React.memo(ModalListTools);
