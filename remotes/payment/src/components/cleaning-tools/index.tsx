import React, { useCallback } from 'react';
import { ViewStyle } from 'react-native';
import { icCheckPlus, iconArrowDown } from '@assets/images';
import {
  AlertHolder,
  BlockView,
  BorderRadius,
  Colors,
  CText,
  IconImage,
  ISO_CODE,
  Spacing,
  TouchableOpacity,
  usePostTaskStore,
} from '@btaskee/design-system';
import { useI18n } from '@src/hooks';

import { ModalListTools } from './modal-list-tools';

/**
 * Props for the CleaningTools component
 */
interface CleaningToolsProps {
  isPremiumSelected?: boolean;
  isSupportPremium?: boolean;
  containerStyle?: ViewStyle;
}

/**
 * Component that displays cleaning tools selection button
 * Opens a modal with available cleaning tools when pressed
 */
const CleaningTools: React.FC<CleaningToolsProps> = ({
  isPremiumSelected = false,
  isSupportPremium = false,
  containerStyle = {},
}) => {
  const { t } = useI18n();
  const { isoCode } = usePostTaskStore();

  // Handle opening the tools modal
  const handleShow = useCallback(() => {
    AlertHolder?.alert?.open({
      title: t('POST_TASK_STEP_2.TITLE_MODAL_LIST_TOOLS'),
      message: (
        <ModalListTools
          isPremiumSelected={isPremiumSelected}
          isSupportPremium={isSupportPremium}
        />
      ),
      actions: [{ text: t('CLOSE') }],
    });
  }, [isPremiumSelected, isSupportPremium, t]);

  // Early return if not in Vietnam (currently only supported in VN)
  if (isoCode !== ISO_CODE.VN) {
    return null;
  }

  return (
    <TouchableOpacity
      testID="btnShowCleaningTools"
      onPress={handleShow}
      activeOpacity={0.7}
      style={containerStyle}
    >
      <BlockView
        row
        flex
        padding={Spacing.SPACE_16}
        margin={{ vertical: isSupportPremium ? Spacing.SPACE_16 : 0 }}
        backgroundColor={Colors.UNDERLAY_COLOR}
        radius={BorderRadius.RADIUS_08}
      >
        <IconImage
          source={icCheckPlus}
          size={18}
          color={Colors.SECONDARY_COLOR}
        />
        <BlockView
          flex
          row
          horizontal
        >
          <CText
            flex
            margin={{ horizontal: Spacing.SPACE_12 }}
          >
            {isPremiumSelected
              ? t('POST_TASK_STEP_2.LIST_TOOL_TASK_PREMIUM')
              : t('POST_TASK_STEP_2.LIST_TOOL_TASK_NORMAL')}
          </CText>
          <IconImage
            source={iconArrowDown}
            size={18}
            color={Colors.BLACK}
          />
        </BlockView>
      </BlockView>
    </TouchableOpacity>
  );
};

export default React.memo(CleaningTools);
