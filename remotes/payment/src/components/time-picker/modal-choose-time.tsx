/**
 * A modal component for selecting time using the TimePickerSpinner.
 * Provides a user-friendly interface for time selection with min/max constraints.
 */
import React, { useCallback, useState } from 'react';
import {
  AlertHolder,
  BlockView,
  DateTimeHelpers,
  IDate,
  ITimezone,
  PrimaryButton,
  SizedBox,
  Spacing,
  TypeFormatDate,
} from '@btaskee/design-system';

import TimePickerSpinner from './TimePickerSpinner';

interface ModalChooseTimeProps {
  /** Current timezone */
  timezone: ITimezone;
  /** Currently selected date */
  date: IDate;
  /** Minimum allowed date */
  minimumDate: IDate;
  /** Maximum allowed date */
  maxTime?: IDate;
  /** Callback when date changes */
  onChangeDate: (date: IDate) => void;
}

/**
 * Modal component for time selection using TimePickerSpinner
 */
export const ModalChooseTime: React.FC<ModalChooseTimeProps> = ({
  timezone,
  date,
  minimumDate,
  maxTime,
  onChangeDate,
}) => {
  // Convert date to timezone-aware date object for internal state
  const [selectedDate, setSelectedDate] = useState(
    date
      ? DateTimeHelpers.toDateTz({ timezone, date })
      : DateTimeHelpers.toDateTz({ timezone }),
  );

  // Convert minimum date to Date object for validation
  const minimumDatePicker = minimumDate
    ? DateTimeHelpers.toDateTz({ timezone, date: minimumDate }).toDate()
    : undefined;

  // Convert maximum date to Date object for validation if provided
  const maximumDate = maxTime
    ? DateTimeHelpers.toDateTz({ timezone, date: maxTime }).toDate()
    : undefined;

  /**
   * Handles confirmation of time selection
   */
  const onConfirm = useCallback(() => {
    AlertHolder.alert.close();
    if (selectedDate) {
      onChangeDate(selectedDate);
    }
  }, [selectedDate, onChangeDate]);

  /**
   * Handles time change from the TimePickerSpinner
   */
  const handleTimeChange = useCallback(
    (time: { hour: string; minute: string }) => {
      // Convert the time back to the expected format
      const newDate = DateTimeHelpers.toDateTz({ timezone, date: selectedDate })
        .hour(parseInt(time.hour, 10))
        .minute(parseInt(time.minute, 10))
        .startOf('minute');

      // Update local state when scrolling
      setSelectedDate(newDate);
    },
    [timezone, selectedDate],
  );

  // Format minimum time for the spinner
  const minTimeString = minimumDatePicker
    ? DateTimeHelpers.formatToString({
        timezone,
        date: minimumDatePicker,
        typeFormat: TypeFormatDate.TimeHourMinute,
      })
    : null;

  // Format maximum time for the spinner
  const maxTimeString = maximumDate
    ? DateTimeHelpers.formatToString({
        timezone,
        date: maximumDate,
        typeFormat: TypeFormatDate.TimeHourMinute,
      })
    : null;

  // Get initial hour and minute values from the selected date
  const initialHour = String(
    DateTimeHelpers.getHour({ timezone, date: selectedDate }),
  ).padStart(2, '0');
  const initialMinute = String(
    DateTimeHelpers.getMinute({ timezone, date: selectedDate }),
  ).padStart(2, '0');

  return (
    <BlockView
      margin={{ top: Spacing.SPACE_16 }}
      padding={{ horizontal: Spacing.SPACE_16 }}
    >
      <TimePickerSpinner
        initialHour={initialHour}
        initialMinute={initialMinute}
        onTimeChange={handleTimeChange}
        minTime={minTimeString}
        maxTime={maxTimeString}
        minDate={minimumDate ? minimumDate.toString() : null}
        maxDate={maxTime ? maxTime.toString() : null}
        selectedDate={selectedDate ? selectedDate.toString() : null}
        enableHapticFeedback={true}
      />
      <SizedBox height={Spacing.SPACE_24} />
      <PrimaryButton
        title="OK"
        onPress={onConfirm}
      />
    </BlockView>
  );
};
