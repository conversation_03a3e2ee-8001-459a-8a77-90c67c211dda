/**
 * A customizable time picker component that displays hours and minutes in a scrollable wheel format.
 * Provides a 3D cylindrical effect with time validation against min/max constraints.
 */
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  Animated,
  Dimensions,
  NativeScrollEvent,
  NativeSyntheticEvent,
  Platform,
  ScrollView,
  StyleSheet,
  TextStyle,
  Vibration,
  ViewStyle,
} from 'react-native';
import { BlockView, Colors, CText } from '@btaskee/design-system';

/**
 * Props for the TimePickerSpinner component
 */
interface TimePickerSpinnerProps {
  /** Initial hour value in 24-hour format (e.g. "09") */
  initialHour?: string;
  /** Initial minute value (e.g. "00") */
  initialMinute?: string;
  /** Callback when time changes */
  onTimeChange?: (time: { hour: string; minute: string }) => void;
  /** Minimum allowed time in format "HH:MM" */
  minTime?: string | null;
  /** Maximum allowed time in format "HH:MM" */
  maxTime?: string | null;
  /** Minimum allowed date (full ISO date string) */
  minDate?: string | null;
  /** Maximum allowed date (full ISO date string) */
  maxDate?: string | null;
  /** Selected date (full ISO date string) */
  selectedDate?: string | null;
  /** Number of times to repeat hour/minute arrays for infinite scroll effect */
  loopCount?: number;
  /** Custom styles for the container */
  containerStyle?: ViewStyle;
  /** Custom styles for the time items */
  itemStyle?: TextStyle;
  /** Whether to enable haptic feedback on selection change */
  enableHapticFeedback?: boolean;
}

// Constants
const ITEM_HEIGHT = 36; // Height of each time item
const VISIBLE_ITEMS = 5; // Number of visible items in the picker
const PICKER_HEIGHT = ITEM_HEIGHT * VISIBLE_ITEMS;
const CENTER_INDEX = Math.floor(VISIBLE_ITEMS / 2);

/**
 * A time picker component with a spinning wheel UI for selecting hours and minutes
 */
const TimePickerSpinner: React.FC<TimePickerSpinnerProps> = ({
  initialHour = '14',
  initialMinute = '00',
  onTimeChange,
  minTime,
  maxTime,
  minDate,
  maxDate,
  selectedDate,
  loopCount = 5,
  containerStyle,
  enableHapticFeedback = false,
}) => {
  // Refs for scroll views
  const hourScrollRef = useRef<ScrollView | null>(null);
  const minuteScrollRef = useRef<ScrollView | null>(null);

  // State for selected values and scroll positions
  const [selectedHour, setSelectedHour] = useState(
    parseInt(initialHour, 10) || 0,
  );
  const [selectedMinute, setSelectedMinute] = useState(
    parseInt(initialMinute, 10) || 0,
  );
  const [hourScrollPosition, setHourScrollPosition] = useState(0);
  const [minuteScrollPosition, setMinuteScrollPosition] = useState(0);

  // Base arrays for hours and minutes
  const baseHours = useMemo(() => Array.from({ length: 24 }, (_, i) => i), []);
  const baseMinutes = useMemo(
    () => Array.from({ length: 12 }, (_, i) => i * 5),
    [],
  ); // 5-minute intervals

  // Create looped arrays for infinite scroll illusion
  const hours = useMemo(
    () => Array.from({ length: 24 * loopCount }, (_, i) => baseHours[i % 24]),
    [loopCount, baseHours],
  );

  const minutes = useMemo(
    () => Array.from({ length: 12 * loopCount }, (_, i) => baseMinutes[i % 12]),
    [loopCount, baseMinutes],
  );

  // Calculate center positions for infinite scroll
  const hourCenterIndex = useMemo(
    () => Math.floor(loopCount / 2) * 24 + (parseInt(initialHour, 10) || 0),
    [initialHour, loopCount],
  );

  const minuteCenterIndex = useMemo(() => {
    const initialMinuteValue = parseInt(initialMinute, 10) || 0;
    const minuteIndex = Math.floor(initialMinuteValue / 5);
    return Math.floor(loopCount / 2) * 12 + minuteIndex;
  }, [initialMinute, loopCount]);

  /**
   * Parses min and max time constraints into hour and minute values
   * @returns Object containing min/max hour and minute values
   */
  const parseTimeConstraints = useCallback(() => {
    let minHour = 0,
      minMinute = 0,
      maxHour = 23,
      maxMinute = 55;

    if (minTime) {
      const parts = minTime.split(':');
      if (parts.length === 2) {
        const [h, m] = parts.map(Number);
        if (!isNaN(h) && !isNaN(m)) {
          minHour = h;
          minMinute = Math.floor(m / 5) * 5; // Round to nearest 5-minute interval
        }
      }
    }

    if (maxTime) {
      const parts = maxTime.split(':');
      if (parts.length === 2) {
        const [h, m] = parts.map(Number);
        if (!isNaN(h) && !isNaN(m)) {
          maxHour = h;
          maxMinute = Math.floor(m / 5) * 5; // Round to nearest 5-minute interval
        }
      }
    }

    return { minHour, minMinute, maxHour, maxMinute };
  }, [minTime, maxTime]);

  /**
   * Checks if a given hour and minute combination is valid within constraints
   * @param hour - Hour value to check
   * @param minute - Minute value to check
   * @returns Boolean indicating if time is valid
   */
  const isValidTime = useCallback(
    (hour: number, minute: number) => {
      // If we have full date objects, use them for validation
      if (minDate && selectedDate) {
        const minDateTime = new Date(minDate);
        const currentDateTime = new Date(selectedDate);

        // If dates are different
        if (minDateTime.toDateString() !== currentDateTime.toDateString()) {
          // If min date is future date compared to selected date, all times are valid
          if (minDateTime.getTime() > currentDateTime.getTime()) {
            return true;
          }
          // If min date is past date compared to selected date, all times are valid
          if (minDateTime.getTime() < currentDateTime.getTime()) {
            return true;
          }
        }
      }

      // Fall back to hour/minute validation for same-day comparisons
      const { minHour, minMinute, maxHour, maxMinute } = parseTimeConstraints();

      if (hour < minHour || hour > maxHour) return false;
      if (hour === minHour && minute < minMinute) return false;
      if (hour === maxHour && minute > maxMinute) return false;

      return true;
    },
    [parseTimeConstraints, minDate, selectedDate],
  );

  // Initialize scroll positions
  useEffect(() => {
    const timer = setTimeout(() => {
      if (hourScrollRef.current) {
        hourScrollRef.current.scrollTo({
          y: hourCenterIndex * ITEM_HEIGHT,
          animated: false,
        });
      }

      if (minuteScrollRef.current) {
        minuteScrollRef.current.scrollTo({
          y: minuteCenterIndex * ITEM_HEIGHT,
          animated: false,
        });
      }
    }, 300);

    return () => clearTimeout(timer);
  }, [hourCenterIndex, minuteCenterIndex]);

  /**
   * Handles hour scroll events and validates selected time
   */
  const handleHourScroll = useCallback(
    (event: NativeSyntheticEvent<NativeScrollEvent>) => {
      const scrollY = event.nativeEvent.contentOffset.y;
      setHourScrollPosition(scrollY);

      // Only update selection when scroll has settled
      if (scrollY % ITEM_HEIGHT !== 0) return;

      const index = Math.round(scrollY / ITEM_HEIGHT);
      if (index < 0 || index >= hours.length) return;

      const actualHour = hours[index];
      if (actualHour === undefined || actualHour === selectedHour) return;

      // Check if new time is valid
      if (isValidTime(actualHour, selectedMinute)) {
        setSelectedHour(actualHour);

        if (enableHapticFeedback) {
          Vibration.vibrate(10);
        }

        onTimeChange?.({
          hour: String(actualHour).padStart(2, '0'),
          minute: String(selectedMinute).padStart(2, '0'),
        });
      } else {
        // Snap back to valid time
        const validIndex = hours.findIndex((h) => h === selectedHour);
        if (validIndex >= 0 && hourScrollRef.current) {
          const validScrollPosition = validIndex * ITEM_HEIGHT;
          hourScrollRef.current.scrollTo({
            y: validScrollPosition,
            animated: true,
          });
        }
      }
    },
    [
      selectedHour,
      selectedMinute,
      onTimeChange,
      hours,
      isValidTime,
      enableHapticFeedback,
    ],
  );

  /**
   * Handles minute scroll events and validates selected time
   */
  const handleMinuteScroll = useCallback(
    (event: NativeSyntheticEvent<NativeScrollEvent>) => {
      const scrollY = event.nativeEvent.contentOffset.y;
      setMinuteScrollPosition(scrollY);

      // Only update selection when scroll has settled
      if (scrollY % ITEM_HEIGHT !== 0) return;

      const index = Math.round(scrollY / ITEM_HEIGHT);
      if (index < 0 || index >= minutes.length) return;

      const actualMinute = minutes[index];
      if (actualMinute === undefined || actualMinute === selectedMinute) return;

      // Check if new time is valid
      if (isValidTime(selectedHour, actualMinute)) {
        setSelectedMinute(actualMinute);

        if (enableHapticFeedback) {
          Vibration.vibrate(10);
        }

        onTimeChange?.({
          hour: String(selectedHour).padStart(2, '0'),
          minute: String(actualMinute).padStart(2, '0'),
        });
      } else {
        // Snap back to valid time
        const validIndex = minutes.findIndex((m) => m === selectedMinute);
        if (validIndex >= 0 && minuteScrollRef.current) {
          const validScrollPosition = validIndex * ITEM_HEIGHT;
          minuteScrollRef.current.scrollTo({
            y: validScrollPosition,
            animated: true,
          });
        }
      }
    },
    [
      selectedHour,
      selectedMinute,
      onTimeChange,
      minutes,
      isValidTime,
      enableHapticFeedback,
    ],
  );

  /**
   * Renders a single time item with 3D effect
   */
  const renderPickerItem = useCallback(
    (
      value: number,
      index: number,
      selectedValue: number,
      type: 'hour' | 'minute',
    ) => {
      // Determine if this time value is valid
      const isValid = isValidTime(
        type === 'hour' ? value : selectedHour,
        type === 'minute' ? value : selectedMinute,
      );

      // Determine text styling based on selection and distance from center
      const isSelected = value === selectedValue;
      const textColor = isSelected
        ? Colors.BLACK
        : isValid
        ? Colors.BLACK
        : Colors.LIGHT_GRAY;
      const fontSize = isSelected ? 22 : 18;
      const fontWeight = isSelected ? '600' : '400';

      // Calculate distance for 3D effect
      const distance = Math.abs(value - selectedValue);

      // Enhanced 3D cylindrical wheel effect with rotation transforms
      const opacity = Math.max(1, 1 - distance * 0.5);
      const scale = Math.max(0.85, 1 - distance * 0.08);

      // Apply rotation to create inclined effect for outermost items
      let rotateX = '0deg';
      if (isSelected) {
        rotateX = '0deg';
      } else if (
        distance === 1 ||
        distance === 23 ||
        distance === 5 ||
        distance === 55
      ) {
        rotateX = index < CENTER_INDEX ? '30deg' : '-30deg';
      } else {
        rotateX = index < CENTER_INDEX ? '45deg' : '-45deg';
      }

      return (
        <Animated.View
          key={`${type}-${value}-${index}`}
          style={[
            styles.pickerItem,
            {
              opacity,
              transform: [{ perspective: 1500 }, { rotateX }, { scale }],
            },
          ]}
        >
          <CText
            bold
            style={[
              styles.pickerItemText,
              {
                color: textColor,
                fontSize,
                fontWeight,
                opacity,
              },
            ]}
          >
            {String(value).padStart(2, '0')}
          </CText>
        </Animated.View>
      );
    },
    [selectedHour, selectedMinute, isValidTime],
  );

  // Common ScrollView props for reuse
  const scrollViewProps = useMemo(
    () => ({
      showsVerticalScrollIndicator: false,
      snapToInterval: ITEM_HEIGHT,
      snapToAlignment: 'center' as const,
      decelerationRate: 'fast' as const,
      scrollEventThrottle: 32,
      bounces: false,
      bouncesZoom: false,
      alwaysBounceVertical: false,
      directionalLockEnabled: true,
      maximumZoomScale: 1,
      minimumZoomScale: 1,
      nestedScrollEnabled: false,
      overScrollMode: 'never' as const,
      removeClippedSubviews: Platform.OS === 'android',
      contentInsetAdjustmentBehavior: 'never' as const,
    }),
    [],
  );

  return (
    <BlockView style={[styles.container, containerStyle]}>
      <BlockView style={styles.pickerContainer}>
        {/* Hour Picker */}
        <BlockView style={styles.wheelContainer}>
          <BlockView style={styles.pickerWrapper}>
            <ScrollView
              ref={hourScrollRef}
              style={styles.picker}
              contentContainerStyle={[
                styles.pickerContent,
                { paddingVertical: CENTER_INDEX * ITEM_HEIGHT },
              ]}
              onMomentumScrollEnd={handleHourScroll}
              onScrollEndDrag={handleHourScroll}
              {...scrollViewProps}
            >
              {hours.map((hour, index) =>
                renderPickerItem(hour, index, selectedHour, 'hour'),
              )}
            </ScrollView>
            <BlockView
              style={styles.highlightZone}
              pointerEvents="none"
            />
          </BlockView>
        </BlockView>

        {/* Separator */}
        <CText style={styles.separator}>:</CText>

        {/* Minute Picker */}
        <BlockView style={styles.wheelContainer}>
          <BlockView style={styles.pickerWrapper}>
            <ScrollView
              ref={minuteScrollRef}
              style={styles.picker}
              contentContainerStyle={[
                styles.pickerContent,
                { paddingVertical: CENTER_INDEX * ITEM_HEIGHT },
              ]}
              onMomentumScrollEnd={handleMinuteScroll}
              onScrollEndDrag={handleMinuteScroll}
              {...scrollViewProps}
            >
              {minutes.map((minute, index) =>
                renderPickerItem(minute, index, selectedMinute, 'minute'),
              )}
            </ScrollView>
            <BlockView
              style={styles.highlightZone}
              pointerEvents="none"
            />
          </BlockView>
        </BlockView>
      </BlockView>
    </BlockView>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.BG_COLOR2,
    borderRadius: 12,
    padding: 8,
    alignItems: 'center',
  },
  pickerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  wheelContainer: {
    alignItems: 'center',
  },
  pickerWrapper: {
    position: 'relative',
    height: PICKER_HEIGHT,
    width: Dimensions.get('window').width / 5,
  },
  picker: {
    height: PICKER_HEIGHT,
    width: Dimensions.get('window').width / 5,
  },
  pickerContent: {
    alignItems: 'center',
  },
  pickerItem: {
    height: ITEM_HEIGHT,
    justifyContent: 'center',
    alignItems: 'center',
  },
  pickerItemText: {
    textAlign: 'center',
    fontFamily: Platform.OS === 'ios' ? 'System' : 'Roboto',
  },
  highlightZone: {
    position: 'absolute',
    top: CENTER_INDEX * ITEM_HEIGHT,
    left: 0,
    right: 0,
    height: ITEM_HEIGHT,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    borderRadius: 8,
    pointerEvents: 'none',
  },
  separator: {
    fontSize: 24,
    fontWeight: '600',
    color: '#333333',
    marginHorizontal: 16,
  },
});

export default React.memo(TimePickerSpinner);
