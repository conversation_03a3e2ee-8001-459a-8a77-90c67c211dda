import { Dimensions, StyleSheet } from 'react-native';
import { Colors, FontSizes, Spacing } from '@btaskee/design-system';

const { width } = Dimensions.get('window');

export const styles = StyleSheet.create({
  container: {
    borderColor: Colors.BORDER_COLOR,
    borderRadius: 8,
    borderWidth: 1,
    marginHorizontal: Spacing.SPACE_16,
    marginTop: Spacing.SPACE_12,
  },
  seperate: {
    width: 1,
    height: '100%',
    backgroundColor: Colors.GREY,
  },
  txtTime: {
    flex: 1,
    textAlign: 'center',
    fontSize: FontSizes.SIZE_16,
    color: Colors.BLACK,
  },
  wrapperBtnTime: {},
  wrapperTime: {
    paddingRight: Spacing.SPACE_12,
    marginVertical: Spacing.SPACE_12,
    alignItems: 'center',
  },
  btnTime: {
    minWidth: 100,
    minHeight: 36,
    borderRadius: 6,
    width: Math.round(width / 2.4),
    paddingVertical: Spacing.SPACE_12,
    backgroundColor: Colors.GREY_2,
  },
  txtDate: {
    color: Colors.BLACK,
    marginLeft: Spacing.SPACE_12,
  },
  boxLabelTitle: {
    marginLeft: Spacing.SPACE_12,
  },
  clockIcon: {
    width: 24,
    height: 24,
  },
});
