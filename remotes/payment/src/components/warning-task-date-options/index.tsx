import React from 'react';
import { BlockView, CText, PAYMENT_METHOD } from '@btaskee/design-system';

import { useI18n } from '@hooks';

import styles from './styles';

type WarningTaskDateOptionsProps = {
  paymentMethod?: {
    value?: string;
  };
};

const WarningTaskDateOptions = ({
  paymentMethod,
}: WarningTaskDateOptionsProps) => {
  const { t } = useI18n();
  let warning = t('FAV_TASKER.WARNING_PAYMENT_METHOD_PREPAID');

  switch (paymentMethod?.value) {
    case PAYMENT_METHOD.cash:
      warning = t('FAV_TASKER.WARNING_PAYMENT_METHOD_CASH');
      break;
    case PAYMENT_METHOD.credit:
      warning = t('FAV_TASKER.WARNING_PAYMENT_METHOD_CASH');
      break;

    case PAYMENT_METHOD.card:
      warning = t('FAV_TASKER.WARNING_PAYMENT_METHOD_CARD');
      break;
    case PAYMENT_METHOD.kredivo:
      warning = t('FAV_TASKER.WARNING_PAYMENT_METHOD_KREDIVO');
      break;
    default:
      break;
  }
  return (
    <BlockView style={styles.container}>
      <CText>{warning}</CText>
    </BlockView>
  );
};
export default WarningTaskDateOptions;
