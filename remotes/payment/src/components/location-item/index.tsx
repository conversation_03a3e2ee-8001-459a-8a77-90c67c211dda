/**
 * LocationItem component
 * Displays location information with optional edit functionality
 */
import React, { useCallback, useMemo } from 'react';
import { StyleSheet, ViewStyle } from 'react-native';
import { icChange, icLocation } from '@assets/images';
import {
  BlockView,
  Colors,
  COMPANY_TYPE,
  ConditionView,
  CText,
  HitSlop,
  HomeType,
  HOUSE_TYPE,
  IconImage,
  SizedBox,
  TouchableOpacity,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';

import { styles } from './styles';

interface LocationItemProps {
  testIDs?: {
    item?: string;
    editBtn?: string;
  };
  shortAddress?: string;
  address?: string;
  homeType?: HomeType;
  containerStyle?: ViewStyle;
  isShowUpdate?: boolean;
  isHideIcon?: boolean;
  onPress?: () => void;
  onPressUpdate?: () => void;
}

export const LocationItem = React.memo(
  ({
    testIDs,
    shortAddress,
    address,
    homeType,
    containerStyle,
    isHideIcon,
    isShowUpdate,
    onPress,
    onPressUpdate,
  }: LocationItemProps) => {
    const { t } = useI18n();

    const homeTypeText = useMemo(() => {
      if (!homeType) return null;

      const companyTypes = COMPANY_TYPE.map((e) => e.key);
      let label;

      if (companyTypes.includes(homeType)) {
        label = COMPANY_TYPE.find((e) => e.value === homeType)?.key;
      } else {
        label = HOUSE_TYPE.find((e) => e.value === homeType)?.key;
      }

      return label ? t(label) : null;
    }, [homeType, t]);

    const renderLocationIcon = useCallback(() => {
      if (isHideIcon) return null;

      return (
        <>
          <IconImage
            source={icLocation}
            size={18}
          />
          <SizedBox width={12} />
        </>
      );
    }, [isHideIcon]);

    const renderHomeTypeText = useCallback(() => {
      if (!homeTypeText) return null;

      return (
        <>
          <SizedBox height={5} />
          <CText
            numberOfLines={2}
            color={Colors.GREY}
          >
            {homeTypeText}
          </CText>
        </>
      );
    }, [homeTypeText]);

    const renderEditButton = useCallback(() => {
      if (!isShowUpdate || !onPressUpdate) return null;

      return (
        <TouchableOpacity
          testID={testIDs?.editBtn}
          hitSlop={HitSlop.SMALL}
          onPress={onPressUpdate}
          style={styles.updateBtn}
        >
          <IconImage
            source={icChange}
            size={20}
          />
        </TouchableOpacity>
      );
    }, [isShowUpdate, onPressUpdate, testIDs?.editBtn]);

    return (
      <BlockView style={StyleSheet.flatten([styles.container, containerStyle])}>
        <TouchableOpacity
          style={styles.leftContainer}
          testID={testIDs?.item}
          disabled={!onPress}
          onPress={onPress}
        >
          <ConditionView
            condition={!isHideIcon}
            viewTrue={renderLocationIcon()}
          />
          <BlockView flex>
            <CText
              size={16}
              bold
              numberOfLines={2}
            >
              {shortAddress}
            </CText>
            <SizedBox height={5} />
            <CText
              numberOfLines={3}
              color={Colors.GREY}
            >
              {address}
            </CText>
            <ConditionView
              condition={Boolean(homeTypeText)}
              viewTrue={renderHomeTypeText()}
            />
          </BlockView>
        </TouchableOpacity>
        <SizedBox width={12} />
        <ConditionView
          condition={Boolean(isShowUpdate && onPressUpdate)}
          viewTrue={renderEditButton()}
        />
      </BlockView>
    );
  },
);

// Add display name for debugging
LocationItem.displayName = 'LocationItem';
