/**
 * Styles for the PriceIncrease component
 */
import { StyleSheet } from 'react-native';
import { Colors, Spacing } from '@btaskee/design-system';

export const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    flexDirection: 'row',
    marginTop: Spacing.SPACE_16,
    padding: Spacing.SPACE_16,
    marginHorizontal: Spacing.SPACE_16,
    backgroundColor: Colors.ORANGE_2,
  },
  txtInfo: {
    marginHorizontal: Spacing.SPACE_16,
  },
  warningIcon: {
    width: 32,
    height: 32,
  },
});
