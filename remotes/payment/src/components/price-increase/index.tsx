/**
 * PriceIncrease Component
 *
 * Displays a warning message when price has increased due to supply and demand.
 * Includes smooth fade-in and fade-out animations.
 */
import React, { useCallback, useEffect } from 'react';
import { Animated, ViewStyle } from 'react-native';
import { iconWarning } from '@assets/images';
import {
  AnimationHelpers,
  BlockView,
  BorderRadius,
  Colors,
  CText,
  FastImage,
} from '@btaskee/design-system';
import { IPrice } from '@types';
import { get } from 'lodash-es';

import { useFadeAnimation, useI18n } from '@hooks';

import { styles } from './styles';

interface PriceIncreaseProps {
  price: IPrice;
  containerStyle?: ViewStyle;
  testID?: string;
}

/**
 * PriceIncrease component
 *
 * Shows a warning when prices have increased due to supply and demand.
 * Animates with fade effects for smooth appearance and disappearance.
 */
const PriceIncrease: React.FC<PriceIncreaseProps> = ({
  price,
  containerStyle = {},
  testID = 'price-increase-warning',
}) => {
  const { t } = useI18n();
  const { opacity, isVisible, isAnimating, fadeIn, fadeOut } =
    useFadeAnimation();

  // Check if price has increased
  const checkPriceIncrease = useCallback((priceData: IPrice) => {
    return Boolean(get(priceData, 'isIncrease', false));
  }, []);

  useEffect(() => {
    const shouldShow = checkPriceIncrease(price);

    // Only update if not currently animating
    if (!isAnimating) {
      if (shouldShow && !isVisible) {
        // Run layout animation for smooth container sizing
        AnimationHelpers.runLayoutAnimation();
        fadeIn();
      } else if (!shouldShow && isVisible) {
        fadeOut();
      }
    }
  }, [price, isVisible, isAnimating, checkPriceIncrease, fadeIn, fadeOut]);

  if (!isVisible) {
    return null;
  }

  return (
    <Animated.View
      style={{ opacity }}
      testID={testID}
    >
      <BlockView
        radius={BorderRadius.RADIUS_08}
        border={{ width: 1, color: Colors.ORANGE }}
        style={[styles.container, containerStyle]}
      >
        <FastImage
          style={styles.warningIcon}
          source={iconWarning}
          tintColor={Colors.ORANGE}
        />
        <BlockView flex>
          <CText style={styles.txtInfo}>
            {t('SUPPLY_DEMAND_COST_INCREASE')}
          </CText>
        </BlockView>
      </BlockView>
    </Animated.View>
  );
};

export default React.memo(PriceIncrease);
