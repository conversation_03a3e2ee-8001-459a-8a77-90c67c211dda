import React from 'react';
import { ViewStyle } from 'react-native';
import { iconNotSupportCity } from '@assets/images';
import { BlockView, CText, FastImage } from '@btaskee/design-system';

import { useI18n } from '@hooks';

import styles from './styles';

type NotSupportCityProps = {
  title?: string;
  contentContainerStyle?: ViewStyle;
  style?: ViewStyle;
};

const NotSupportCity = ({
  title,
  style,
  contentContainerStyle,
}: NotSupportCityProps) => {
  const { t } = useI18n();

  return (
    <BlockView style={[styles.container, style]}>
      <BlockView
        horizontal
        style={[styles.contentContainer, contentContainerStyle]}
      >
        <FastImage
          source={iconNotSupportCity}
          style={styles.iconStyle}
        />
        <CText
          bold
          style={styles.txtContentNotSupportCity}
        >
          {title ? title : t('CONTENT_NOT_SUPPORT_CITY')}
        </CText>
      </BlockView>
    </BlockView>
  );
};
export default NotSupportCity;
