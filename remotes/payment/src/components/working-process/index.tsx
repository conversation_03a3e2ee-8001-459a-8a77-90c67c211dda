/**
 * WorkingProcess Component
 *
 * Displays a tabbed view of the working process details for a cleaning service.
 * Uses TabView to allow users to navigate between different process steps.
 */
import React, { useCallback, useMemo } from 'react';
import { TabBar, TabView } from 'react-native-tab-view';
import {
  BlockView,
  Colors,
  CText,
  DeviceHelper,
  getTextWithLocale,
  LOCALES,
  Spacing,
  TouchableOpacity,
  usePostTaskStore,
} from '@btaskee/design-system';
import { WorkingProcessDetail } from '@src/types/service';
import { cloneDeep, isEmpty } from 'lodash-es';

import { Scene } from '@components';
import { useI18n } from '@hooks';

import styles from './styles';

/**
 * Interface for tab route items
 */
interface RouteItem extends WorkingProcessDetail {
  key: number;
}

export const WorkingProcess: React.FC = () => {
  const { i18n } = useI18n();
  const { service } = usePostTaskStore();

  const [index, setIndex] = React.useState(0);

  // Format routes from service working process details
  const routes = useMemo<RouteItem[]>(() => {
    const detail = cloneDeep(service?.workingProcessV2?.detail || []);
    return detail.map((e, index) => ({
      key: index,
      ...e,
      text: getTextWithLocale(e.text, i18n.language as LOCALES),
    }));
  }, [service?.workingProcessV2?.detail, i18n.language]);

  // Render individual tab scene
  const renderScene = useCallback(({ route }: { route: RouteItem }) => {
    return <Scene route={route} />;
  }, []);

  // Render custom tab bar item
  const renderTabBarItem = useCallback(
    ({ route, props }: { route: RouteItem; props: any }) => {
      // Check if the current tab is focused
      const focused =
        props.navigationState.index ===
        props.navigationState.routes.findIndex(
          (r: RouteItem) => r.key === route.key,
        );

      return (
        <TouchableOpacity
          onPress={() => props.jumpTo(route.key)}
          style={[styles.tabStyle, focused && styles.tabStyleActive]}
        >
          <CText
            style={[styles.label, focused ? styles.activeLabel : styles.label]}
          >
            {route.text}
          </CText>
        </TouchableOpacity>
      );
    },
    [],
  );

  // Render tab bar
  const renderTabBar = useCallback(
    (props: any) => (
      <TabBar
        {...props}
        indicatorStyle={styles.backgroundWhite}
        activeColor={Colors.PRIMARY_COLOR}
        scrollEnabled={true}
        tabStyle={styles.tabStyleTabBar}
        style={styles.tabBarStyle}
        inactiveColor={Colors.BLACK}
        renderTabBarItem={({ route }) => renderTabBarItem({ route, props })}
      />
    ),
    [renderTabBarItem],
  );

  // Early return if no working process details
  if (isEmpty(service?.workingProcessV2?.detail)) {
    return null;
  }

  return (
    <BlockView
      height={Math.round(DeviceHelper.WINDOW.HEIGHT / 2.5)}
      margin={{ horizontal: Spacing.SPACE_16, top: Spacing.SPACE_20 }}
    >
      <TabView
        swipeEnabled={false}
        navigationState={{ index, routes }}
        renderScene={renderScene}
        onIndexChange={setIndex}
        renderTabBar={renderTabBar}
      />
    </BlockView>
  );
};
