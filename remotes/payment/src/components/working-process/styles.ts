/**
 * Styles for the WorkingProcess component
 * Defines styling for tabs, tab bar, and labels
 */
import { StyleSheet } from 'react-native';
import { BorderRadius, Colors, Spacing } from '@btaskee/design-system';

export default StyleSheet.create({
  backgroundWhite: {
    backgroundColor: Colors.BG_COLOR2,
  },
  tabStyleTabBar: {
    paddingHorizontal: 8,
    paddingTop: 0,
    borderBottomColor: Colors.BG_COLOR2,
  },
  tabBarStyle: {
    backgroundColor: Colors.BG_COLOR2,
    shadowColor: Colors.BG_COLOR2,
  },
  contentContainer: {
    paddingLeft: Spacing.SPACE_08,
  },
  labelStyle: {
    textAlign: 'center',
  },
  tabStyle: {
    paddingHorizontal: Spacing.SPACE_16,
    paddingVertical: Spacing.SPACE_08,
    backgroundColor: Colors.BORDER_COLOR,
    borderRadius: BorderRadius.RADIUS_08,
    marginRight: Spacing.SPACE_08,
  },
  tabStyleActive: {
    backgroundColor: Colors.PRIMARY_COLOR,
  },
  activeLabel: {
    color: Colors.WHITE,
  },
  label: {
    textAlign: 'center',
    color: Colors.BLACK,
  },
});
