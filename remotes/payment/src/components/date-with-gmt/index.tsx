import React, { useMemo } from 'react';
import {
  CommonTextProps,
  CText,
  DateTimeHelpers,
  IDate,
  ITimezone,
  TypeFormatDate,
} from '@btaskee/design-system';
import { capitalize } from 'lodash-es';

// import { capitalize } from '@helper';

/**
 * Props for the DateWithGMT component
 */
interface DateWithGMTProps extends CommonTextProps {
  timezone: ITimezone;
  date: IDate;
  typeFormat: TypeFormatDate;
}

/**
 * Component that displays a date with GMT timezone information
 * Formats the date according to the specified format and appends GMT offset
 */
export const DateWithGMT: React.FC<DateWithGMTProps> = ({
  timezone,
  date,
  typeFormat,
  ...props
}) => {
  // Format the date and append GMT information if available
  const formattedText = useMemo(() => {
    const dateFormat = capitalize(
      DateTimeHelpers.formatToString({ timezone, date, typeFormat }),
    );
    const gmt = DateTimeHelpers.getGMTByCompareTzDefault(timezone);

    return gmt ? `${dateFormat} ${gmt}` : dateFormat;
  }, [date, timezone, typeFormat]);

  return <CText {...props}>{formattedText}</CText>;
};

export default React.memo(DateWithGMT);
