/**
 * DurationWithGMT component
 * Displays duration information with GMT timezone details
 */
import React, { useMemo } from 'react';
import {
  CommonTextProps,
  CText,
  DateTimeHelpers,
  IDate,
  ITimezone,
  TypeFormatDate,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';

interface DateWithGMTProps extends CommonTextProps {
  timezone: ITimezone;
  date: IDate;
  duration?: number;
  isShowOnlyDuration?: boolean;
}

export const DurationWithGMT = React.memo(
  ({
    timezone,
    date,
    duration,
    isShowOnlyDuration,
    ...props
  }: DateWithGMTProps) => {
    const { t } = useI18n();

    const durationText = useMemo(() => {
      if (!duration) return null;

      if (isShowOnlyDuration) {
        return t('INCREASE_DURATION.DURATION', { t: duration });
      }

      const gmt = DateTimeHelpers.getGMTByCompareTzDefault(timezone);
      const fromHour = DateTimeHelpers.toDateTz({ timezone, date }).add(
        duration,
        'hour',
      );

      let workInText = t('WORK_IN_TIME_FROM_A_TO_B', {
        t1: duration,
        t2: DateTimeHelpers.formatToString({
          timezone,
          date,
          typeFormat: TypeFormatDate.TimeHourMinute,
        }),
        t3: DateTimeHelpers.formatToString({
          timezone,
          date: fromHour,
          typeFormat: TypeFormatDate.TimeHourMinute,
        }),
      });

      if (gmt) {
        workInText = `${workInText} ${gmt}`;
      }

      return workInText;
    }, [date, duration, isShowOnlyDuration, t, timezone]);

    if (!durationText) return null;

    return <CText {...props}>{durationText}</CText>;
  },
);

// Add display name for debugging
DurationWithGMT.displayName = 'DurationWithGMT';
