/**
 * TermsAndConditions Component
 *
 * Displays terms and conditions for air conditioning and sofa cleaning services in Thailand.
 * This component is only rendered for users in Thailand (ISO_CODE.TH).
 */
import React from 'react';
import {
  BlockView,
  CText,
  ISO_CODE,
  usePostTaskStore,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';

import { styles } from './styles';

interface TermsAndConditionsProps {
  style?: object;
}

/**
 * Displays terms and conditions for air conditioning and sofa cleaning services in Thailand
 */
const TermsAndConditions: React.FC<TermsAndConditionsProps> = ({ style }) => {
  const { t } = useI18n();
  const { isoCode } = usePostTaskStore();

  // Early return if not in Thailand
  if (isoCode !== ISO_CODE.TH) {
    return null;
  }

  return (
    <BlockView style={[styles.containerStyle, style]}>
      <CText
        h5
        bold
        style={styles.txtTitle}
      >
        {t('TERMS_AND_CONDITIONS_TH')}
      </CText>
      <CText style={styles.txtDescription}>
        {t('TERMS_AND_CONDITIONS_1_TH')}
      </CText>
      <CText style={styles.txtDescription}>
        {t('TERMS_AND_CONDITIONS_2_TH')}
      </CText>
    </BlockView>
  );
};

export default React.memo(TermsAndConditions);
