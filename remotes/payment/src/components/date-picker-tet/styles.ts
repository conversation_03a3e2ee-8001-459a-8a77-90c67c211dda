import { Dimensions, StyleSheet } from 'react-native';
import { Colors, FontSizes, Spacing } from '@btaskee/design-system';

const { height } = Dimensions.get('window');
const RATIO_HEIGHT_MODAL = 0.7;

export const styles = StyleSheet.create({
  container: {
    marginVertical: Spacing.SPACE_24,
    marginHorizontal: Spacing.SPACE_16,
  },
  txtTitle: {
    fontSize: FontSizes.SIZE_16,
  },
  touchableStyle: {
    flex: 1,
    marginTop: 12,
    borderRadius: 8,
    borderColor: Colors.LIGHT_GREY_3,
    paddingLeft: Spacing.SPACE_16,
    paddingRight: Spacing.SPACE_12,
    flexDirection: 'row',
    borderWidth: 1,
    minHeight: 56,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  iconCalendarStyle: {
    width: 28,
    height: 28,
  },
  boxContentStyle: {
    paddingVertical: 18,
  },
  txtDate: {
    textTransform: 'capitalize',
  },
  titleStyle: {
    textAlign: 'center',
  },
  modalContent: {
    height: Math.round(height * RATIO_HEIGHT_MODAL),
  },
});
