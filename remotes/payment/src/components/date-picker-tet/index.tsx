import React, { memo, useCallback, useEffect, useRef } from 'react';
import { iconCalendar1 } from '@assets/images';
import {
  BlockView,
  CModal,
  CModalHandle,
  CText,
  DateTimeHelpers,
  ESTIMATED_TIME_POST_TASK_MINUTES,
  FastImage,
  IDate,
  ITimezone,
  MIN_POST_TASK_TIME,
  roundOfNumberMinutes,
  TouchableOpacity,
  TypeFormatDate,
} from '@btaskee/design-system';
import { IService } from '@types';
import { cloneDeep, get } from 'lodash-es';

import { useI18n } from '@hooks';

import TetBookingCalendar from '../tet-booking-calendar';
import { styles } from './styles';

interface DatePickerTetProps {
  service?: IService;
  date?: IDate;
  onChangeDateTime?: (date?: IDate, timezone?: ITimezone) => void;
  settingSystem?: any;
  setService?: (data?: any) => void;
  setPreviousTetBooking?: (value?: boolean) => void;
  timezone?: ITimezone;
}

/**
 * Date picker component specifically for Tet holiday bookings
 * Handles special date selection logic for the Tet holiday period
 */
const DatePickerTet = (props: DatePickerTetProps) => {
  const {
    service,
    date,
    onChangeDateTime,
    settingSystem,
    setService,
    setPreviousTetBooking,
    timezone,
  } = props;
  const tetBookingFromDate = get(
    service,
    'tetBookingDates.bookTaskTime.fromDate',
    null,
  );

  const { t } = useI18n();
  const modalRef = useRef<CModalHandle>(null);

  // Initialize date if not provided
  useEffect(() => {
    if (!date) {
      const minute = roundOfNumberMinutes(
        DateTimeHelpers.getMinute({ timezone }),
      );

      // get minPostTaskTime from settingSystem
      const minPostTaskTime =
        get(settingSystem, 'minPostTaskTime', MIN_POST_TASK_TIME) +
        ESTIMATED_TIME_POST_TASK_MINUTES;

      const minDateTimePostTask = DateTimeHelpers.toDayTz({ timezone })
        .minute(minute)
        .add(minPostTaskTime, 'minute'); // minimum date to post task

      onChangeDate(minDateTimePostTask);
    }
  }, []);

  // Handle Tet booking date constraints
  useEffect(() => {
    // When current time +2 days is before the start of Tet booking period,
    // set default to the start of Tet booking period
    // This prevents booking dates before Tet booking is allowed
    const latestDate = DateTimeHelpers.toDayTz({ timezone })
      .add(2, 'day')
      .toDate();
    if (
      tetBookingFromDate &&
      DateTimeHelpers.checkIsAfter({
        timezone,
        firstDate: tetBookingFromDate,
        secondDate: latestDate,
        unit: 'day',
      })
    ) {
      onChangeDateTime?.(
        DateTimeHelpers.formatToString({
          timezone,
          date: DateTimeHelpers.toDateTz({ timezone, date: tetBookingFromDate })
            .hour(service?.defaultTaskTime || 14)
            .startOf('hour')
            .toDate(),
        }),
        timezone,
      );
    }
  }, [tetBookingFromDate, service, onChangeDateTime, timezone]);

  const onChangeDate = useCallback(
    (value: IDate) => {
      const dateFormat = DateTimeHelpers.formatToString({
        timezone,
        date: value,
      });
      onChangeDateTime?.(dateFormat, timezone);
    },
    [onChangeDateTime, timezone],
  );

  const _onChooseDate = useCallback(async () => {
    modalRef?.current?.close?.();
  }, []);

  const _chooseDate = useCallback(() => {
    modalRef?.current?.open?.();
  }, []);

  const onRemoveServiceTet = useCallback(() => {
    const cloneService = cloneDeep(service);
    delete cloneService.isTet;
    setPreviousTetBooking?.(false);
    modalRef?.current?.close?.();
    return setService?.(cloneService);
  }, [service, setPreviousTetBooking, setService]);

  const formattedDate = DateTimeHelpers.formatToString({
    timezone,
    date,
    typeFormat: TypeFormatDate.DayOfMonth,
  });

  return (
    <BlockView style={styles.container}>
      <CText
        style={styles.txtTitle}
        bold
      >
        {t('ELDERLY_CARE_CHOOSE_TIME_TITLE')}
      </CText>
      <TouchableOpacity
        testID="datePickerTet"
        onPress={_chooseDate}
        style={styles.touchableStyle}
      >
        <BlockView
          flex
          style={styles.boxContentStyle}
        >
          <CText
            style={styles.txtDate}
            bold
          >
            {formattedDate}
          </CText>
        </BlockView>
        <FastImage
          source={iconCalendar1}
          style={styles.iconCalendarStyle}
        />
      </TouchableOpacity>

      <CModal
        hideButtonClose
        noBackdropPress
        ref={modalRef}
        titleStyle={styles.titleStyle}
        title={t('WORKING_SCHEDULE')}
      >
        <BlockView style={styles.modalContent}>
          <TetBookingCalendar
            onRemoveServiceTet={onRemoveServiceTet}
            service={service}
            date={date}
            settingSystem={settingSystem}
            onChangeDateTime={onChangeDateTime}
            onChooseDate={_onChooseDate}
            timezone={timezone}
          />
        </BlockView>
      </CModal>
    </BlockView>
  );
};

export default memo(DatePickerTet);
