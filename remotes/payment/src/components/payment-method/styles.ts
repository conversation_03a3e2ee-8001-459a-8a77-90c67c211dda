import { StyleSheet } from 'react-native';
import {
  BorderRadius,
  Colors,
  FontSizes,
  Spacing,
} from '@btaskee/design-system';

export default StyleSheet.create({
  panel: {
    marginTop: Spacing.SPACE_32,
    marginBottom: Spacing.SPACE_16,
    justifyContent: 'space-between',
  },
  txtPanel: {
    fontSize: FontSizes.SIZE_16,
  },
  wrapRemovePromotion: {
    backgroundColor: Colors.LIGHT_GRAY,
    borderRadius: BorderRadius.RADIUS_16,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  imgIcon: {
    width: 28,
    height: 28,
  },
  imgIconArrow: {
    width: 20,
    height: 20,
  },
  txtBankName: {
    color: Colors.GREY,
    marginTop: 5,
  },
  rightContent: {
    flex: 1,
    marginLeft: 10,
  },
  iconPaymentMethod: {
    width: 30,
    height: 30,
  },
  wrapperPayment: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    padding: Spacing.SPACE_12,
  },
  btnPayment: {
    padding: Spacing.SPACE_04,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  txtButton: {
    // textAlign: 'center',
    // marginLeft: 10,
    // maxWidth: '80%',
  },
  wrapperBtn: {
    flex: 1,
  },
  line: {
    width: 1,
    height: '100%',
    backgroundColor: Colors.BORDER_COLOR,
  },
});
