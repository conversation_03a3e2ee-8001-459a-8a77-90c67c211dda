/**
 * Scene Component - Displays a list of work items to be done
 * Renders a scrollable card with work items based on route data
 */
import React, { memo } from 'react';
import { ScrollView } from 'react-native';
import { iconStarOrange } from '@assets/images';
import {
  BlockView,
  Card,
  Colors,
  ConditionView,
  CText,
  getTextWithLocale,
  IconImage,
  SizedBox,
  Spacing,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';

import { styles } from './styles';

interface WorkToDo {
  [key: string]: string;
}

interface RouteParams {
  workToDo?: WorkToDo[];
}

interface SceneProps {
  route?: RouteParams;
}

const WorkingItem = ({
  title,
  isShowDivider,
}: {
  title?: string;
  isShowDivider?: boolean;
}) => {
  return (
    <BlockView>
      <BlockView row>
        <IconImage
          source={iconStarOrange}
          size={20}
        />
        <CText margin={{ left: Spacing.SPACE_12 }}>{title}</CText>
      </BlockView>
      <ConditionView
        condition={Boolean(isShowDivider)}
        viewTrue={
          <SizedBox
            height={1}
            color={Colors.BORDER_COLOR}
            margin={{ vertical: Spacing.SPACE_12 }}
          />
        }
      />
    </BlockView>
  );
};
/**
 * Renders a list of work items from the route parameters
 */
export const Scene: React.FC<SceneProps> = memo(({ route }: SceneProps) => {
  const { i18n } = useI18n();
  const workItems = route?.workToDo || [];

  return (
    <ScrollView
      contentContainerStyle={styles.contentContainer}
      showsVerticalScrollIndicator={false}
    >
      <Card>
        {workItems.map((item, index) => {
          const isShowDivider = index !== workItems.length - 1;
          const title = getTextWithLocale(item, i18n.language) || '';

          return (
            <WorkingItem
              key={`workToDo-${index}`}
              title={title}
              isShowDivider={isShowDivider}
            />
          );
        })}
      </Card>
    </ScrollView>
  );
});

Scene.displayName = 'Scene';
