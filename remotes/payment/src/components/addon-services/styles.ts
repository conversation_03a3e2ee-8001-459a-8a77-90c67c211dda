import { Dimensions, StyleSheet } from 'react-native';
import { Colors, FontSizes, Spacing } from '@btaskee/design-system';

const { width } = Dimensions.get('window');
const SIZE_IMAGE = Math.round(width / 7);

export default StyleSheet.create({
  borderActive: {
    borderColor: Colors.PRIMARY_COLOR,
  },
  textActive: {
    color: Colors.PRIMARY_COLOR,
    fontSize: FontSizes.SIZE_12,
    textAlign: 'center',
  },
  image: {
    height: SIZE_IMAGE,
    width: SIZE_IMAGE,
  },
  txtName: {
    marginTop: Spacing.SPACE_16,
    fontSize: FontSizes.SIZE_12,
    textAlign: 'center',
  },
  containerAddOnService: {
    marginTop: Spacing.SPACE_08,
  },
  txtPanel: {
    marginVertical: Spacing.SPACE_16,
  },
  content: {
    justifyContent: 'space-around',
  },
  txtPrice: {
    textAlign: 'center',
    color: Colors.GREY,
    fontSize: FontSizes.SIZE_12,
    marginTop: 5,
  },
  buttonService: {
    // Empty style object kept for consistency with existing code
  },
});
