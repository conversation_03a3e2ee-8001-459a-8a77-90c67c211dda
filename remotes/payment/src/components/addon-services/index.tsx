import React, { useCallback, useMemo } from 'react';
import {
  BlockView,
  Card,
  Colors,
  CText,
  DeviceHelper,
  formatMoney,
  IconImage,
  Spacing,
  TouchableOpacity,
  usePostTaskStore,
} from '@btaskee/design-system';
import { useI18n } from '@src/hooks';
import { Requirement } from '@types';
import { get, isEmpty } from 'lodash-es';

import styles from './styles';

/**
 * Props for the AddOnServices component
 */
interface AddOnServicesProps {
  /** Selected requirements */
  data: Requirement[];
  /** Callback when a requirement is selected/deselected */
  onChange: (requirement: Requirement) => void;
  /** Whether premium service is selected */
  isPremium?: boolean;
}

/**
 * Component for displaying and selecting add-on services
 */
const AddOnServices: React.FC<AddOnServicesProps> = ({
  data,
  onChange,
  isPremium,
}) => {
  const { t, i18n } = useI18n();
  const { currency, forceTasker, service } = usePostTaskStore();

  /**
   * <PERSON><PERSON> click on a requirement item
   */
  const handleClick = useCallback(
    (requirement: Requirement) => () => {
      if (!requirement) return;
      onChange(requirement);
    },
    [onChange],
  );

  /**
   * Checks if a requirement is active (selected)
   */
  const checkActive = useMemo(
    () =>
      (requirement: Requirement): boolean => {
        if (!requirement || !data) return false;
        const isExist = data.find(
          (e: Requirement) => e && requirement?.type === e.type,
        );
        return Boolean(isExist);
      },
    [data],
  );

  /**
   * Get requirements from service or default to empty array
   */
  const requirements: Requirement[] = get(service, 'requirements', []) || [];

  /**
   * Renders the service items based on requirements and conditions
   */
  const shouldRenderService = useMemo(() => {
    if (!requirements || requirements.length === 0) return null;

    let requirePrimary = requirements;

    // If premium service is selected or force tasker doesn't have cleaning kit,
    // only show first two requirements
    if (
      isPremium === true ||
      (!isEmpty(forceTasker) &&
        typeof forceTasker.hasCleaningKit !== 'undefined' &&
        !forceTasker.hasCleaningKit)
    ) {
      requirePrimary = requirements.slice(0, 2);
    }

    return requirePrimary.map((requirement: Requirement, index: number) => {
      if (!requirement) return null;

      const active = checkActive(requirement);
      const costText = requirement?.duration
        ? `+${requirement?.duration}h`
        : `+${t('SV_HC_SCR2_DETAIL_BTN_COST_CURRENCY', {
          cost: formatMoney(requirement?.cost),
          currency: currency.sign,
        })}`;

      const iconSource = requirement?.icon
        ? { uri: requirement?.icon }
        : undefined;
      const textValue =
        requirement?.text && requirement?.text?.[i18n.language]
          ? requirement?.text?.[i18n.language]
          : '';

      return (
        <BlockView
          style={{ width: Math.round(DeviceHelper.WINDOW.WIDTH / 4) }}
          key={index}
        >
          <TouchableOpacity
            testID={`chooseServies-${index}`}
            activeOpacity={0.7}
            onPress={handleClick(requirement)}
            style={styles.buttonService}
          >
            <Card
              center
              style={active ? styles.borderActive : {}}
            >
              <IconImage
                source={iconSource}
                style={styles.image}
                color={active ? Colors.PRIMARY_COLOR : Colors.GREY}
              />
            </Card>
          </TouchableOpacity>
          <CText
            numberOfLines={2}
            bold
            style={[styles.txtName, active ? styles.textActive : {}]}
          >
            {textValue}
          </CText>
          <CText style={[styles.txtPrice, active ? styles.textActive : {}]}>
            {costText}
          </CText>
        </BlockView>
      );
    });
  }, [
    requirements,
    isPremium,
    i18n.language,
    t,
    currency.sign,
    forceTasker,
    checkActive,
    handleClick,
  ]);

  // Early return if no requirements
  if (!requirements || isEmpty(requirements)) return null;

  return (
    <BlockView style={styles.containerAddOnService}>
      <CText
        h4
        bold
        style={styles.txtPanel}
      >
        {t('SV_HC_SCR2_DETAIL_EXTRA_SV_TITLE')}
      </CText>
      <CText margin={{ bottom: Spacing.SPACE_24 }}>
        {t('SV_HC_SCR2_DETAIL_EXTRA_SV_NOTE')}
      </CText>
      <BlockView
        row
        style={styles.content}
      >
        {shouldRenderService}
      </BlockView>
    </BlockView>
  );
};

export default React.memo(AddOnServices);
