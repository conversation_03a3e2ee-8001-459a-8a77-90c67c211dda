import { StyleSheet } from 'react-native';
import { BorderRadius, Colors, Spacing } from '@btaskee/design-system';

export default StyleSheet.create({
  panel: {
    marginTop: Spacing.SPACE_16,
    marginBottom: Spacing.SPACE_16,
    justifyContent: 'space-between',
  },
  txtPanel: {},
  wrapBtn: {
    backgroundColor: Colors.GREEN,
    paddingVertical: Spacing.SPACE_08,
    paddingHorizontal: Spacing.SPACE_12,
    borderRadius: BorderRadius.RADIUS_16,
  },
  cardStyle: {
    paddingHorizontal: Spacing.SPACE_16,
  },
  wrapHomeNumber: {
    marginTop: Spacing.SPACE_16,
  },
  wrapContact: {
    borderTopWidth: 1,
    borderTopColor: Colors.BORDER_COLOR,
    marginTop: Spacing.SPACE_16,
    paddingTop: Spacing.SPACE_16,
  },
});
