/* eslint-disable react-native/no-raw-text */
import React, { createRef, useState } from 'react';
import { } from 'react-native';
// import { setAddress } from '@action/post-task';
import { icLocation, icMess, icUser } from '@assets/images';
import {
  BlockView,
  Card,
  CModal,
  CModalHandle,
  Colors,
  ConditionView,
  CText,
  CTextInput,
  FontSizes,
  IconImage,
  SizedBox,
  Spacing,
  TouchableOpacity,
  usePostTaskStore,
  validPhoneNumber,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';

import styles from './styles';

type LocationPostTaskProps = {
  title?: string;
};

const LocationPostTask = ({ title }: LocationPostTaskProps) => {
  const { t } = useI18n();
  const { address, homeNumber, setAddress } = usePostTaskStore();

  const modalRef = createRef<CModalHandle>();
  const [phone, setPhone] = useState(address?.phoneNumber);
  const [name, setName] = useState(address?.contact);

  const _openChangeContactModal = () => {
    // trackingServiceClick({
    //   campaignName: service?.isTet ? configSpecialPreBooking?.name : null,
    //   screenName: TrackingScreenNames.ConfirmPayment,
    //   serviceName: service.name,
    //   action: TRACKING_ACTION.ChangeInfo,
    // });

    modalRef?.current?.open && modalRef?.current?.open();
    setPhone(address?.phoneNumber);
    setName(address?.contact);
  };

  const _changeContactInfo = () => {
    if (phone && name) {
      const newAddress = { ...address };
      newAddress.phoneNumber = phone;
      newAddress.contact = name;

      setAddress(newAddress);
    }
  };

  if (!address) {
    return null;
  }

  return (
    <BlockView>
      <BlockView
        style={styles.panel}
        row
      >
        <CText
          bold
          h4
          style={styles.txtPanel}
        >
          {title ? title : t('LOCATION')}
        </CText>
      </BlockView>
      <Card style={styles.cardStyle}>
        <BlockView row>
          <BlockView>
            <IconImage
              source={icLocation}
              color={Colors.PRIMARY_COLOR}
              size={24}
            />
          </BlockView>
          <BlockView
            flex
            margin={{ left: Spacing.SPACE_12 }}
          >
            <ConditionView
              condition={Boolean(address?.shortAddress)}
              viewTrue={
                <>
                  <CText
                    testID={'shortAddressPT4'}
                    bold
                    size={FontSizes.SIZE_14}
                  >
                    {address?.shortAddress}
                  </CText>
                  <SizedBox height={Spacing.SPACE_08} />
                </>
              }
            />
            <CText color={Colors.GREY}>{address?.address}</CText>
          </BlockView>
        </BlockView>
        <ConditionView
          condition={Boolean(homeNumber)}
          viewTrue={
            <BlockView
              row
              style={styles.wrapHomeNumber}
            >
              <IconImage
                source={icMess}
                color={Colors.PRIMARY_COLOR}
                size={24}
              />
              <BlockView
                flex
                margin={{ left: Spacing.SPACE_12 }}
              >
                <CText
                  bold
                  size={FontSizes.SIZE_14}
                >
                  {t('HK_SV_ADDRESS_DETAIL')}
                </CText>
                <SizedBox height={Spacing.SPACE_08} />
                <CText
                  testID="homeNumberPT4"
                  color={Colors.GREY}
                >
                  {homeNumber}
                </CText>
              </BlockView>
            </BlockView>
          }
        />

        <ConditionView
          condition={Boolean(address?.contact && address?.phoneNumber)}
          viewTrue={
            <BlockView
              row
              style={styles.wrapContact}
            >
              <IconImage
                source={icUser}
                color={Colors.PRIMARY_COLOR}
              />
              <BlockView
                flex
                margin={{ horizontal: Spacing.SPACE_12 }}
              >
                <CText
                  testID="contactNamePT4"
                  bold
                  size={FontSizes.SIZE_14}
                >
                  {address?.contact}
                </CText>
                <SizedBox height={Spacing.SPACE_08} />
                <CText
                  testID={'contactPT4'}
                  color={Colors.GREY}
                >
                  {`(${address?.countryCode}) ${address?.phoneNumber}`}
                </CText>
              </BlockView>
              <BlockView>
                <TouchableOpacity
                  testID="btnChangeContact"
                  onPress={_openChangeContactModal}
                  style={styles.wrapBtn}
                >
                  <CText
                    bold
                    size={FontSizes.SIZE_12}
                    color={Colors.WHITE}
                  >
                    {t('EDIT')}
                  </CText>
                </TouchableOpacity>
              </BlockView>
            </BlockView>
          }
        />
      </Card>
      <CModal
        ref={modalRef}
        avoidKeyboard
        title={t('CONTACT_INFO_MODAL_TITLE')}
        actions={[
          {
            text: t('UPDATE'),
            disabled: !name || !validPhoneNumber(phone, address?.countryCode),
            onPress: _changeContactInfo,
          },
        ]}
      >
        <BlockView>
          <CTextInput
            testID="contactPhoneNumber"
            defaultValue={phone}
            countryCode={address?.countryCode}
            validType="phone"
            label={t('PHONE_NUMBER').toUpperCase()}
            onChangeText={(text: string) => setPhone(text.trim())}
            maxLength={12}
            keyboardType="phone-pad"
          />
          <CTextInput
            testID="contactName"
            defaultValue={name}
            label={t('CONTACT_NAME').toUpperCase()}
            onChangeText={(text: string) => setName(text.trim())}
            maxLength={40}
          />
        </BlockView>
      </CModal>
    </BlockView>
  );
};

export default LocationPostTask;
