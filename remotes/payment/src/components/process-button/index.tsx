import React from 'react';
import {
  CText,
  IconImage,
  SizedBox,
  Spacing,
  TouchableOpacity,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';
import { icArrowRight, iconProcess } from '@images';

import { styles } from './styles';

type ProcessButtonProps = {
  label?: string;
  onPress?: () => void;
};

export const ProcessButton = ({ label, onPress }: ProcessButtonProps) => {
  const { t } = useI18n();
  return (
    <TouchableOpacity
      activeOpacity={0.7}
      style={styles.container}
      onPress={onPress}
    >
      <IconImage source={iconProcess} />
      <SizedBox width={Spacing.SPACE_16} />
      <CText flex>{label || t('TASK_DETAIL')}</CText>
      <IconImage source={icArrowRight} />
    </TouchableOpacity>
  );
};
