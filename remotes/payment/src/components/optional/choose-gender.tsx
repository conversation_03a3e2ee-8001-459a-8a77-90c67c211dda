/**
 * <AUTHOR>
 * @email [<EMAIL>]
 * @create date 2023-11-24 11:27:46
 * @modify date 2023-11-24 11:27:46
 * @desc Choose pet
 */
import React, { useEffect, useRef, useState } from 'react';
import {
  AlertHolder,
  BlockView,
  OPTIONAL_CHOOSE_GENDER,
  usePostTaskStore,
} from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

import { useI18n } from '@hooks';

import ModalChooseGender, { ModalHandle } from './modal-choose-gender';
import OptionalItem from './optional-item';

const OptionalChooseGender = () => {
  const { t } = useI18n();
  const modalRef = useRef<ModalHandle>();

  const { gender, setGender } = usePostTaskStore();

  const [isGender, setIsGender] = useState(false);

  const showDescriptionChooseGender = () => {
    return AlertHolder.alert?.open?.({
      title: t('BOOKING_STEP_2.WHAT_IS_CHOOSE_GENDER'),
      message: t('BOOKING_STEP_2.DESCRIPTION_CHOOSE_GENDER'),
      actions: [{ text: t('PT1_DETAIL_CHOOSE_MANUAL_UNDERSTOOD') }],
    });
  };

  const onChangeGender = (value: string) => {
    setGender(value);
  };

  const onValueChange = (checked: boolean) => {
    setIsGender(checked);
    // ON
    if (checked) {
      modalRef?.current?.open && modalRef?.current?.open();
    } else {
      // OFF
      onChangeGender('');
    }
  };

  useEffect(() => {
    setIsGender(!isEmpty(gender));
  }, [gender]);

  return (
    <BlockView>
      <OptionalItem
        value={isGender}
        onValueChange={onValueChange}
        optionalName={OPTIONAL_CHOOSE_GENDER}
        showDescription={showDescriptionChooseGender}
      />
      <ModalChooseGender
        ref={modalRef}
        onSubmit={(gender) => {
          if (gender) {
            onChangeGender(gender);
          } else {
            // not fill or check to pet option
            setIsGender(false);
          }
        }}
        onCancel={() => {
          setIsGender(false);
        }}
      />
    </BlockView>
  );
};

export default OptionalChooseGender;
