import React, { forwardRef, useImperativeHandle, useState } from 'react';
import { Dimensions, StyleSheet } from 'react-native';
import {
  BlockView,
  BorderRadius,
  CheckBox,
  CModal,
  Colors,
  CText,
  GENDER_LIST,
  Spacing,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';

const { width } = Dimensions.get('window');

export interface ModalHandle {
  open?: () => void;
  close?: () => void;
}
interface ModalProps {
  onCancel: () => void;
  onSubmit: (value: any) => void;
}

export default forwardRef<ModalHandle, ModalProps>(
  ({ onCancel, onSubmit }, ref) => {
    const { t } = useI18n();

    const [gender, setGender] = useState<any>();

    const modalRef = React.useRef<ModalHandle>();

    const _handleClose = () => {
      modalRef?.current?.close && modalRef?.current?.close();
    };

    const _handleOpen = () => {
      setGender('');
      modalRef?.current?.open && modalRef?.current?.open();
    };

    const selectedGender = (isChecked, newGender) => {
      // add
      if (isChecked) {
        setGender(newGender);
      } else {
        // remove
        setGender('');
      }
    };

    // can call from parent component, with useRef
    useImperativeHandle(ref, () => ({
      open() {
        _handleOpen();
      },
      close() {
        _handleClose();
      },
    }));

    return (
      <BlockView style={styles.container}>
        <CModal
          ref={modalRef}
          avoidKeyboard
          title={t('BOOKING_STEP_2.CHOOSE_GENDER')}
          actions={[
            {
              text: t('PT1_MAP_POPUP_ADD_PET_SUBMIT'),
              onPress: () => onSubmit(gender),
              testID: 'btnOkPet',
            },
          ]}
          closeModalAction={() => {
            onCancel();
          }}
          onClose={() => {
            onCancel();
          }}
        >
          <BlockView style={styles.content}>
            <CText>{t('BOOKING_STEP_2.DESCRIPTION_CHOOSE_GENDER')}</CText>
            <BlockView
              center
              row
              style={styles.boxListPet}
            >
              {GENDER_LIST.map((genderItem, index) => (
                <CheckBox
                  testID={`Pet${index}`}
                  key={index}
                  checked={Boolean(gender === genderItem)}
                  onChecked={(isChecked) =>
                    selectedGender(isChecked, genderItem)
                  }
                  title={t(`BOOKING_STEP_2.GENDER_${genderItem}`)}
                  checkedIcon="dot-circle-o"
                  uncheckedIcon="circle-o"
                  textStyle={gender === genderItem ? styles.textActive : {}}
                />
              ))}
            </BlockView>
          </BlockView>
        </CModal>
      </BlockView>
    );
  },
);

const styles = StyleSheet.create({
  container: {},
  containerStyleInput: {
    paddingHorizontal: 0,
  },
  boxListPet: {
    marginTop: Spacing.SPACE_32,
    paddingHorizontal: width * 0.15,
    justifyContent: 'space-between',
  },
  btn: {
    marginTop: Spacing.SPACE_16,
  },
  borderActive: {
    borderColor: Colors.PRIMARY_COLOR,
  },
  textActive: {
    color: Colors.PRIMARY_COLOR,
    fontFamily: 'Montserrat-Bold',
    fontWeight: '600',
  },
  content: {
    marginBottom: Spacing.SPACE_16,
  },
  wrapBtn: {
    padding: Spacing.SPACE_16,
    borderRadius: BorderRadius.RADIUS_08,
    borderWidth: 1,
    borderColor: Colors.BORDER_COLOR,
  },
});
