/**
 * <AUTHOR>
 * @email [<EMAIL>]
 * @create date 2023-11-24 11:28:34
 * @modify date 2023-11-24 11:28:34
 * @desc Auto choose tasker
 */
import React, { useMemo } from 'react';
import { StyleSheet } from 'react-native';
import {
  AlertHolder,
  BEFORE_HOUR_POST_TASK_CHOOSE_TASKER,
  BlockView,
  BorderRadius,
  Colors,
  CText,
  formatMoney,
  getIsoCodeGlobal,
  ISO_CODE,
  Markdown,
  OPTIONAL_CHOOSE_TASKER,
  Spacing,
  usePostTaskStore,
  useSettingsStore,
} from '@btaskee/design-system';

import { useChangeData, useI18n } from '@hooks';

import OptionalItem from './optional-item';

interface AutoChooseTaskerProps {}

const OptionalAutoChooseTasker = ({}: AutoChooseTaskerProps) => {
  const { t } = useI18n();
  const { onChangeAutoChooseTasker } = useChangeData();
  const { isAutoChooseTasker, currency, service } = usePostTaskStore();

  const settings = useSettingsStore().settings;

  const contentDetailChooseTasker = useMemo(() => {
    if (getIsoCodeGlobal() === ISO_CODE.TH) {
      return t('PT1_DETAIL_CHOOSE_TASKER_EXPLAIN_3_TH');
    }
    return t('PT1_DETAIL_CHOOSE_TASKER_EXPLAIN_3');
  }, [t]);

  const showDescriptionForAutoChooseTasker = () => {
    const feeChoose =
      service?.priceSetting?.costForChooseTasker ||
      settings?.settingSystem?.costForChooseTasker;
    const fee = formatMoney(feeChoose);
    const feeTxt = `${t('PT1_DETAIL_CHOOSE_MANUAL_FEES')}: ${t(
      'PT1_DETAIL_CHOOSE_MANUAL_COST_AND_CURRENCY',
      {
        cost: fee,
        currency: currency?.sign,
      },
    )}`;

    return AlertHolder.alert?.open({
      title: t('PT1_DETAIL_OPTION_ITEM_CHOOSE_MANUAL'),
      message: (
        <BlockView
          padding={{ horizontal: Spacing.SPACE_16, top: Spacing.SPACE_20 }}
        >
          <Markdown
            text={t('PT1_DETAIL_CHOOSE_TASKER_EXPLAIN_1')}
            paragraphStyle={styles.markdownText}
          />
          <Markdown
            text={t('PT1_DETAIL_CHOOSE_TASKER_EXPLAIN_2', {
              hour: BEFORE_HOUR_POST_TASK_CHOOSE_TASKER,
            })}
            paragraphStyle={styles.markdownText}
          />
          <CText
            bold
            center
            color={Colors.RED}
            padding={{ vertical: Spacing.SPACE_12 }}
          >
            {feeTxt}
          </CText>
          <BlockView
            border={{ width: 1, color: Colors.PRIMARY_COLOR }}
            padding={Spacing.SPACE_16}
            margin={{ top: Spacing.SPACE_16 }}
            radius={BorderRadius.RADIUS_08}
            backgroundColor={Colors.YELLOW_1}
          >
            <CText center>{contentDetailChooseTasker}</CText>
          </BlockView>
        </BlockView>
      ),
      actions: [{ text: t('PT1_DETAIL_CHOOSE_MANUAL_UNDERSTOOD') }],
    });
  };

  const onValueChange = (checked: boolean) => {
    onChangeAutoChooseTasker();
    checked ? showDescriptionForAutoChooseTasker() : null;
  };

  return (
    <OptionalItem
      value={!isAutoChooseTasker}
      onValueChange={onValueChange}
      optionalName={OPTIONAL_CHOOSE_TASKER}
      showDescription={showDescriptionForAutoChooseTasker}
    />
  );
};

const styles = StyleSheet.create({
  markdownText: {
    textAlign: 'center',
  },
});

export default OptionalAutoChooseTasker;
