/**
 * @file components/optional/modal-choose-pet.js
 * <AUTHOR>
 * @email [<EMAIL>]
 * @create date 2023-11-24 11:26:16
 * @modify date 2023-11-24 11:26:16
 * @desc Modal choose pet
 */

import React, {
  forwardRef,
  useImperativeHandle,
  useMemo,
  useState,
} from 'react';
import { icCat, icDog } from '@assets/images';
import {
  BlockView,
  CheckBox,
  CModal,
  Colors,
  CText,
  CTextInput,
  CustomMarkDown,
  formatMoney,
  IconImage,
  Spacing,
  TouchableOpacity,
  usePostTaskStore,
} from '@btaskee/design-system';
import { useI18n } from '@src/hooks';
import { isEmpty, uniq, without } from 'lodash-es';

import { IAddons } from '../../../';
import styles from './styles';

const PETS_LIST = [
  {
    icon: icDog,
    name: '<PERSON>O<PERSON>',
  },
  { icon: icCat, name: 'CAT' },
];

export interface ModalHandle {
  open?: () => void;
  close?: () => void;
}
interface ModalProps {
  onCancel: () => void;
  onSubmit: (value: any) => void;
  petOption?: IAddons;
  petSelected?: { [key: string]: string }[];
}

export default forwardRef<ModalHandle, ModalProps>(
  ({ onCancel, onSubmit, petOption, petSelected }, ref) => {
    const { t } = useI18n();
    const { currency } = usePostTaskStore();

    const [pet, setPet] = useState([]);
    const [otherPet, setOtherPet] = useState('');

    const modalRef = React.useRef<ModalHandle>();

    const _handleClose = () => {
      modalRef?.current?.close && modalRef?.current?.close();
    };

    const _handleOpen = () => {
      modalRef?.current?.open && modalRef?.current?.open();
    };

    const selectedPet = (isChecked?: boolean, newPet?: string) => {
      let arrPet = [...pet];
      // add
      if (isChecked) {
        arrPet = uniq(arrPet.concat([newPet]));
      } else {
        // remove
        arrPet = without(arrPet, newPet);
      }
      setPet(arrPet);
    };

    // can call from parent component, with useRef
    useImperativeHandle(ref, () => ({
      open() {
        _handleOpen();
      },
      close() {
        _handleClose();
      },
    }));

    const getPetValue = useMemo(() => {
      const newPets: any = pet.map((e) => {
        return { name: e };
      });
      if (otherPet?.trim()) {
        newPets.push({ other: otherPet?.trim() });
      }
      return newPets;
    }, [otherPet, pet]);

    const content = useMemo(() => {
      if (petOption?.cost) {
        return (
          <BlockView>
            <CustomMarkDown
              testID="txtPetFee"
              text={t('POST_TASK_STEP_2.OPTION_PET_FEE')}
              params={[
                {
                  key: 'price',
                  value: t('COST_AND_CURRENCY', {
                    cost: formatMoney(petOption?.cost),
                    currency: currency?.sign,
                  }),
                  style: styles.txtPrice,
                },
              ]}
            />
            <CText
              testID="txtPetNote"
              margin={{ top: Spacing.SPACE_24 }}
            >
              {t('POST_TASK_STEP_2.OPTION_PET_NOTE')}
            </CText>
          </BlockView>
        );
      }
      return (
        <CText testID="txtPetNote">
          {t('POST_TASK_STEP_2.OPTION_PET_NOTE')}
        </CText>
      );
    }, [currency, petOption?.cost, t]);

    return (
      <BlockView style={styles.container}>
        <CModal
          ref={modalRef}
          avoidKeyboard
          title={t('PT1_DETAIL_OPTION_HAVE_PET')}
          titleStyle={styles.titleStyle}
          hideButtonClose
          actions={[
            {
              text: t('PT1_MAP_POPUP_ADD_PET_SUBMIT'),
              onPress: () => onSubmit(getPetValue),
              testID: 'btnOkPet',
            },
          ]}
          onClose={() => {
            if (isEmpty(getPetValue) || isEmpty(petSelected)) {
              onCancel();
              setPet([]);
              setOtherPet('');
            }
          }}
        >
          <BlockView margin={{ bottom: Spacing.SPACE_12 }}>
            {content}
            <BlockView margin={{ top: Spacing.SPACE_08 }}>
              {PETS_LIST.map((pe, index) => {
                const isChecked = Boolean(pet.find((e) => e === pe.name));
                return (
                  <TouchableOpacity
                    testID={`Pet${index}`}
                    key={`Pet${index}`}
                    onPress={() => selectedPet(!isChecked, pe.name)}
                  >
                    <BlockView
                      row
                      horizontal
                      jBetween
                      border={{
                        bottom: { width: 1, color: Colors.UNDERLAY_COLOR },
                      }}
                      padding={{ vertical: Spacing.SPACE_12 }}
                    >
                      <BlockView
                        row
                        horizontal
                      >
                        <IconImage
                          source={pe.icon}
                          size={Spacing.SPACE_24}
                        />
                        <CText margin={{ left: Spacing.SPACE_12 }}>
                          {t(pe.name)}
                        </CText>
                      </BlockView>
                      <CheckBox
                        key={index}
                        checked={isChecked}
                        disabled={true}
                      />
                    </BlockView>
                  </TouchableOpacity>
                );
              })}
            </BlockView>
            <CTextInput
              testID="tfOtherPet"
              upperCase
              containerStyle={styles.containerStyleInput}
              placeholder={t('OTHER')}
              maxLength={50}
              value={otherPet}
              onChangeText={(e) => setOtherPet(e)}
            />
          </BlockView>
        </CModal>
      </BlockView>
    );
  },
);
