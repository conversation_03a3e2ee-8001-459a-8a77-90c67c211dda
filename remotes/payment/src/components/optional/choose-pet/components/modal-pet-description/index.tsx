import React from 'react';
import { ScrollView } from 'react-native';
import { bgPet } from '@assets/images';
import {
  BlockView,
  ConditionView,
  CText,
  CustomMarkDown,
  DeviceHelper,
  FastImage,
  formatMoney,
  Spacing,
  usePostTaskStore,
} from '@btaskee/design-system';
import { useI18n } from '@src/hooks';

import { MarkdownList } from '@components';

import { IAddons } from '../../../';
import styles from './styles';

interface IModalPetDescriptionProps {
  petOption?: IAddons;
}

export const ModalPetDescription = ({
  petOption,
}: IModalPetDescriptionProps) => {
  const { t } = useI18n();
  const { currency } = usePostTaskStore();

  return (
    <BlockView
      margin={{ top: Spacing.SPACE_32 }}
      maxHeight={Math.round(DeviceHelper.WINDOW.HEIGHT / 1.5)}
    >
      <ScrollView scrollIndicatorInsets={{ right: 1 }}>
        <BlockView
          center
          margin={{ vertical: Spacing.SPACE_08, bottom: Spacing.SPACE_16 }}
        >
          <FastImage
            source={bgPet}
            style={styles.bgImg}
          />
        </BlockView>
        <BlockView margin={{ horizontal: Spacing.SPACE_16 }}>
          <ConditionView
            condition={Boolean(petOption?.cost)}
            viewTrue={
              <CustomMarkDown
                testID="txtPetDescriptionPet"
                text={t('POST_TASK_STEP_2.OPTION_PET_FEE')}
                params={[
                  {
                    key: 'price',
                    value: t('COST_AND_CURRENCY', {
                      cost: formatMoney(petOption?.cost),
                      currency: currency?.sign,
                    }),
                    style: styles.txtPrice,
                  },
                ]}
                containerStyles={styles.txtDescription}
              />
            }
          />
          <CText bold>{t('POST_TASK_STEP_2.SOME_RECOMMEND')}</CText>
          <MarkdownList
            data={[
              t('POST_TASK_STEP_2.RECOMMEND_1'),
              t('POST_TASK_STEP_2.RECOMMEND_2'),
            ]}
            containerStyle={styles.containerMarkdown}
            markdownStyle={{
              textStyle: styles.textStyle,
            }}
            ItemSeparatorComponent={<BlockView height={Spacing.SPACE_08} />}
          />
        </BlockView>
      </ScrollView>
    </BlockView>
  );
};
