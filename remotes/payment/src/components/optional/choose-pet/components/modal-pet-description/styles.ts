import { StyleSheet } from 'react-native';
import {
  Colors,
  DeviceHelper,
  FontFamily,
  Spacing,
} from '@btaskee/design-system';

const WIDTH_IMG = Math.round(DeviceHelper.WINDOW.WIDTH - 2 * Spacing.SPACE_16);

export default StyleSheet.create({
  bgImg: {
    width: WIDTH_IMG,
    height: Math.round(WIDTH_IMG / 2),
  },
  txtPrice: {
    color: Colors.PRIMARY_COLOR,
    fontFamily: FontFamily.FONT_BOLD,
    fontWeight: '700',
  },
  containerMarkdown: {
    paddingHorizontal: 0,
  },
  textStyle: {
    color: Colors.BLACK,
  },
  txtDescription: {
    marginBottom: Spacing.SPACE_16,
  },
});
