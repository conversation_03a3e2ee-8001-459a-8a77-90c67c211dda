/**
 * <AUTHOR>
 * @email [<EMAIL>]
 * @create date 2023-11-24 11:27:46
 * @modify date 2023-11-24 11:27:46
 * @desc Choose pet
 */
import React, { useEffect, useState } from 'react';
import {
  <PERSON>donsName,
  AlertHolder,
  BlockView,
  OPTIONAL_CHOOSE_PET,
} from '@btaskee/design-system';
import { cloneDeep, isEmpty } from 'lodash-es';

import { useI18n } from '@hooks';

import { IAddons } from '../';
import OptionalItem from '../optional-item';
import ModalChoosePet, { ModalHandle } from './components/modal-choose-pet';
import { ModalPetDescription } from './components/modal-pet-description';

interface ChoosePetProps {
  pet: any;
  onChangePet?: (value?: any) => void;
  petOption?: IAddons;
  setAddons?: (addons: IAddons[]) => void;
  addons?: IAddons[];
}

const OptionalChoosePet = ({
  onChangePet,
  pet,
  petOption,
  setAddons,
  addons,
}: ChoosePetProps) => {
  const { t } = useI18n();

  const [isPet, setIsPet] = useState(false);
  const modalRef = React.useRef<ModalHandle>();

  const showDescriptionForPet = () => {
    return AlertHolder.alert.open({
      title: t('PT1_DETAIL_OPTION_HAVE_PET'),
      message: <ModalPetDescription petOption={petOption} />,
      actions: [{ text: t('PT1_DETAIL_CHOOSE_MANUAL_UNDERSTOOD') }],
    });
  };

  useEffect(() => {
    setIsPet(!isEmpty(pet));
  }, [pet]);

  const unsetPet = () => {
    const index = addons?.findIndex(
      (item: IAddons) => item?.name === AddonsName.Pet,
    );
    if (index !== -1) {
      const newAddons = cloneDeep(addons);
      newAddons?.splice(index, 1);
      setAddons?.(newAddons || []);
    }
  };

  const onValueChange = (checked: boolean) => {
    setIsPet(checked);
    // ON
    if (checked) {
      modalRef?.current?.open && modalRef?.current?.open();
      return;
    }
    unsetPet();
    // OFF
    onChangePet?.(null);
  };

  const onSubmit = async (pets: object[]) => {
    if (pets && pets.length > 0) {
      if (!isEmpty(petOption)) {
        setAddons && (await setAddons([...addons, petOption]));
      }
      onChangePet?.(pets);
    } else {
      // not fill or check to pet option
      setIsPet(false);
      unsetPet();
    }
  };

  return (
    <BlockView>
      <OptionalItem
        value={isPet}
        onValueChange={onValueChange}
        optionalName={OPTIONAL_CHOOSE_PET}
        showDescription={showDescriptionForPet}
      />
      <ModalChoosePet
        ref={modalRef}
        onSubmit={(pets) => onSubmit(pets)}
        onCancel={() => {
          setIsPet(false);
          unsetPet();
        }}
        petOption={petOption}
        petSelected={pet}
      />
    </BlockView>
  );
};

export default OptionalChoosePet;
