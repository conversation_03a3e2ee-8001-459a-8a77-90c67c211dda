/**
 * <AUTHOR>
 * @email [<EMAIL>]
 * @create date 2023-11-24 11:28:34
 * @modify date 2023-11-24 11:28:34
 * @desc Auto choose tasker
 */
import React from 'react';
import { StyleSheet } from 'react-native';
import { icQuestion } from '@assets/images';
import {
  BlockView,
  Colors,
  CText,
  FastImage,
  IconImage,
  OPTIONAL_BOOKING_TASK,
  Spacing,
  Switch,
  TouchableOpacity,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';

interface OptionalItemProps {
  optionalName: string;
  value?: boolean;
  onValueChange: (value: boolean) => void;
  showDescription: () => void;
}

const OptionalItem = ({
  onValueChange,
  showDescription,
  optionalName,
  value,
}: OptionalItemProps) => {
  const { t } = useI18n();

  const dataOfItem = OPTIONAL_BOOKING_TASK.find(
    (item) => item.optionalName === optionalName,
  );
  if (!dataOfItem) {
    return null;
  }

  return (
    <BlockView
      row
      style={styles.group}
    >
      <BlockView
        row
        style={styles.left}
      >
        <FastImage
          source={dataOfItem?.image}
          style={styles.imageIcon}
        />
        <BlockView flex>
          <CText
            testID={`txt${dataOfItem.optionalName}`}
            style={styles.txtLabel}
          >
            {t(dataOfItem.label)}
          </CText>
        </BlockView>

        <TouchableOpacity
          testID={`whatIs${dataOfItem.optionalName}`}
          onPress={showDescription}
          style={styles.btnInfo}
        >
          <IconImage
            source={icQuestion}
            size={16}
            color={Colors.SECONDARY_COLOR}
          />
        </TouchableOpacity>
      </BlockView>
      <Switch
        style={styles.switch}
        testID={dataOfItem.optionalName}
        value={value}
        onValueChange={onValueChange}
      />
    </BlockView>
  );
};

const styles = StyleSheet.create({
  imageIcon: {
    marginLeft: 4,
    marginRight: 12,
    width: 24,
    height: 24,
    tintColor: Colors.PRIMARY_COLOR,
  },
  txtLabel: {},
  btnInfo: {
    top: -5,
    paddingLeft: 5,
    paddingRight: 10,
  },
  left: {
    flex: 1,
    alignItems: 'center',
  },
  group: {
    marginBottom: Spacing.SPACE_20,
  },
  switch: {
    zIndex: 1,
  },
});

export default OptionalItem;
