/**
 * <AUTHOR>
 * @email [<EMAIL>]
 * @create date 2023-11-24 11:28:12
 * @modify date 2023-11-24 11:28:12
 * @desc Choose favourite tasker
 */
import React from 'react';
import {
  AlertHolder,
  OPTIONAL_CHOOSE_FAV_TASKER,
  usePostTaskStore,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';

import OptionalItem from './optional-item';

interface ChooseFavouriteTaskerProps {
  blacklist?: any;
}

const OptionalChooseFavouriteTasker = ({
  blacklist,
}: ChooseFavouriteTaskerProps) => {
  const { t } = useI18n();
  const { isFavouriteTasker, setIsFavouriteTasker } = usePostTaskStore();

  const showDescriptionForFavoriteTasker = () => {
    return AlertHolder.alert?.open?.({
      title: t('FAV_TASKER_TITLE'),
      message: [
        t('FAV_TASKER_DESCRIPTION_EXPLAIN_1'),
        t('FAV_TASKER_DESCRIPTION_EXPLAIN_2'),
      ],
      actions: [{ text: t('PT1_DETAIL_CHOOSE_MANUAL_UNDERSTOOD') }],
    });
  };

  const onValueChange = (checked: boolean) => {
    setIsFavouriteTasker(checked);
  };

  if (blacklist && blacklist.indexOf('FAV_TASKER') !== -1) {
    return null;
  }
  return (
    <OptionalItem
      value={isFavouriteTasker}
      onValueChange={onValueChange}
      optionalName={OPTIONAL_CHOOSE_FAV_TASKER}
      showDescription={showDescriptionForFavoriteTasker}
    />
  );
};

export default OptionalChooseFavouriteTasker;
