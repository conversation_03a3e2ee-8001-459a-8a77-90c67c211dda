/* eslint-disable react-hooks/exhaustive-deps */
/**
 * @file components/optional/index.js
 * <AUTHOR>
 * @email [<EMAIL>]
 * @create date 2023-11-24 10:59:04
 * @modify date 2023-11-24 10:59:04
 * @desc Optional when book task
 */
import React from 'react';
import { StyleSheet, ViewStyle } from 'react-native';
import {
  AddonsName,
  BlockView,
  ConditionView,
  CText,
  Spacing,
  usePostTaskStore,
} from '@btaskee/design-system';
import { IObjectText } from '@types';
import { isEmpty } from 'lodash-es';

import { useI18n } from '@hooks';

import OptionalAutoChooseTasker from './auto-choose-tasker';
import OptionalChooseFavouriteTasker from './choose-favourite-tasker';
import OptionalChooseGender from './choose-gender';
import OptionalChoosePet from './choose-pet';

export type IAddons = {
  name?: string;
  cost?: number;
  text?: IObjectText;
  // status?: StatusOption;
};

interface OptionalProps {
  optional?: any;
  blacklist?: any;
  pet?: any;
  gender?: string;
  style?: ViewStyle;
  onChangePet?: (value: any) => void;
  addons?: IAddons[];
  setAddons?: (addons: IAddons[]) => void;
  addonsSelected?: IAddons[];
}

const Optional = ({
  style,
  optional,
  blacklist,
  pet,
  gender,
  addons,
  onChangePet,
  setAddons,
  addonsSelected,
}: OptionalProps) => {
  const { t } = useI18n();
  const forceTasker = {};
  const { isAutoChooseTasker, isFavouriteTasker } = usePostTaskStore();
  // const forceTasker = useAppSelector(PostTaskSelector.forceTasker);

  const shouldRenderAutoChooseTasker = React.useMemo(() => {
    // ON/OFF from service
    if (!optional?.isAutoChooseTaskerEnabled) {
      return null;
    }
    return <OptionalAutoChooseTasker />;
  }, [isAutoChooseTasker]);

  const shouldRenderPet = React.useMemo(() => {
    if (!onChangePet) {
      return null;
    }
    const petOption = addons?.find((item) => item?.name === AddonsName.Pet);
    return (
      <OptionalChoosePet
        onChangePet={onChangePet}
        pet={pet}
        petOption={petOption}
        setAddons={setAddons}
        addons={addonsSelected}
      />
    );
  }, [pet, addons, addonsSelected]);

  const shouldRenderFavoriteTasker = React.useMemo(() => {
    if (blacklist && blacklist.indexOf('FAV_TASKER') !== -1) {
      return null;
    }
    return <OptionalChooseFavouriteTasker blacklist={blacklist} />;
  }, [blacklist, isFavouriteTasker]);

  const shouldRenderChooseGender = React.useMemo(() => {
    if (!optional?.isGenderEnabled) {
      return null;
    }
    return <OptionalChooseGender />;
  }, [gender]);

  return (
    <BlockView style={[styles.container, style]}>
      <CText
        h4
        bold
        style={styles.txtPanel}
      >
        {t('PT1_DETAIL_OPTION_TITLE')}
      </CText>
      <BlockView style={styles.content}>
        {shouldRenderPet}
        <ConditionView
          condition={isEmpty(forceTasker)}
          viewTrue={
            <>
              {shouldRenderAutoChooseTasker}
              {shouldRenderFavoriteTasker}
              {shouldRenderChooseGender}
            </>
          }
        />
      </BlockView>
    </BlockView>
  );
};

const styles = StyleSheet.create({
  content: {},

  container: {
    marginTop: '6%',
  },
  txtPanel: {
    marginTop: Spacing.SPACE_16,
    marginBottom: Spacing.SPACE_20,
  },
});

export default Optional;
