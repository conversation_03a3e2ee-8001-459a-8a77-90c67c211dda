import { ISO_CODE, getIsoCodeGlobal } from '@btaskee/design-system';
import { IParamsGetPrice, IPrice } from '@types';
import { IRespond } from '@helper';

import * as API_ID from './id';
import * as API_MY from './my';
import * as API_TH from './th';
import * as API_VN from './vn';

export const getPiceAPI = async (data: IParamsGetPrice): Promise<IRespond<IPrice>> => {
  const apis = {
    [ISO_CODE.VN]: API_VN.getPrice,
    [ISO_CODE.TH]: API_TH.getPrice,
    [ISO_CODE.ID]: API_ID.getPrice,
    [ISO_CODE.MY]: API_MY.getPrice,
  };
  const apiByCountry = apis[getIsoCodeGlobal()];
  return apiByCountry?.(data);
};