import { ISO_CODE, getIsoCodeGlobal } from '@btaskee/design-system';
import { IDataBooking } from '@types';

import * as API_ID from './id';
import * as API_MY from './my';
import * as API_TH from './th';
import * as API_VN from './vn';

export const postTaskAPI = async (options: { data: IDataBooking; token?: string }) => {
  const apis = {
    [ISO_CODE.VN]: API_VN.postTask,
    [ISO_CODE.TH]: API_TH.postTask,
    [ISO_CODE.ID]: API_ID.postTask,
    [ISO_CODE.MY]: API_MY.postTask,
  };
  const apiByCountry = apis[getIsoCodeGlobal()];
  return apiByCountry?.(options);
};