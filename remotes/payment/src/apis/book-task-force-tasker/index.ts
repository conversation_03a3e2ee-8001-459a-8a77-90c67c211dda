import { getIsoCodeGlobal, ISO_CODE } from '@btaskee/design-system';
import { IDataBooking } from '@types';

import * as API_ID from './id';
import * as API_MY from './my';
import * as API_TH from './th';
import * as API_VN from './vn';

export const bookTaskForceTasker = async (options: {
  data: IDataBooking;
  token?: string;
}) => {
  const apis = {
    [ISO_CODE.VN]: API_VN.bookTaskForceTasker,
    [ISO_CODE.TH]: API_TH.bookTaskForceTasker,
    [ISO_CODE.ID]: API_ID.bookTaskForceTasker,
    [ISO_CODE.MY]: API_MY.bookTaskForceTasker,
  };
  const apiByCountry = apis[getIsoCodeGlobal()];
  return apiByCountry?.(options);
};
