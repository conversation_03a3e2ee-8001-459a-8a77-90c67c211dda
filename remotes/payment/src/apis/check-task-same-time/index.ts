import { getIsoCodeGlobal, ISO_CODE } from '@btaskee/design-system';
import { IParamsCheckTaskSameTime } from '@types';

import * as API_ID from './id';
import * as API_MY from './my';
import * as API_TH from './th';
import * as API_VN from './vn';

export const checkTaskSameTime = async (data: IParamsCheckTaskSameTime) => {
  const apis = {
    [ISO_CODE.VN]: API_VN.checkTaskSameTime,
    [ISO_CODE.TH]: API_TH.checkTaskSameTime,
    [ISO_CODE.ID]: API_ID.checkTaskSameTime,
    [ISO_CODE.MY]: API_MY.checkTaskSameTime,
  };
  const apiByCountry = apis[getIsoCodeGlobal()];
  return apiByCountry?.(data);
};
