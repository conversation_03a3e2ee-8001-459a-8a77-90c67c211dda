import { Dimensions, StyleSheet } from 'react-native';
import { Colors, FontSizes, Spacing } from '@btaskee/design-system';

const { width } = Dimensions.get('window');
const SIZE_IMAGE = Math.round(width / 7);

export default StyleSheet.create({
  container: {
    flex: 1,
    padding: 0,
    backgroundColor: Colors.BG_COLOR,
  },
  containerScroll: {
    flexGrow: 1,
    paddingBottom: '40%',
    paddingHorizontal: Spacing.SPACE_16,
    backgroundColor: Colors.WHITE,
  },
  txtPanel: {
    marginVertical: Spacing.SPACE_16,
  },
  txtDescription: {
    color: Colors.BLACK,
    lineHeight: 18,
  },
  txtPrice: {
    textAlign: 'center',
    color: Colors.GREY,
    fontSize: FontSizes.SIZE_12,
    marginTop: 5,
  },
  txtName: {
    marginTop: Spacing.SPACE_16,
    fontSize: FontSizes.SIZE_12,
    textAlign: 'center',
  },
  buttonService: {},
  borderActive: {
    borderColor: Colors.PRIMARY_COLOR,
  },
  textActive: {
    color: Colors.PRIMARY_COLOR,
    fontSize: FontSizes.SIZE_12,
    textAlign: 'center',
  },
  image: {
    height: SIZE_IMAGE,
    width: SIZE_IMAGE,
  },
  content: {
    justifyContent: 'space-around',
  },
  containerAddOnService: {
    marginTop: '6%',
  },
  wrapCleaningTool: {
    marginTop: Spacing.SPACE_16,
  },
  txtParagraph: {
    marginTop: 0,
    marginBottom: Spacing.SPACE_24,
  },
});
