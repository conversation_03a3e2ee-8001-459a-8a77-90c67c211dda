/**
 * ChooseAddress Screen
 *
 * Displays a list of locations for the user to choose from when booking a cleaning service.
 * Allows users to select an address which will be used for the cleaning service.
 */
import React, { memo, useCallback, useEffect, useRef } from 'react';
import { <PERSON><PERSON>, Animated, FlatList, ListRenderItem } from 'react-native';
import {
  AnimationHelpers,
  BlockView,
  CText,
  // postTask,
  SizedBox,
  usePostTaskStore,
} from '@btaskee/design-system';
import { RootStackScreenProps } from '@src/types/navigation';
import { IAddress } from '@types';

import { LocationItem } from '@components';
import { useI18n } from '@hooks';

import styles from './styles';

// Mock data for locations - in a real app, this would likely come from an API or store
const LOCATION_DATA: IAddress[] = [
  {
    _id: 'x50e93206c3799610281fd67108941977',
    address: 'Vinh, Nghệ An, Việt Nam',
    city: 'Ng<PERSON>ệ An',
    contact: 'Quan test',
    country: 'VN',
    countryCode: '+84',
    description: 'ns',
    district: 'Vinh',
    homeType: 'HOME',
    isoCode: 'VN',
    lat: 18.6795848,
    lng: 105.6813333,
    phoneNumber: '0397411511',
    shortAddress: 'Vinh Nghe An',
  },
  {
    _id: 'x02d858e8bb2c0e01de8216b7a6a3d801',
    address: 'Thanh Hóa, Việt Nam',
    city: 'Thanh Hóa',
    contact: 'Quan test',
    country: 'VN',
    countryCode: '+84',
    description: 'ma',
    district: 'Thành phố Thanh Hoá',
    homeType: 'HOME',
    isoCode: 'VN',
    lat: 19.806692,
    lng: 105.7851816,
    phoneNumber: '0397411511',
    shortAddress: 'thanh hoá',
  },
  {
    _id: 'xd6a36168ada192611c922cc962907a04',
    address: 'Quận 1, Hồ Chí Minh, Việt Nam',
    city: 'Hồ Chí Minh',
    contact: 'ba test',
    country: 'VN',
    countryCode: '+84',
    description: 'uỷ ban',
    district: 'Quận 1',
    homeType: 'HOME_OFFICE',
    isoCode: 'VN',
    lat: 10.7756587,
    lng: 106.7004238,
    phoneNumber: '0777777777',
    shortAddress: 'District 1 Ho Chi Minh City',
  },
  {
    _id: 'xc3f74b0de1f15cfcb59df62a8ad60791',
    address:
      'Place in Saigon apartment 2, Phường 22, Bình Thạnh, Hồ Chí Minh, Việt Nam',
    city: 'Hồ Chí Minh',
    contact: 'ba test',
    country: 'VN',
    countryCode: '+84',
    description: '222',
    district: 'Bình Thạnh',
    homeType: 'HOME',
    isoCode: 'VN',
    lat: 10.7906155,
    lng: 106.7173618,
    phoneNumber: '0777777777',
    shortAddress: 'Vietnam Bình Thạnh',
  },
  {
    _id: 'x777a0907842372808c64869bda77d56b',
    address:
      'Chung Cư, A20.5/A20.5 Tân Phước, Khu phố 2, Quận 11, Hồ Chí Minh, Việt Nam',
    city: 'Hồ Chí Minh',
    contact: 'ba test',
    country: 'VN',
    countryCode: '+84',
    description: '1',
    district: 'Quận 11',
    homeType: 'HOME_OFFICE',
    isoCode: 'VN',
    lat: 10.7617947,
    lng: 106.6602193,
    phoneNumber: '0777777777',
    shortAddress: 'Chung Cư A20.5',
  },
  {
    _id: 'x07c6ccd2f6cbbfc9772a085e7008e3ff',
    address:
      'PJV6+GVR, Đường Số 17A, Bình Trị Đông B, Bình Tân, TP. Hồ Chí Minh, Việt Nam',
    city: 'Hồ Chí Minh',
    contact: 'ba test',
    country: 'VN',
    countryCode: '+84',
    description: 'tầng 6',
    district: 'Bình Tân',
    homeType: 'HOME',
    isoCode: 'VN',
    lat: 10.7440898,
    lng: 106.6121872,
    phoneNumber: '0777777777',
    shortAddress: 'PJV6+GVR Đường Số 17A',
  },
  {
    _id: 'x24b73aa2cd08d0a07efdb25c5b796c29',
    address:
      'Trung Tâm Thương Mại Gigamall, Đường Phạm Văn Đồng, Hiệp Bình Chánh, Thủ Đức, Hồ Chí Minh, Việt Nam',
    city: 'Hồ Chí Minh',
    contact: 'ba test',
    country: 'VN',
    countryCode: '+84',
    description: 'số 22',
    district: 'Thủ Đức',
    homeType: 'HOME',
    isoCode: 'VN',
    lat: 10.8276425,
    lng: 106.7214707,
    phoneNumber: '0777777777',
    shortAddress: '240 242',
  },
  {
    _id: 'x52c330cf19a1b09f0393f115578a64e5',
    address:
      'Trung Tâm Thương Mại Gigamall, Đường Phạm Văn Đồng, Hiệp Bình Chánh, Thủ Đức, Hồ Chí Minh, Việt Nam',
    city: 'Hồ Chí Minh',
    contact: 'ba test',
    country: 'VN',
    countryCode: '+84',
    description: 'số22',
    district: 'Thủ Đức',
    homeType: 'HOME',
    isoCode: 'VN',
    lat: 10.8276425,
    lng: 106.7214707,
    phoneNumber: '0777777777',
    shortAddress: '240 242',
  },
  {
    _id: 'xecf011a36230f35c740fa7417337f378',
    address: 'Bình Chánh, Hồ Chí Minh, Việt Nam',
    city: 'Hồ Chí Minh',
    contact: 'ba test',
    country: 'VN',
    countryCode: '+84',
    description: '1',
    district: 'Bình Chánh',
    homeType: 'HOME_OFFICE',
    isoCode: 'VN',
    lat: 10.687392,
    lng: 106.5938538,
    phoneNumber: '0777777777',
    shortAddress: 'Bình Chánh Hồ Chí Minh',
  },
  {
    _id: 'x78aea451e9fcd376764917006273dfdf',
    address:
      'VẠN PHÚC CITY, Quốc lộ 13, Hiệp Bình Phước, Thủ Đức, Hồ Chí Minh, Việt Nam',
    city: 'Hồ Chí Minh',
    contact: 'ba test',
    country: 'VN',
    countryCode: '+84',
    description: 'số 22',
    district: 'Thủ Đức',
    homeType: 'HOME',
    isoCode: 'VN',
    lat: 10.8403244,
    lng: 106.7160982,
    phoneNumber: '0777777777',
    shortAddress: '375 Quốc lộ 13',
  },
  {
    _id: 'x3a4c5e289aef7446c49ec105e1d2f447',
    address:
      'Công ty TNHH bTaskee, Đường Trần Não, P, An Khánh, Thủ Đức, Hồ Chí Minh, Việt Nam',
    city: 'Hồ Chí Minh',
    contact: 'ba test',
    country: 'VN',
    countryCode: '+84',
    description: 'số 9',
    district: 'Thủ Đức',
    homeType: 'HOME',
    isoCode: 'VN',
    lat: 10.7866056,
    lng: 106.7298182,
    phoneNumber: '0777777777',
    shortAddress: 'Tòa nhà HQ Tower 09',
  },
  {
    _id: 'xe6a3f5940e2c8e59184bda7cf280bc91',
    address:
      'bTaskee, Đường D1, Khu đô thị Him Lam, Tân Hưng, Quận 7, Hồ Chí Minh, Việt Nam',
    city: 'Hồ Chí Minh',
    contact: 'ba test',
    country: 'VN',
    countryCode: '+84',
    description: 'bTaskee',
    district: 'Quận 7',
    homeType: 'HOME',
    isDefault: true,
    isoCode: 'VN',
    lat: 10.7394465,
    lng: 106.696324,
    phoneNumber: '0777777777',
    shortAddress: '69 Đường D1',
  },
];

/**
 * ChooseAddress component that displays a list of locations for the user to select
 *
 * @param navigation - Navigation prop for navigating between screens
 */
export const ChooseAddress = memo(
  ({ navigation }: RootStackScreenProps<'CleaningChooseAddress'>) => {
    const { t } = useI18n();
    const { setAddress } = usePostTaskStore();

    // Animation values for each item
    const fadeAnims = useRef<{ [key: string]: Animated.Value }>({}).current;

    const locations = LOCATION_DATA as IAddress[];
    /**
     * Handles the selection of an address and navigates to the next screen
     *
     * @param selectedAddress - The location selected by the user
     */
    const onChooseAddress = useCallback(
      async (selectedAddress: IAddress) => {
        if (!selectedAddress) return;

        setAddress(selectedAddress);
        // postTask.set('address', selectedAddress);
        navigation.navigate('CleaningChooseDuration');
      },
      [navigation, setAddress],
    );

    /**
     * Creates or retrieves an animation value for a specific item
     *
     * @param index - The index of the item
     * @returns The animation value for the item
     */
    const getAnimationValue = useCallback(
      (index: number): Animated.Value => {
        const key = index.toString();
        if (!fadeAnims[key]) {
          fadeAnims[key] = new Animated.Value(0);
          // Start the animation with a staggered delay based on index
          Animated.timing(fadeAnims[key], {
            toValue: 1,
            duration: 400,
            delay: index * 100, // Stagger the animations
            useNativeDriver: true,
          }).start();
        }
        return fadeAnims[key];
      },
      [fadeAnims],
    );

    /**
     * Renders a location item in the FlatList with animation
     */
    const renderItem: ListRenderItem<IAddress> = useCallback(
      ({ item, index }) => {
        const fadeAnim = getAnimationValue(index);

        return (
          <Animated.View
            style={{
              opacity: fadeAnim,
              transform: [
                {
                  translateY: fadeAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [20, 0], // Slide up from 20px below
                  }),
                },
              ],
            }}
          >
            <LocationItem
              testIDs={{
                item: `address${index + 1}`,
              }}
              shortAddress={item?.shortAddress || ''}
              address={item?.address || ''}
              onPress={() => onChooseAddress(item)}
              onPressUpdate={() =>
                navigation.navigate('CleaningEditLocation', { location: item })
              }
            />
          </Animated.View>
        );
      },
      [navigation, onChooseAddress, getAnimationValue],
    );

    /**
     * Handles the action to add a new address
     */
    const handleAddNewAddress = useCallback(() => {
      Alert.alert('Go to Add new address');
      // Uncomment to enable navigation to AddNewLocation screen
      // navigation.navigate('AddNewLocation');
    }, []);

    /**
     * Renders a separator between list items
     */
    const itemSeparatorComponent = useCallback(
      () => <SizedBox height={16} />,
      [],
    );

    /**
     * Key extractor for the FlatList
     */
    const keyExtractor = useCallback(
      (_, index: number) => index.toString(),
      [],
    );

    // Apply layout animation when component mounts
    useEffect(() => {
      AnimationHelpers.runLayoutAnimation('easeInEaseOut', 500);
    }, []);

    return (
      <BlockView
        inset={['bottom']}
        style={styles.container}
      >
        <BlockView style={styles.wrapFlatList}>
          <CText
            bold
            style={styles.txtDescription}
          >
            {t('LIST_OF_LOCATIONS')}
          </CText>
          <SizedBox height={8} />
          <FlatList
            data={locations}
            keyExtractor={keyExtractor}
            renderItem={renderItem}
            showsVerticalScrollIndicator={false}
            testID={'scrollChooseAddress'}
            ItemSeparatorComponent={itemSeparatorComponent}
          />
        </BlockView>
        {/* Commented out footer button
      <BlockView style={styles.footerSubmitContainer}>
        <PrimaryButton
          testID={'btnAddNewLocation'}
          title={t('ADD_ADDRESS')}
          onPress={handleAddNewAddress}
          titleProps={{
            style: {
              fontSize: FontSizes.SIZE_16,
              fontFamily: FontFamily.FONT_BOLD,
              color: Colors.WHITE
            }
          }}
        />
      </BlockView>
      */}
      </BlockView>
    );
  },
);
