/**
 * Styles for the ChooseAddress screen
 */
import { StyleSheet } from 'react-native';
import { BorderRadius, Colors, Spacing } from '@btaskee/design-system';

// Shadow color constants
const SHADOW_BUTTON_COLOR = '#6D6D6D';
const SHADOW_FOOTER_COLOR = 'rgba(109, 109, 109, 0.25)';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.BG_COLOR,
  },
  containerBtn: {
    borderTopColor: Colors.BORDER_COLOR,
    borderTopWidth: 1,
    paddingTop: Spacing.SPACE_16,
    paddingHorizontal: Spacing.SPACE_16,
    paddingBottom: Spacing.SPACE_16,
  },
  btnAdd: {
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: BorderRadius.RADIUS_08,
    backgroundColor: Colors.SECONDARY_COLOR,
    padding: Spacing.SPACE_08,
  },
  wrapFlatList: {
    backgroundColor: Colors.WHITE,
    marginTop: Spacing.SPACE_08,
    padding: Spacing.SPACE_16,
    flex: 1,
  },
  wrapItem: {
    flexDirection: 'row',
    borderColor: Colors.BORDER_COLOR,
    borderWidth: 1,
    paddingHorizontal: Spacing.SPACE_08,
    paddingVertical: Spacing.SPACE_12,
    borderRadius: BorderRadius.RADIUS_08,
    marginTop: Spacing.SPACE_12,
  },
  wrapContent: {
    marginRight: Spacing.SPACE_20,
    marginLeft: Spacing.SPACE_08,
    flex: 1,
  },
  txtTitle: {
    color: Colors.BLACK_2,
  },
  txtContent: {
    marginTop: Spacing.SPACE_04,
    color: Colors.BLACK_2,
  },
  txtBtnAdd: {
    color: Colors.WHITE,
  },
  txtDescription: {
    color: Colors.BLACK_2,
    marginVertical: Spacing.SPACE_04,
  },
  submitButtonContainer: {
    shadowColor: SHADOW_BUTTON_COLOR,
    shadowOffset: {
      width: 2,
      height: 5,
    },
    shadowOpacity: 1,
    shadowRadius: 4,
    elevation: 2,
  },
  footerSubmitContainer: {
    marginHorizontal: Spacing.SPACE_16,
    shadowColor: SHADOW_FOOTER_COLOR,
    backgroundColor: Colors.WHITE,
    shadowOpacity: 1,
    shadowRadius: 5,
    shadowOffset: {
      width: 0,
      height: 1,
    },
  },
  containerNotSupport: {
    marginTop: 0,
  },
  wrapBtnChange: {
    position: 'absolute',
    top: 15,
    right: 5,
    width: 32,
    height: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconUpdate: {
    width: Spacing.SPACE_20,
    height: Spacing.SPACE_20,
  },
});
