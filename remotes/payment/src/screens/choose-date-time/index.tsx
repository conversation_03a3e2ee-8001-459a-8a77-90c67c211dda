import React, { useEffect, useMemo, useRef, useState } from 'react';
import { ScrollView } from 'react-native';
import {
  AlertHolder,
  AnimationHelpers,
  BEFORE_HOUR_POST_TASK_CHOOSE_TASKER,
  BlockView,
  CModalHandle,
  ConditionView,
  CText,
  DateTimeHelpers,
  PostTaskHelpers,
  Spacing,
  TouchableOpacity,
  usePostTaskStore,
  useSettingsStore,
} from '@btaskee/design-system';
import { useIsFocused } from '@react-navigation/native';
import { useChangeData } from '@src/hooks/useChangeData';
import { usePostTask } from '@src/hooks/usePostTask';
import { get, isEmpty } from 'lodash-es';

import {
  ChooseTaskerBeforeHour,
  DatePicker,
  DatePickerTet,
  ModalChooseSchedule,
  NotePostTask,
  PriceButton,
  PriceIncrease,
  RepeatWeekly,
  ScheduleAskerSelected,
  SelectedTime,
  TaskerFavoriteForRebook,
  TermsAndConditions,
  TimePicker,
} from '@components';
import { useAppNavigation, useI18n } from '@hooks';

import styles from './styles';

const MIN_HOUR_TO_BOOK_FAV_TASKER = 3;

// Import component
export const ChooseDateTime = () => {
  const navigation = useAppNavigation();
  const { t } = useI18n();
  const isFocused = useIsFocused();
  const modalRef = useRef<CModalHandle>();

  const { getPrice } = usePostTask();
  const { onChangeDateTime } = useChangeData();
  const {
    address,
    date,
    schedule,
    isAutoChooseTasker,
    duration,
    setDateTime,
    setIsEnabledSchedule,
    setSchedule,
    isEnabledSchedule,
    note,
    setNote,
    setIsApplyNoteForAllTask,
    isApplyNoteForAllTask,
    price,
    dateOptions,
    forceTasker,
    service,
  } = usePostTaskStore();

  const settings = useSettingsStore().settings;

  const [isConflictTime, setIsConflictTime] = useState(false);
  const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

  const checkConflictTime = async (dateTime: any) => {
    // const params = {
    //   lat: address?.lat,
    //   lng: address?.lng,
    //   taskerId: forceTasker?._id,
    //   taskDate: dateTime,
    //   serviceId: service?._id,
    //   duration: duration,
    // };
    // const result = await checkTaskerConflictTime(params);
    // if (result.error) {
    //   setIsConflictTime(true);
    // } else {
    //   setIsConflictTime(false);
    // }
  };

  useEffect(() => {
    if (date && !isEmpty(forceTasker)) {
      checkConflictTime(date);
    }
  }, [date, forceTasker]);

  const onConfirmed = () => {
    const diff = DateTimeHelpers.diffDate({
      timezone,
      firstDate: date,
      unit: 'hour',
    });
    //Kiểm tra nếu isAutoChooseTasker = false(Asker tự chọn Tasker) và thời gian làm việc nhỏ hơn 3 tiếng thì báo lỗi
    if (!isAutoChooseTasker && diff < BEFORE_HOUR_POST_TASK_CHOOSE_TASKER) {
      return AlertHolder.alert?.open?.({
        title: t('DIALOG_TITLE_INFORMATION'),
        message: <ChooseTaskerBeforeHour />,
        actions: [{ text: t('PT2_POPUP_ERROR_TIME_CLOSE') }],
      });
    }

    // check time before 60min
    if (
      !PostTaskHelpers.validateDateTime(
        timezone,
        date,
        // settings?.settingSystem?.minPostTaskTime,
      )
    ) {
      return AlertHolder.alert?.open?.({
        title: t('DIALOG_TITLE_INFORMATION'),
        message: t('POSTTASK_STEP2_ERROR_TIME', {
          t: settings?.settingSystem?.minPostTaskTime,
        }),
        actions: [{ text: t('PT2_POPUP_ERROR_TIME_CLOSE') }],
      });
    }

    // check posting limit, ex 6AM - 10PM
    const postingLimits = service?.postingLimits;
    if (
      !PostTaskHelpers.checkTimeValidFromService(
        timezone,
        date,
        duration,
        postingLimits,
      )
    ) {
      const postingLimitsFormat = PostTaskHelpers.formatPostingLimits({
        timezone,
        postingLimits,
      });
      return AlertHolder.alert?.open?.({
        title: t('DIALOG_TITLE_INFORMATION'),
        message: t('PT2_POPUP_ERROR_TIME_INVALID_CONTENT', {
          from: postingLimitsFormat.from,
          to: postingLimitsFormat.to,
        }),
        actions: [{ text: t('PT2_POPUP_ERROR_TIME_INVALID_CLOSE') }],
      });
    }

    // Khi đặt lịch với người FAV Tasker, phải đặt trước 3h
    if (!isEmpty(forceTasker) && diff < MIN_HOUR_TO_BOOK_FAV_TASKER) {
      return AlertHolder.alert?.open?.({
        title: t('DIALOG_TITLE_INFORMATION'),
        message: t('FAV_TASKER.WARNING_BOOKING_WITH_FAV_TASKER'),
        actions: [{ text: t('CLOSE') }],
      });
    }

    if (!isEmpty(forceTasker) && dateOptions?.length < 2) {
      return AlertHolder.alert?.open?.({
        title: t('FAV_TASKER.ADD_OPTION_TIME'),
        message: t('FAV_TASKER.ADD_OPTION_TIME_CONTENT'),
        actions: [
          {
            text: t('FAV_TASKER.ADD_OPTION_TIME_2'),
            style: 'cancel',
            onPress: () => onGotoStep4(),
          },
          {
            text: t('FAV_TASKER.ADD_OPTION_TIME_1'),
            onPress: () => {
              setTimeout(() => {
                modalRef?.current?.open && modalRef?.current?.open();
              }, 500);
            },
          },
        ],
      });
    }

    // data ok
    onGotoStep4();
  };

  const onGotoStep4 = () => {
    // data ok
    // setStepPostTask(TRACKING_STEP.STEP_4);
    navigation.navigate('CleaningConfirmBooking');
  };

  const onConfirmSchedule = async (timeAskerSelected: SelectedTime[]) => {
    // data ok
    if (timeAskerSelected && timeAskerSelected.length === 1) {
      setDateTime(timeAskerSelected?.[0]?.date as string);
    }
    setIsConflictTime(false);
    getPrice();
  };

  const shouldRenderRepeatWeekly = React.useMemo(() => {
    // Do not support schedule for Tet
    // Đăng lại với Tasker yêu thích không hỗ trợ lặt lại
    if (service?.isTet || !isEmpty(forceTasker)) {
      return null;
    }
    return (
      <RepeatWeekly
        setEnabled={(enabled: boolean) => {
          // Use animation helper for smooth transition
          AnimationHelpers.runLayoutAnimation('easeInEaseOut', 400);
          setIsEnabledSchedule(enabled);
        }}
        onChange={(value: number[]) => {
          // Add subtle animation when days are selected
          AnimationHelpers.runLayoutAnimation('spring');
          setSchedule(value);
        }}
        isEnabled={isEnabledSchedule}
        weeklyRepeater={schedule}
        message={[
          t('WEEKLY_REPEATER_BODY_1'),
          t('WEEKLY_REPEATER_BODY_2'),
          t('WEEKLY_REPEATER_BODY_3'),
        ]}
      />
    );
  }, [service, isEnabledSchedule, forceTasker, schedule]);

  const shouldRenderChooseDateTime = useMemo(() => {
    return (
      <BlockView>
        {get(service, 'isTet', null) ? (
          // Tet booking need a calendar to choose easily
          <DatePickerTet
            service={service}
            date={date}
            onChangeDateTime={onChangeDateTime}
            settingSystem={settings?.settingSystem}
            timezone={timezone}
          />
        ) : (
          // Normal date picker
          <DatePicker
            value={date}
            onChange={onChangeDateTime}
            settingSystem={settings?.settingSystem}
            timezone={timezone}
          />
        )}
        <TimePicker
          value={date}
          onChange={onChangeDateTime}
          settingSystem={settings?.settingSystem}
          timezone={timezone}
        />
      </BlockView>
    );
  }, [date, service?.isTet, isFocused, timezone]);

  return (
    <BlockView
      inset={['bottom']}
      style={styles.container}
    >
      <BlockView style={styles.content}>
        <ScrollView
          contentContainerStyle={styles.containerScroll}
          showsVerticalScrollIndicator={false}
        >
          <ConditionView
            condition={!isEmpty(forceTasker)}
            viewTrue={
              <BlockView
                padding={{
                  horizontal: Spacing.SPACE_16,
                  top: Spacing.SPACE_16,
                }}
              >
                <TaskerFavoriteForRebook forceTasker={forceTasker} />
              </BlockView>
            }
          />
          <ConditionView
            condition={!isEmpty(dateOptions)}
            viewTrue={
              <ScheduleAskerSelected
                timezone={timezone}
                scheduleSelected={dateOptions}
                onChangeSchedule={() =>
                  modalRef?.current?.open &&
                  modalRef?.current?.open(isConflictTime)
                }
              />
            }
            viewFalse={shouldRenderChooseDateTime}
          />
          <PriceIncrease
            price={price}
            address={address}
            service={service}
            increaseReasons={get(price, 'increaseReasons', false)}
            isShow={Boolean(get(price, 'isIncrease', false))}
          />
          <ConditionView
            condition={isConflictTime}
            viewTrue={
              <BlockView style={styles.wrapConflictTime}>
                <CText>{t('FAV_TASKER.CONFLICT_TIME_NOTE')}</CText>
              </BlockView>
            }
            viewFalse={shouldRenderRepeatWeekly}
          />

          <NotePostTask
            setNote={setNote}
            value={note}
            service={service}
            isApplyNoteForAllTask={isApplyNoteForAllTask}
            setNoteForAllTask={setIsApplyNoteForAllTask}
            containerStyle={{ marginTop: 24 }}
          />
          <TermsAndConditions style={{ marginTop: 0 }} />
        </ScrollView>
      </BlockView>

      {/* Khi trùng thời gian làm việc, sẽ hiển thị nút xem lịch để Asker chọn lịch khác */}
      <ConditionView
        condition={isConflictTime}
        viewTrue={
          <BlockView style={styles.wrapBtnSeeSchedule}>
            <TouchableOpacity
              style={styles.btnSeeSchedule}
              onPress={() =>
                modalRef?.current?.open &&
                modalRef?.current?.open(isConflictTime)
              }
            >
              <CText
                color="white"
                bold
              >
                {t('FAV_TASKER.SEE_TASKER_SCHEDULE')}
              </CText>
            </TouchableOpacity>
          </BlockView>
        }
        viewFalse={
          <PriceButton
            testID="btnNextStep3"
            onPress={onConfirmed}
            fromScreen={service?.name}
            pricePostTask={price}
          />
        }
      />
      <ConditionView
        condition={!isEmpty(forceTasker)}
        viewTrue={
          <ModalChooseSchedule
            ref={modalRef}
            dateDefault={date}
            timezone={timezone}
            onConfirmSchedule={onConfirmSchedule}
          />
        }
      />
    </BlockView>
  );
};
