/**
 * ConfirmBooking Screen Component
 *
 * This screen allows users to review and confirm their cleaning service booking details,
 * including location, task details, payment method, and pricing information.
 * It handles regular and special (Tet holiday) booking flows with appropriate date selection options.
 */
import React, { useCallback, useMemo, useRef, useState } from 'react';
import { ScrollView } from 'react-native';
import {
  AlertHolder,
  BlockView,
  CModal,
  CModalHandle,
  Colors,
  ConditionView,
  CText,
  DateTimeHelpers,
  DeviceHelper,
  ITimezone,
  TouchableOpacity,
  usePostTaskStore,
  useSettingsStore,
} from '@btaskee/design-system';
import { usePostTask } from '@src/hooks/usePostTask';
import { debounce } from 'lodash-es';

import {
  BookingButton,
  DatePicker,
  LocationPostTask,
  OptionBookingWithFavTasker,
  PaymentDetailStep4WithDateOptions,
  PaymentMethod,
  TaskDetail,
  TermsOfUseServices,
  TimePicker,
} from '@components';
import { useI18n } from '@hooks';

import styles from './styles';

const RATIO_PADDING_BOTTOM = 0.05;

/**
 * Props for the ConfirmBooking component
 */
interface ConfirmBookingProps {
  navigation: {
    navigate: (screen: string, params?: any) => void;
    popToTop: () => void;
  };
}

/**
 * ConfirmBooking component for reviewing and confirming cleaning service bookings
 * @param props - Component props
 */
export const ConfirmBooking: React.FC<ConfirmBookingProps> = ({
  navigation,
}) => {
  const { t } = useI18n();
  const {
    address,
    price,
    setDateTime,
    setAddress,
    date,
    service,
    timezone: timezoneStore,
  } = usePostTaskStore();
  const { getPrice, postTask } = usePostTask();
  const settings = useSettingsStore().settings;

  const timezone = useMemo(() => {
    if (address?.city) {
      return DateTimeHelpers.getTimezoneByCity(address.city);
    }
    return timezoneStore;
  }, [address?.city, timezoneStore]);

  const paymentMethod = useMemo(
    () => ({
      name: 'MoMo',
      label: 'PAYMENT_METHOD_MOMO',
      value: 'MOMO',
      icon: require('@assets/images/icons/payments/logo-momo.png'),
    }),
    [],
  );

  const [isTet, setIsTet] = useState(service?.isTet);
  const [selectedDate, setSelectedDate] = useState(date);
  const modalRef = useRef<CModalHandle>(null);
  const refModalLogin = useRef<any>(null);

  /**
   * Removes Tet holiday flag from service and updates state
   */
  const removeTetInService = useCallback(() => {
    if (service) {
      // Clear isTet in service
      const updatedService = { ...service };
      delete updatedService.isTet;
      // setService(service);
      setIsTet(null);
      // setPaymentMethodWhenBooking();
    }
  }, [service]);

  /**
   * Handles switching from Tet holiday booking to regular booking
   */
  const _changeToRegularBooking = useCallback(() => {
    // trackingServiceClick({
    //   campaignName: configSpecialPreBooking?.name,
    //   screenName: TrackingScreenNames.ConfirmPayment,
    //   serviceName: SERVICES.CLEANING,
    //   action: TRACKING_ACTION.SwitchToRegularBooking,
    // });

    if (!timezone) return;

    // Require change date if the chosen date is over regular range
    const maxDate = DateTimeHelpers.toDayTz({ timezone })
      .add(6, 'day')
      .endOf('date');
    const isAfter = date
      ? DateTimeHelpers.checkIsAfter({
        timezone,
        firstDate: date,
        secondDate: maxDate,
      })
      : false;

    if (isAfter) {
      // Set default date is 2PM tomorrow
      const tomorrow = DateTimeHelpers.toDayTz({ timezone })
        .add(1, 'day')
        .hour(14)
        .startOf('hour');
      setSelectedDate(tomorrow.toDate());

      // Show change new date modal
      modalRef?.current?.open && modalRef.current.open();
    } else {
      removeTetInService();
    }
  }, [timezone, date, removeTetInService]);

  /**
   * Updates date time when user changes it
   * @param dateObj - The new date object selected by user
   */
  const onChangeDateTime = useCallback(
    (dateObj: Date) => {
      // check spam, call api with same data
      if (!timezone || !date || !dateObj) return null;

      const isSame = DateTimeHelpers.checkIsSame({
        timezone,
        firstDate: date,
        secondDate: dateObj,
      });

      if (isSame) return null;

      setSelectedDate(dateObj);
    },
    [timezone, date],
  );

  /**
   * Applies the new date selection and recalculates price
   */
  const _changeNewDate = useCallback(async () => {
    // trackingServiceClick({
    //   campaignName: configSpecialPreBooking?.name,
    //   screenName: TrackingScreenNames.UpdateDataTime,
    //   serviceName: SERVICES.CLEANING,
    //   action: TRACKING_ACTION.Update,
    //   additionInfo: {
    //     workingTime: {
    //       date: DateTimeHelpers.formatToString({ date, timezone, typeFormat: TypeFormatDate.DateShort }),
    //       time: DateTimeHelpers.formatToString({ date, timezone, typeFormat: TypeFormatDate.TimeHourMinute }),
    //     },
    //   },
    // });

    if (!service || !selectedDate) return;

    await setDateTime(selectedDate, service);
    // recalculate duration and estimated time, only Home Cooking service
    getPrice();
    removeTetInService();
  }, [selectedDate, service, setDateTime, getPrice, removeTetInService]);

  /**
   * Removes promotion and recalculates price
   */
  const removePromotion = useCallback(async () => {
    // await props.setPromotion(null);
    if (service?.name) {
      getPrice(service.name);
    }
  }, [getPrice, service?.name]);

  /**
   * Opens the login modal
   */
  const _openModalLogin = useCallback(() => {
    refModalLogin?.current?.open && refModalLogin.current.open();
  }, []);

  /**
   * Resets state after successful post task
   */
  const _resetStateAfterPTSuccess = useCallback(() => {
    // resetAllState();
    // resetStateStep2();
    // resetStateStep4();
  }, []);

  /**
   * Callback after user login
   * @param userData - User data from login
   */
  const _callback = useCallback(
    async (userData: {
      name?: string;
      phone?: string;
      countryCode?: string;
    }) => {
      // Set info user address
      if (address) {
        await setAddress({
          ...address,
          contact: userData?.name || address.contact,
          phoneNumber: userData?.phone || address.phoneNumber,
          countryCode: userData?.countryCode || address.countryCode,
        });
      }

      // post task
      await _onPosTask();

      // Reset state after post task
      // await _resetStateAfterPTSuccess();

      // get data new task in activity tab
      // await props.getDataUpcoming();
      // return null;
    },
    [address, setAddress, _onPosTask],
  );

  /**
   * Handles outstanding payment checks
   */
  const getOutstandingPayment = useCallback(async () => {
    // Check exist userId
    // if (!global.userId) {
    //   return;
    // }
    // // Fetch the Outstanding payment debt
    // const outstandingDebt = await getOutstandingPaymentDebt();
    // if (outstandingDebt?.isSuccess && outstandingDebt?.data?.length > 0) {
    //   // If user has any outstanding payment, redirect user to out standing payment debt page
    //   // return navigation.navigate(RouteName.OutstandingPayment, { outstanding: outstandingDebt.data });
    // }
    // Do nothing
  }, []);

  /**
   * Callback after successful task posting
   */
  const postTaskCallBack = useCallback(() => {
    if (navigation) {
      navigation.popToTop();
      navigation.navigate('CleaningPostTaskSuccess');
    }
  }, [navigation]);

  /**
   * Handles the booking process
   */
  const onBooking = useCallback(async () => {
    const response = await postTask(postTaskCallBack);
    if (response && response?.code === 'OUTSTANDING_PAYMENT_STATUS_NEW') {
      AlertHolder.alert?.open?.({
        title: t('DIALOG_TITLE_INFORMATION'),
        message: t('OUTSTANDING_PAYMENT_STATUS_NEW'),
        actions: [
          {
            text: t('PAYMENT_TOP_UP'),
            onPress: () => {
              getOutstandingPayment();
            },
          },
        ],
      });
    }
  }, [postTask, postTaskCallBack, t, getOutstandingPayment]);

  /**
   * Handles modal close event for regular booking change
   */
  const onCloseModalChangeToRegularBooking = useCallback(() => {
    // trackingServiceClick({
    //   campaignName: configSpecialPreBooking?.name,
    //   screenName: TrackingScreenNames.UpdateDataTime,
    //   serviceName: SERVICES.CLEANING,
    //   action: TRACKING_ACTION.Close,
    // });
  }, []);

  /**
   * Shows options for booking with favorite tasker
   */
  const showOptionBookingWithFavTasker = useMemo(
    () =>
      debounce(() => {
        AlertHolder.alert?.open?.({
          title: 'FAV_TASKER.ADD_OPTION',
          message: <OptionBookingWithFavTasker />,
          actions: [
            {
              text: 'CONTINUE',
              style: 'cancel',
              onPress: () => {
                // trackingButtonClick({
                //   screenName: TrackingScreenNames.AddOption,
                //   featureName: 'BookWithFavTasker',
                //   action: TrackingScreenNames.Next,
                //   additionalInfo: {
                //     ResendForAnotherTasker: Boolean(forceTasker?.isResent),
                //   },
                // });
                onBooking();
              },
            },
          ],
          containerStyle: {
            backgroundColor: Colors.BG_COLOR2,
          },
        });
      }, 400),
    [onBooking],
  );

  /**
   * Initiates the task posting process
   */
  const _onPosTask = useCallback(async () => {
    // trackingServiceClick({
    //   campaignName: service?.isTet ? configSpecialPreBooking?.name : null,
    //   screenName: TrackingScreenNames.ConfirmPayment,
    //   serviceName: props?.service?.name,
    //   action: TRACKING_ACTION.Next,
    //   isTetBooking: service?.isTet,
    //   additionalInfo: {
    //     phoneNumber: address?.phoneNumber,
    //     contactName: address?.contact,
    //     paymentMethod: {
    //       method: paymentMethod?.value,
    //       promotion: promotion?.code,
    //     },
    //   },
    // });
    // if (!isEmpty(forceTasker)) {
    // trackingScreenView({
    //   screenName: 'AddOption',
    //   featureName: 'BookWithFavTasker',
    //   entryPoint: TrackingScreenNames.ConfirmPayment,
    //   taskerID: forceTasker?._id,
    // });

    //   AlertHolder.alert.close();
    //   return showOptionBookingWithFavTasker();
    // }
    return onBooking();
  }, [onBooking, showOptionBookingWithFavTasker]);

  // Determine if date comparison is possible for the modal action button
  const isDateComparisonPossible = useMemo(
    () => Boolean(timezone && date && selectedDate),
    [timezone, date, selectedDate],
  );

  // Check if dates are the same for disabling the modal action button
  const areDatesTheSame = useMemo(() => {
    if (!isDateComparisonPossible) return false;

    return DateTimeHelpers.checkIsSame({
      timezone: timezone as ITimezone,
      firstDate: date as Date,
      secondDate: selectedDate as Date,
    });
  }, [isDateComparisonPossible, timezone, date, selectedDate]);

  return (
    <BlockView
      inset={['bottom']}
      style={styles.container}
    >
      <ScrollView
        scrollIndicatorInsets={{ right: 1 }}
        testID="scrollViewStep4"
        contentContainerStyle={styles.content}
      >
        <LocationPostTask />

        <TaskDetail />

        <PaymentDetailStep4WithDateOptions
          dateOptions={price?.dateOptions}
          timezone={timezone}
          paymentMethod={paymentMethod}
        />

        <PaymentMethod
          isTet={isTet}
          removePromotion={removePromotion}
        />
        <ConditionView
          condition={isTet}
          viewTrue={
            <TouchableOpacity
              style={styles.wrapChangeToRegularBooking}
              onPress={_changeToRegularBooking}
            >
              <CText
                center
                bold
                style={styles.txtChangeToRegularBooking}
              >
                {t('TET_BOOKING_TO_NORMAL_TASK')}
              </CText>
              <CText
                center
                style={styles.txtChangeToRegularBookingDescription}
              >
                {t('TET_BOOKING_TO_NORMAL_TASK_DESCRIPTION')}
              </CText>
            </TouchableOpacity>
          }
        />
        <TermsOfUseServices />
      </ScrollView>
      <BookingButton
        testID="btnSubmitPostTask"
        onPostTask={_onPosTask}
        price={price}
        navigation={navigation}
      />
      {/* <ModalLogin
        ref={refModalLogin}
        callback={_callback} // call when login success
        navigation={navigation}
        routeName={{ routeName: 'Tab_Activity', screen: 'Tab_Upcoming' }} // After sign in success will navigate to this route
      /> */}
      {isTet ? ( // Change Tet booking to regular booking
        <CModal
          contentContainerStyle={{
            paddingBottom: DeviceHelper.WINDOW.HEIGHT * RATIO_PADDING_BOTTOM,
          }}
          hideButtonClose
          ref={modalRef}
          title={t('TET_BOOKING_TO_NOMAL_NOTE_TITLE')}
          closeModalAction={onCloseModalChangeToRegularBooking}
          actions={[
            {
              text: t('TET_BOOKING_TO_NOMAL_NOTE_DONE'),
              onPress: _changeNewDate,
              disabled: areDatesTheSame,
            },
          ]}
        >
          <CText>{t('TET_BOOKING_TO_NORMAL_TASK_CHANGE_DATE')}</CText>
          <BlockView>
            <DatePicker
              title={t('STEP_4_UPDATE_CALENDAR_TITLE')}
              value={selectedDate}
              onChange={onChangeDateTime}
              settingSystem={settings?.settingSystem}
              timezone={timezone}
            />
            <TimePicker
              title={t('STEP_4_UPDATE_TIME_TITLE')}
              value={selectedDate}
              onChange={onChangeDateTime}
              settingSystem={settings?.settingSystem}
              timezone={timezone}
            />
          </BlockView>
        </CModal>
      ) : null}
    </BlockView>
  );
};
