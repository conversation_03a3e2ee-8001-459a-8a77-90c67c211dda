import { AlertHolder } from '@btaskee/design-system';
import i18n from '@src/i18n';
import { find, forEach, isEmpty } from 'lodash-es';

// const cityDictionary = {
// 	'<PERSON><PERSON>': {
//     key: /<PERSON><PERSON>|<PERSON>|HoChiMinh|HCM|HCMC/gi,
//     district: {
// 			'Quận 1': /(Quận\s|Quận|Quan\s|Quan|District\s|District)[1](\D|$)/gi,
//       'Quận 2': /(Quận\s|Quận|Quan\s|Quan|District\s|District)[2](\D|$)/gi,
// 			'Quận 3': /(Quận\s|Quận|Quan\s|Quan|District\s|District)[3](\D|$)/gi,
// 			'Quận 4': /(Quận\s|Quận|Quan\s|Quan|District\s|District)[4](\D|$)/gi,
// 			'Quận 5': /(Quận\s|Quận|Quan\s|Quan|District\s|District)[5](\D|$)/gi,
// 			'Quận 6': /(Quận\s|Quận|Quan\s|Quan|District\s|District)[6](\D|$)/gi,
// 			'Quận 7': /(Quận\s|Quận|Quan\s|Quan|District\s|District)[7](\D|$)/gi,
// 			'Quận 8': /(Quận\s|Quận|Quan\s|Quan|District\s|District)[8](\D|$)/gi,
// 			'Quận 9': /(Quận\s|Quận|Quan\s|Quan|District\s|District)[9](\D|$)/gi,
//       'Quận 10': /(Quận\s|Quận|Quan\s|Quan|District\s|District)[1][0](\D|$)/gi,
//       'Quận 11': /(Quận\s|Quận|Quan\s|Quan|District\s|District)[1][1](\D|$)/gi,
//       'Quận 12': /(Quận\s|Quận|Quan\s|Quan|District\s|District)[1][2](\D|$)/gi,
//       'Bình Thạnh': /Bình Thạnh|BinhThanh|Binh Thanh/gi,
//       'Bình Tân': /Bình Tân|BinhTan|Binh Tan/gi,
//       'Bình Chánh': /Bình Chánh|BinhChanh|Binh Chanh/gi,
//       'Tân Bình': /Tân Bình|TanBinh|Tan Binh/gi,
//       'Tân Phú': /Tân Phú|TanPhu|Tan Phu/gi,
//       'Nhà Bè': /Nhà Bè|NhaBe|Nha Be/gi,
//       'Củ Chi': /Củ Chi|CuChi|Cu Chi/gi,
//       'Hóc Môn': /Hóc Môn|HocMon|Hoc Mon/gi,
//       'Thủ Đức': /Thủ Đức|ThuDuc|ThuDuc/gi,
//       'Gò Vấp': /Gò Vấp|GoVap|Go Vap/gi,
//       'Cần Giờ': /Cần Giờ|CanGio|Can Gio/gi,
//       'Phú Nhuận': /Phú Nhuận|PhuNhuan|Phu Nhuan/gi,
//     },
//   },
//   'Hà Nội': {
//     key: /Hà Nội|Ha Noi|HaNoi/gi,
//     district: {
//       'Hoàn Kiếm': /Hoàn Kiếm|HoanKiem|Hoan Kiem/gi,
//       'Ba Đình': /Ba Đình|BaDinh|Ba Dinh/gi,
//       'Cầu Giấy': /Cầu Giấy|CauGiay|Cau Giay/gi,
//       'Đống Đa': /Đống Đa|DongDa|Dong Da/gi,
//       'Hà Đông': /Hà Đông|HaDong|Ha Dong/gi,
//       'Hoàng Mai': /Hoàng Mai|HoangMai|Hoang Mai/gi,
//       'Long Biên': /Long Biên|LongBien|Long Bien/gi,
//       'Nam Từ Liêm': /Nam Từ Liêm|NamTuLiem|Nam Tu Liem/gi,
//       'Bắc Từ Liêm': /Bắc Từ Liêm|BacTuLiem|Bac Tu Liem/gi,
// 			'Từ Liêm': /(,|,\s)(Từ Liêm|Tu Liem|TuLiem)/gi,
//       'Tây Hồ': /Tây Hồ|TayHo|Tay Ho/gi,
//       'Thanh Xuân': /Thanh Xuân|ThanhXuan|Thanh Xuan/gi,
//       'Thanh Trì': /Thanh Trì|ThanhTri|Thanh Tri/gi,
//       'Gia Lâm': /Gia Lâm|GiaLam|Gia Lam/gi,
//       'Đông Anh': /Đông Anh|DongAnh|Dong Anh/gi,
//       'Sóc Sơn': /Sóc Sơn|SocSon|Soc Son/gi,
//       'Sơn Tây': /Sơn Tây|SonTay|Son Tay/gi,
//       'Ba Vì': /Ba Vì|BaVi|Ba Vi/gi,
//       'Phúc Thọ': /Phúc Thọ|PhucTho|Phuc Tho/gi,
//       'Thạch Thất': /Thạch Thất|ThachThat|Thach That/gi,
//       'Quốc Oai': /Quốc Oai|QuocOai|Quoc Oai/gi,
//       'Chương Mỹ': /Chương Mỹ|ChuongMy|Chuong My/gi,
//       'Đan Phượng': /Đan Phượng|DanPhuong|Dan Phuong/gi,
//       'Hoài Đức': /Hoài Đức|HoaiDuc|Hoai Duc/gi,
//       'Thanh Oai': /Thanh Oai|ThanhOai|Thanh Oai/gi,
//       'Mỹ Đức': /Mỹ Đức|MyDuc|My Duc/gi,
//       'Ứng Hoà': /Ứng Hoà|UngHoa|Ung Hoa/gi,
//       'Thường Tín': /Thường Tín|ThuongTin|Thuong Tin/gi,
//       'Phú Xuyên': /Phú Xuyên|PhuXuyen|Phu Xuyen/gi,
//       'Mê Linh': /Mê Linh|MeLinh|Me Linh/gi,
//       'Hai Bà Trưng': /Hai Bà Trưng|HaiBaTrung|Hai Ba Trung/gi,
//     }
//   },
//   'Đà Nẵng': {
//     key: /Đà Nẵng|Da Nang|DaNang/gi,
//     district: {
//       'Hải Châu': /Hải Châu|HaiChau|Hai Chau/gi,
//       'Thanh Khê': /Thanh Khê|ThanhKhe|Thanh Khe/gi,
//       'Sơn Trà': /Sơn Trà|SonTra|Son Tra/gi,
//       'Ngũ Hành Sơn': /Ngũ Hành Sơn|NguHanhSon|Ngu Hanh Son/gi,
//       'Liên Chiểu': /Liên Chiểu|LienChieu|Lien Chieu/gi,
//       'Hòa Vang': /Hòa Vang|HoaVang|Hoa Vang/gi,
//       'Cẩm Lệ': /Cẩm Lệ|CamLe|Cam Le/gi,
//       'Hoàng Sa': /Hoàng Sa|HoangSa|Hoang Sa/gi,
//     }
//   },
//   'Bình Dương': {
//     key: /Bình Dương|Binh Duong|BinhDuong/gi,
//     district: {
//       'Thủ Dầu Một': /Thủ Dầu Một|ThuDauMot|Thu Dau Mot/gi,
//       'Bến Cát': /Bến Cát|BenCat|Ben Cat/gi,
//       'Dĩ An': /Dĩ An|DiAn|Di An/gi,
//       'Tân Uyên': /Tân Uyên|TanUyen|Tan Uyen/gi,
//       'Thuận An': /Thuận An|ThuanAn|Thuan An/gi,
//       'Dầu Tiếng': /Dầu Tiếng|DauTieng|Dau Tieng/gi,
//       'Phú Giáo': /Phú Giáo|PhuGiao|Phu Giao/gi,
//       'Bàu Bàng': /Bàu Bàng|BauBang|Bau Bang/gi,
//     },
//   },
//   'Đồng Nai': {
//     key: /Đồng Nai|Dong Nai|DongNai/gi,
//     district: {
//       'Biên Hòa': /Biên Hòa|BienHoa|Bien Hoa/gi,
//       'Cẩm Mỹ': /Cẩm Mỹ|CamMy|Cam My/gi,
//       'Định Quán': /Định Quán|DinhQuan|Dinh Quan/gi,
//       'Long Khánh': /Long Khánh|LongKhanh|Long Khanh/gi,
//       'Long Thành': /Long Thành|LongThanh|Long Thanh/gi,
//       'Nhơn Trạch': /Nhơn Trạch|NhonTrach|Nhon Trach/gi,
//       'Tân Phú': /Tân Phú|TanPhu|Tan Phu/gi,
//       'Thống Nhất': /Thống Nhất|ThongNhat|Thong Nhat/gi,
//       'Trảng Bom': /Trảng Bom|TrangBom|Trang Bom/gi,
//       'Vĩnh Cửu': /Vĩnh Cửu|VinhCuu|Vinh Cuu/gi,
//       'Xuân Lộc': /Xuân Lộc|XuanLoc|Xuan Loc/gi,
//     }
//   },
//   'Bangkok': {
//     key: /Bangkok|Krung Thep Maha Nakhon|Krung Thep|กรุงเทพมหานคร/gi,
//     district: {
//       'Phra Nakhon': /Phra Nakhon|เขตพระนคร/gi,
//       'Dusit': /Dusit|เขตดุสิต/gi,
//       'Nong Chok': /Nong Chok|เขต หนองจอก/gi,
//       'Bang Rak': /Bang Rak|เขตบางรัก/gi,
//       'Bang Khen': /Bang Khen|เขตบางเขน/gi,
//       'Bang Kapi': /Bang Kapi|เขตบางกะปิ/gi,
//       'Pathum Wan': /Pathum Wan|เขตปทุมวัน/gi,
//       'Pom Prap Sattru Phai': /Pom Prap Sattru Phai|เขตป้อมปราบศัตรูพ่าย/gi,
//       'Phra Khanong': /Phra Khanong|เขตพระโขนง/gi,
//       'Min Buri': /Min Buri|เขตมีนบุรี/gi,
//       'Lat Krabang': /Lat Krabang|เขตลาดกระบัง/gi,
//       'Yan Nawa': /Yan Nawa|เขต ยานนาวา/gi,
//       'Samphanthawong': /Samphanthawong|เขตสัมพันธวงศ์/gi,
//       'Phaya Thai': /Phaya Thai|เขตพญาไท/gi,
//       'Thon Buri': /Thon Buri|เขตธนบุรี/gi,
//       'Bangkok Yai': /Bangkok Yai|เขตบางกอกใหญ่/gi,
//       'Huai Khwang': /Huai Khwang|เขตห้วยขวาง/gi,
//       'Khlong San': /Khlong San|เขตคลองสาน/gi,
//       'Taling Chan': /Taling Chan|เขตตลิ่งชัน/gi,
//       'Bangkok Noi': /Bangkok Noi|เขตบางกอกน้อย/gi,
//       'Bang Khun Thian': /Bang Khun Thian|เขตบางขุนเทียน/gi,
//       'Phasi Charoen': /Phasi Charoen|เขตภาษีเจริญ/gi,
//       'Nong Khaem': /Nong Khaem|เขตหนองแขม/gi,
//       'Rat Burana': /Rat Burana|เขตราษฎร์บูรณะ/gi,
//       'Bang Phlat': /Bang Phlat|เขตบางพลัด/gi,
//       'Din Daeng': /Din Daeng|เขตดินแดง/gi,
//       'Bueng Kum': /Bueng Kum|เขต บึงกุ่ม/gi,
//       'Sathon': /Sathon|เขต สาทร/gi,
//       'Bang Sue': /Bang Sue|เขตบางซื่อ/gi,
//       'Chatuchak': /Chatuchak|เขตจตุจักร/gi,
//       'Bang Kho Laem': /Bang Kho Laem|เขตบางคอแหลม/gi,
//       'Prawet': /Prawet|เขต ประเวศ/gi,
//       'Khlong Toei': /Khlong Toei|เขตคลองเตย/gi,
//       'Suan Luang': /Suan Luang|แขวงสวนหลวง/gi,
//       'Chom Thong': /Chom Thong|เขตจอมทอง/gi,
//       'Don Mueang': /Don Mueang|เขตดอนเมือง/gi,
//       'Ratchathewi': /Ratchathewi|เขตราชเทวี/gi,
//       'Lat Phrao': /Lat Phrao|เขตลาดพร้าว/gi,
//       'Watthana': /Watthana|เขตวัฒนา/gi,
//       'Bang Khae': /Bang Khae|เขตบางแค/gi,
//       'Lak Si': /Lak Si|เขต หลักสี่/gi,
//       'Sai Mai': /Sai Mai|เขตสายไหม/gi,
//       'Khan Na Yao': /Khan Na Yao|เขตคันนายาว/gi,
//       'Saphan Sung': /Saphan Sung|เขตสะพานสูง/gi,
//       'Wang Thonglang': /Wang Thonglang|เขตวังทองหลาง/gi,
//       'Khlong Sam Wa': /Khlong Sam Wa|เขตคลองสามวา/gi,
//       'Bang Na': /Bang Na|เขตบางนา/gi,
//       'Thawi Watthana': /Thawi Watthana|เขตทวีวัฒนา/gi,
//       'Thung Khru': /Thung Khru|เขตทุ่งครุ/gi,
//       'Bang Bon': /Bang Bon|เขตบางบอน/gi
//     }
//   },
//   'Cần Thơ': {
//     key: /Cần Thơ|Can Tho|CanTho/gi,
//     district: {
//       'Cờ Đỏ': /Cờ Đỏ|CoDo|Co Do/gi,
//       'Phong Điền': /Phong Điền|PhongDien|Phong Dien/gi,
//       'Ô Môn': /Ô Môn|OMon|O Mon/gi,
//       'Cái Răng': /Cái Răng|CaiRang|Cai Rang/gi,
//       'Bình Thủy': /Bình Thủy|BinhThuy|Binh Thuy/gi,
//       'Ninh Kiều': /Ninh Kiều|NinhKieu|Ninh Kieu/gi,
//       'Thốt Nốt': /Thốt Nốt|ThotNot|Thot Not/gi,
//       'Vĩnh Thạnh': /Vĩnh Thạnh|VinhThanh|Vinh Thanh/gi,
//       'Thới Lai': /Thới Lai|ThoiLai|Thoi Lai/gi,
//     }
//   },
//   'Hải Phòng': {
//     key: /Hải Phòng|HaiPhong|Hai Phong/gi,
//     district: {
//       'Hồng Bàng': /Hồng Bàng|HongBang|Hong Bang/gi,
//       'Dương Kinh': /Dương Kinh|DuongKinh|Duong Kinh/gi,
//       'Đồ Sơn': /Đồ Sơn|DoSon|Do Son/gi,
//       'Bạch Long Vĩ': /Bạch Long Vĩ|BachLongVi|Bach Long Vi/gi,
//       'Cát Hải': /Cát Hải|CatHai|Cat Hai/gi,
//       'Vĩnh Bảo': /Vĩnh Bảo|VinhBao|Vinh Bao/gi,
//       'Tiên Lãng': /Tiên Lãng|TienLang|Tien Lang/gi,
//       'Kiến Thụy': /Kiến Thụy|KienThuy|Kien Thuy/gi,
//       'An Lão': /An Lão|An Lao|An Lao/gi,
//       'Hải An': /Hải An|HaiAn|Hai An/gi,
//       'Thủy Nguyên': /Thủy Nguyên|ThuyNguyen|Thuy Nguyen/gi,
//       'Kiến An': /Kiến An|KienAn|Kien An/gi,
//       'Lê Chân': /Lê Chân|LeChan|Le Chan/gi,
//       'Ngô Quyền': /Ngô Quyền|NgoQuyen|Ngo Quyen/gi,
//       'An Dương': /An Dương|AnDuong|An Duong/gi,
//     }
//   },
//   'Lâm Đồng': {
//     key: /Lâm Đồng|Lam Dong|Lam Dong/gi,
//     district: {
//       'Đức Trọng': /Đức Trọng|DucTrong|Duc Trong/gi,
//       'Đơn Dương': /Đơn Dương|DonDuong|Don Duong/gi,
//       'Bảo Lộc': /Bảo Lộc|BaoLoc|Bao Loc/gi,
//       'Đạ Tẻh': /Đạ Tẻh|DaTeh|Da Teh/gi,
//       'Bảo Lâm': /Bảo Lâm|BaoLam|Bao Lam/gi,
//       'Đạ Huoai': /Đạ Huoai|DaHuoai|Da Huoai/gi,
//       'Lạc Dương': /Lạc Dương|LacDuong|Lac Duong/gi,
//       'Cát Tiên': /Cát Tiên|CatTien|Cat Tien/gi,
//       'Lâm Hà': /Lâm Hà|LamHa|Lam Ha/gi,
//       'Di Linh': /Di Linh|DiLinh/gi,
//       'Đà Lạt': /Đà Lạt|DaLat|Da Lat/gi,
//       'Đam Rông': /Đam Rông|DamRong|Dam Rong/gi,
//     }
//   },
//   'Khánh Hòa': {
//     key: /Khánh Hòa|KhanhHoa|Khanh Hoa/gi,
//     district: {
//       'Cam Ranh': /Cam Ranh|CamRanh/gi,
//       'Trường Sa': /Trường Sa|TruongSa|Truong Sa/gi,
//       'Ninh Hòa': /Ninh Hòa|NinhHoa|Ninh Hoa/gi,
//       'Khánh Sơn': /Khánh Sơn|KhanhSon|Khanh Son/gi,
//       'Khánh Vĩnh': /Khánh Vĩnh|KhanhVinh|Khanh Vinh/gi,
//       'Diên Khánh': /Diên Khánh|DienKhanh|Dien Khanh/gi,
//       'Vạn Ninh': /Vạn Ninh|VanNinh|Van Ninh/gi,
//       'Cam Lâm': /Cam Lâm|CamLam|Cam Lam/gi,
//       'Nha Trang': /Nha Trang|NhaTrang/gi,
//     }
//   }
// }

/**
 * @description get data cites from global
 */
const getCityDictionary = () => {
  return global.citesReg || [];
};

/**
 * @description mapping city name
 * @param cityName city name need mapping
 * @returns
 * Match: new city name after mapping
 * Not Match: old city name
 * @example
 * MATCH
 * cityName: 'Ho Chi Minh'
 *  return 'Hồ Chí Minh'
 * NOT MATCH
 * cityName: 'Hồ Chí Minh city'
 *  return 'Hồ Chí Minh city'
 */
export const findTaskCity = (cityName) => {
  let newCityName = cityName;
  const cityDictionary = getCityDictionary();
  forEach(cityDictionary, (city) => {
    const isMatch = new RegExp(city.key, 'gi').test(cityName);
    if (isMatch) {
      newCityName = city.name;
      return false;
    }
  });
  return newCityName;
};

/*
Params:
  districtName,
  cityName,
  returnDistrictName:? Boolean -> return districtName or null if district not exist
*/

export const findTaskDistrict = (
  districtName,
  cityName,
  returnDistrictName = true,
) => {
  const cityDictionary = getCityDictionary();
  let newDistrictName = '';
  //find city
  const newCityName = findTaskCity(cityName);
  //get districts
  const objCity = cityDictionary.find((ci) => ci.name === newCityName);
  if (objCity) {
    forEach(objCity.district, (district) => {
      const isMatch = new RegExp(district.key, 'gi').test(districtName);
      if (isMatch) {
        newDistrictName = district.name;
        return false;
      }
    });
  }
  // found district name
  if (newDistrictName) {
    return newDistrictName;
  }
  // return old district name or not
  return returnDistrictName ? districtName : null;
};

export const findTaskSubDistrict = (
  subDistrictName,
  districtName,
  cityName,
  returnDistrictName = true,
) => {
  let newSubDistrictName = returnDistrictName ? subDistrictName : null;
  const cityDictionary = getCityDictionary();

  //find city name
  const newCityName = findTaskCity(cityName);

  // find district name
  const newDistrictName = findTaskDistrict(districtName, cityName);

  const objCity = cityDictionary.find((ci) => ci.name === newCityName);

  if (objCity) {
    forEach(objCity.district, (district) => {
      if (district?.name === newDistrictName) {
        forEach(district.subDistrict, (subDistrict) => {
          const isMatch = new RegExp(subDistrict.key, 'gi').test(
            subDistrictName,
          );
          if (isMatch) {
            newSubDistrictName = subDistrict.name;
            return;
          }
        });
        return;
      }
    });
  }

  return newSubDistrictName;
};

/**
 * @description get list districts by city name
 * @param cityName city name
 * @returns array of districts
 */

export const getDistrictFromCityName = (cityName) => {
  let districts = [];
  const cityDictionary = getCityDictionary();
  const newCityName = findTaskCity(cityName);
  const objCity = cityDictionary.find((ci) => ci.name === newCityName);
  if (objCity) {
    districts = objCity.district;
  }

  return districts;
};

// serviceCity: array city supprt, nameCity: name city
export const checkSupportCity = (
  serviceCity,
  nameCity = 'Hồ Chí Minh',
  unlimited = false,
) => {
  if (unlimited) {
    return true;
  }

  if (!serviceCity) {
    return false;
  }
  // Check if object
  if (typeof serviceCity[0] === 'object') {
    return find(serviceCity, { name: nameCity });
  }

  return serviceCity.indexOf(nameCity) >= 0;
};

export const getSubDistrictFromDistrict = (districtName, cityName) => {
  let subDistricts = [];
  const cityDictionary = getCityDictionary();
  const newCityName = findTaskCity(cityName);
  const newDistrictName = findTaskDistrict(districtName, cityName);
  const objCity = cityDictionary.find((ci) => ci.name === newCityName);

  if (objCity) {
    forEach(objCity.district, (district) => {
      if (district?.name === newDistrictName && district?.subDistrict?.length) {
        subDistricts = [...district?.subDistrict];
        return;
      }
    });
  }

  return subDistricts;
};

/**
 * return district if exist, null when not exist
 */
export const getDistrictFromAddress = (dataAddress) => {
  let district = null;
  // check district exist
  if (dataAddress.district) {
    // make sure district correct format
    district = findTaskDistrict(dataAddress.district, dataAddress.city, false);
  } else {
    // not exist
    //get district from address
    district = findTaskDistrict(dataAddress.address, dataAddress.city, false);
  }
  // //send logger
  // if (!district) {
  //   sendLogger('Missing district', dataAddress);
  // }
  return district;
};

export const getSubDistrictFromAddress = (dataAddress) => {
  return findTaskSubDistrict(
    dataAddress.subDistrict,
    dataAddress.district,
    dataAddress.city,
    false,
  );
};

export const checkSubDistrict = (district, city) => {
  // nếu district không có subDistrict thì không check subDistrict
  return !isEmpty(getSubDistrictFromDistrict(district, city));
};

/**
 * @description check support and status of city
 * @param cityName name of city need book task
 * @returns Boolean
 * true if support
 * false if city have status LOCKED, not open city
 */
export const checkSupportCityAndAlert = (cityName) => {
  const cityDictionary = getCityDictionary();
  const objCity = cityDictionary.find((city) => city.name === cityName);
  // not open city
  if (!objCity) {
    AlertHolder.alert.open?.(
      {
        title: i18n.t('DIALOG_TITLE_ERROR'),
        message: i18n.t('CITY_NOT_APPLY_FOR_SERVICE'),
        actions: [{ text: i18n.t('CLOSE') }],
      },
      true,
    );
    return false;
  }
  // locked city, ex covid
  if (objCity?.status === 'LOCKED') {
    AlertHolder.alert.open?.(
      {
        title: i18n.t('PT1_MAP_POPUP_NOT_SUPPORT_TITLE'),
        message: i18n.t('CITY_LOCKED'),
        actions: [{ text: i18n.t('CLOSE') }],
      },
      true,
    );
    return false;
  }
  // city ok
  return true;
};
