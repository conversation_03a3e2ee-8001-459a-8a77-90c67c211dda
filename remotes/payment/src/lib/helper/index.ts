import {
  DateTimeHelpers,
  getIsoCodeGlobal,
  ISO_CODE,
  ITimezone,
  NATIONAL_HOLIDAYS,
} from '@btaskee/design-system';
import { LunarDate } from 'vietnamese-lunar-calendar';

import { DURATIONS_BY_ISO_CODE } from '../configs';

/**
 * @description Get isoCode for variable global
 */
export const getDurationByCountry = () => {
  return DURATIONS_BY_ISO_CODE[getIsoCodeGlobal() || ISO_CODE.VN];
};

/**
 * Trả về ngày lễ, tết của Việt Name
 * @param date
 * @returns
 */
export const getLunaHoliday = (paramDate: any, timezone: ITimezone) => {
  const { date, month } = new LunarDate(
    DateTimeHelpers.toDateTz({ date: paramDate, timezone }).toDate(),
  );
  return (
    NATIONAL_HOLIDAYS.find((d) => d.day === date && d.month === month)?.info ||
    null
  );
};
