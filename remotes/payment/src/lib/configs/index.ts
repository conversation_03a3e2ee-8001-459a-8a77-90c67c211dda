import {
  toolBroom,
  toolBucket,
  toolCif,
  toolCloth,
  toolDishWashingLiquid,
  toolFloorCleaner,
  toolGift,
  toolGlove,
  toolMop,
  toolReedGlass,
  toolToiletScrub,
  toolVacuum,
  toolVim,
  toolWashingDish,
} from '../assets/images';

export const CLEANING_TOOLS = [
  {
    name: 'vacuum',
    image: toolVacuum,
    isPremium: true,
  },
  {
    name: 'broom',
    image: toolBroom,
    isPremium: false,
  },
  {
    name: 'glove',
    image: toolGlove,
    isPremium: false,
  },
  {
    name: 'mop',
    image: toolMop,
    isPremium: false,
  },
  {
    name: 'cloth',
    image: toolCloth,
    isPremium: false,
  },
  {
    name: 'toiletScrub',
    image: toolToiletScrub,
    isPremium: false,
  },
  {
    name: 'washingDish',
    image: toolWashingDish,
    isPremium: false,
  },
  {
    name: 'dishWashingLiquid',
    image: toolDishWashingLiquid,
    isPremium: false,
  },
  {
    name: 'cif',
    image: toolCif,
    isPremium: false,
  },
  {
    name: 'vim',
    image: toolVim,
    isPremium: false,
  },
  {
    name: 'floorCleaner',
    image: toolFloorCleaner,
    isPremium: false,
  },
  {
    name: 'gift',
    image: toolGift,
    isPremium: false,
  },
  {
    name: 'bucket',
    image: toolBucket,
    isPremium: false,
  },
  {
    name: 'reed_glass',
    image: toolReedGlass,
    isPremium: false,
  },
];

export const DURATIONS_BY_ISO_CODE = {
  VN: [
    { duration: 2, area: '55m²', rooms: 'NUMBER_OF_ROOMS' },
    { duration: 3, area: '85m²', rooms: 'NUMBER_OF_ROOMS' },
    { duration: 4, area: '105m²', rooms: 'NUMBER_OF_ROOMS' },
  ],
  TH: [
    { duration: 2, area: '35m²', rooms: 'NUMBER_OF_ROOMS' },
    { duration: 3, area: '35-55m²', rooms: 'NUMBER_OF_ROOMS' },
    { duration: 4, area: '55-100m²', rooms: 'NUMBER_OF_ROOMS' },
  ],
  ID: [
    { duration: 2, area: '55m²', rooms: 'NUMBER_OF_ROOMS' },
    { duration: 3, area: '85m²', rooms: 'NUMBER_OF_ROOMS' },
    { duration: 4, area: '105m²', rooms: 'NUMBER_OF_ROOMS' },
  ],
  MY: [
    { duration: 2, area: '55m²', rooms: 'NUMBER_OF_ROOMS' },
    { duration: 3, area: '85m²', rooms: 'NUMBER_OF_ROOMS' },
    { duration: 4, area: '105m²', rooms: 'NUMBER_OF_ROOMS' },
  ],
};
