import React, { useEffect } from 'react';
import { icArrowLeft, icLocation } from '@assets/images';
import { useAuth } from '@btaskee/auth-store';
import {
  AlertHolder,
  AlertRef,
  BlockView,
  CAlert,
  Colors,
  CText,
  FontFamily,
  IconImage,
  SERVICES,
  Spacing,
  TouchableOpacity,
  usePostTaskStore,
  useSettingsStore,
} from '@btaskee/design-system';
import {
  createNativeStackNavigator,
  type NativeStackNavigationOptions,
} from '@react-navigation/native-stack';
import i18n from '@src/i18n';
import { RootStackParamList } from '@types';

import { useI18n } from '@hooks';
import {
  ChooseAddress,
  ChooseDateTime,
  ChooseDuration,
  ConfirmBooking,
  PostTaskSuccess,
} from '@screens';

const Stack = createNativeStackNavigator<RootStackParamList>();

const MainNavigator = () => {
  const { t } = useI18n();

  const { address, setUser, setService, setCurrency } = usePostTaskStore();
  const { token, userId, rehydrate } = useAuth();
  const settings = useSettingsStore().settings;

  useEffect(() => {
    initData();
  }, [token, userId]);

  // TODO: init data for cleaning service
  const initData = async () => {
    await rehydrate();
    setUser({
      _id: userId ?? '',
      token: token ?? '',
    });
    i18n.changeLanguage('vi');

    const cleaningService = settings?.services?.find(
      (service) => service?.name === SERVICES.CLEANING,
    );

    setService(cleaningService);
    setCurrency(settings?.currency);
  };

  const renderTitle = () => {
    return (
      <BlockView
        flex
        row
        horizontal
      >
        <IconImage
          source={icLocation}
          size={24}
          color={Colors.RED}
        />
        <BlockView margin={{ left: Spacing.SPACE_08 }}>
          <CText>{address?.shortAddress}</CText>
          <CText
            bold
            numberOfLines={1}
            margin={{ right: Spacing.SPACE_16 }}
          >
            {address?.address}
          </CText>
        </BlockView>
      </BlockView>
    );
  };

  const renderHeaderLeft = (navigation: any) => {
    return (
      <TouchableOpacity
        onPress={() => navigation?.goBack()}
        activeOpacity={0.7}
      >
        <IconImage
          source={icArrowLeft}
          size={24}
          color={Colors.BLACK}
        />
      </TouchableOpacity>
    );
  };

  return (
    <>
      <Stack.Navigator
        screenOptions={({ navigation }): NativeStackNavigationOptions => ({
          headerShown: true,
          headerLeft: () => renderHeaderLeft(navigation),
          animation: 'slide_from_right',
          animationDuration: 200,
          contentStyle: { backgroundColor: Colors.WHITE },
          headerStyle: {
            backgroundColor: Colors.WHITE,
          },
          headerTitleStyle: {
            color: Colors.BLACK,
            fontSize: 18,
            fontFamily: FontFamily.FONT_BOLD,
          },
        })}
        initialRouteName="CleaningChooseAddress"
      >
        <Stack.Screen
          name="CleaningChooseAddress"
          component={ChooseAddress}
          options={{ title: t('LIST_OF_LOCATIONS') }}
        />
        <Stack.Screen
          name="CleaningChooseDuration"
          component={ChooseDuration}
          options={{ headerTitle: renderTitle }}
        />
        <Stack.Screen
          name="CleaningChooseDateTime"
          component={ChooseDateTime}
          options={{ title: t('WORK_TIME_TITLE') }}
        />
        <Stack.Screen
          name="CleaningConfirmBooking"
          component={ConfirmBooking}
          options={{ title: t('PT2_CONFIRM_HEADER_TITLE') }}
        />
        <Stack.Screen
          name="CleaningPostTaskSuccess"
          component={PostTaskSuccess}
          options={{ headerShown: false }}
        />
      </Stack.Navigator>
      <CAlert ref={(ref: AlertRef) => AlertHolder.setAlert(ref)} />
    </>
  );
};

export default MainNavigator;
