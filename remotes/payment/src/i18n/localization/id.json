{"SV_HC_SCR2_DETAIL_BTN_COST_CURRENCY": "{{cost}} {{currency}}", "SV_HC_SCR2_DETAIL_EXTRA_SV_TITLE": "Anda dapat memilih untuk menambahkan layanan.", "SV_HC_SCR2_DETAIL_EXTRA_SV_NOTE": "<PERSON>a dapat menam<PERSON> layanan", "BTASKEE": "b<PERSON><PERSON><PERSON>", "POST_TASK_STEP_2": {"LIST_TOOL_TASK_NORMAL": "<PERSON><PERSON><PERSON> pembersihan sudah termasuk alat kebersihan standart.", "LIST_TOOL_TASK_PREMIUM": "Layanan pembersihan sudah termasuk standart alat kebersihan dan vacuum cleaner tangan/portable.", "TITLE_MODAL_LIST_TOOLS": "<PERSON>at, peralatan, dan bahan kimia", "PREMIUM": "Premium", "OPTION_PET_FEE": "Untuk membersihkan area hewan peliharaaan dengan efektif, Tasker butuh untuk menggunakan alat khusus juga chemical khusus. Dikarenakan hal tersebut ada biaya tambahan {{price}} yang akan di tambahkan pada tagihan.", "OPTION_PET_NOTE": "Beberapa tasker yang alergi pada bulu peliharaan tidak dapat menjalankan tugasnya. Mohon informasikan hewan peliharaan dengan detail, supaya bisa memaksimalkan layanan.", "SOME_RECOMMEND": "Saran untuk Anda : ", "RECOMMEND_1": "- Beberapa tasker yang alergi pada bulu peliharaan tidak dapat menjalankan tugasnya. Mohon informasikan hewan peliharaan dengan detail, supaya bisa memaksimalkan layanan.", "RECOMMEND_2": "- Untuk memastikan keamanan pada peliharaan dan Tasker kami, mohon untuk tetap jaga hewan peliharaan <PERSON>a da<PERSON> kandang, atau area terpisah dari tempat tasker kami be<PERSON>, terima kasih. ", "AIR_CONDITION_TITLE": "Cuci AC ({{from}} - {{to}}{{unit}})"}, "CLOSE": "<PERSON><PERSON><PERSON>", "MESSAGE_NAME_INVALID": "<PERSON>a tidak boleh mengandung angka dan karakter khusus", "MESSAGE_EMAIL_INVALID": "Email tidak sah", "PHONE_NUMBER_SYNTAX_IS_INCORRECT": "Nomor telepon salah", "ACCOUNT_ACTIVATION_ERROR_PASSWORD_LENGTH": "<PERSON><PERSON> sandi 6 - 12 ka<PERSON><PERSON>, tanpa spasi.", "THIS_FIELD_IS_REQUIRED": "Informasi yang diminta", "MONEY_SYNTAX_INVALID": "<PERSON><PERSON><PERSON> t<PERSON> valid", "NUMBER_OF_HOURS": "{{t}} jam", "DURATION_TEXT": "<PERSON><PERSON><PERSON><PERSON> r<PERSON> {{t1}} atau {{t2}}", "CONTENT_NOT_SUPPORT_CITY": "Layanan belum tersedia di kota ini.", "PT1_DETAIL_CHOOSE_TASKER_EXPLAIN_3_TH": "Tiga jam sebelum tugas dimulai, ji<PERSON> tidak, sistem akan memilihkan untuk Anda dan biaya layanan tambahan akan dikenakan.", "PT1_DETAIL_CHOOSE_TASKER_EXPLAIN_3": "Satu jam sebelum tugas dimulai, ji<PERSON> tidak, sistem akan memilihkan untuk Anda dan biaya layanan tambahan akan dikenakan.", "PT1_DETAIL_CHOOSE_MANUAL_FEES": "Biaya", "PT1_DETAIL_CHOOSE_MANUAL_COST_AND_CURRENCY": "{{cost}} {{currency}}", "PT1_DETAIL_OPTION_ITEM_CHOOSE_MANUAL": "<PERSON><PERSON><PERSON> Tasker secara manual", "PT1_DETAIL_CHOOSE_TASKER_EXPLAIN_1": "Fungsi ini memungkinkan banyak Tasker mengambil tawaran Anda. <PERSON>a dapat memilih salah satu Tasker untuk mengerjakan tawaran Anda.", "PT1_DETAIL_CHOOSE_TASKER_EXPLAIN_2": "Anda harus memesan paling tidak **{{hour}} Jam** sebelum bisa menggunakan layanan ini. ", "PT1_DETAIL_CHOOSE_MANUAL_UNDERSTOOD": "<PERSON><PERSON>", "FAV_TASKER_TITLE": "Apa itu \"Prioritaskan Tasker favorit\" ?", "FAV_TASKER_DESCRIPTION_EXPLAIN_1": "Ini adalah fungsi default ketika Anda memiliki daftar Taskers favorit.", "FAV_TASKER_DESCRIPTION_EXPLAIN_2": "Tasker favorit <PERSON>a akan diprioritaskan. Jika Tasker favorit sedang sibuk atau tidak mengambil tugas, sistem akan mengirimkan pesanan Anda ke Tasker terdekat.", "BOOKING_STEP_2": {"CHOOSE_GENDER": "<PERSON><PERSON><PERSON>", "GENDER_MALE": "Pria", "GENDER_FEMALE": "<PERSON><PERSON>", "WHAT_IS_CHOOSE_GENDER": "Apa yang di maksud <PERSON>?", "DESCRIPTION_CHOOSE_GENDER": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> dapat memilih jenis kelamin <PERSON>er untuk memastikan kualitas pekerjaan dan mendapatkan lebih banyak kemudahan serta kenyamanan ketika Anda memesan layanan bTaskee."}, "PT1_DETAIL_OPTION_TITLE": "<PERSON><PERSON><PERSON>", "PT1_MAP_POPUP_ADD_PET_SUBMIT": "OK", "PT1_DETAIL_OPTION_HAVE_PET": "<PERSON><PERSON><PERSON> den<PERSON> p<PERSON>", "COST_AND_CURRENCY": "{{currency}}{{cost}}", "OTHER": "<PERSON><PERSON><PERSON>", "PREMIUM_TITLE_OPTIONAL": "Pilih layanan premium", "PREMIUM_CONTENT_OPTIONAL": "Layanan premium", "PREMIUM_DETAIL_2": "Dilakukan oleh Tasker premium yang telah menerima pelatihan lanjutan dan lisensi Premium dari bTaskee.", "PREMIUM_DETAIL_3": "Alat Pembersih Standar: Penyedot debu disertakan.',", "PREMIUM": "[object Object]", "PREMIUM_DETAIL_1": "Apa itu Layanan Premium?", "NEXT": "<PERSON><PERSON><PERSON><PERSON>", "UPDATE": "<PERSON><PERSON><PERSON>", "INCLUDE_FEE_SHIP": "<PERSON><PERSON><PERSON><PERSON>", "LAUNDRY_EMERGENCY_FEE": "{{t1}} {{t2}} (biaya pengantaran di hari yang sama)", "TASK_DETAIL": "<PERSON><PERSON><PERSON> tugas", "ADD_ADDRESS": "Tambah<PERSON> alamat baru", "SIGN_UP_NOW": "<PERSON><PERSON><PERSON>", "LIST_OF_LOCATIONS": "<PERSON><PERSON><PERSON>", "SV_HC_SCR2_DETAIL_DURATION_TITLE_NOTE_TH": "Mohon berikan perkiraan area dan waktu yang dibutuhkan untuk membersihkan. **Untuk tugas memasukan barang atau mengeluarkan**, mohon ganti layanan ke **Pembersihan besar**, pembersihan tersebut lebih cocok untuk layanan pindahan. Pembersihan (harian) hanya cocok untuk pembersihan umum atau reguler saja.", "SV_HC_SCR2_DETAIL_DURATION_TITLE_NOTE": "Harap perkirakan area yang tepat untuk dibersihkan", "SV_HC_SCR2_DETAIL_NOTE_INDO": "*Catatan: Layanan ini ditujukan untuk pembersihan rutin sehari-hari dan tidak mencakup kebutuhan k<PERSON>, seperti pasca renovasi atau pasca banjir.", "SV_HC_SCR2_DETAIL_NOTE": "*Catatan : kami hanya melayani di atas 4 jam. <PERSON><PERSON><PERSON> dari 4 jam, mohon pesan ", "NOTE_POST_TASK_CLEANING_1": "<PERSON><PERSON><PERSON> pem<PERSON>an mendalam", "NOTE_POST_TASK_CLEANING_2": " atau 2 sesi terpisah.", "SV_HC_SCR2_DETAIL_DURATION_TITLE": "<PERSON><PERSON><PERSON>", "NUMBER_OF_ROOMS": "{{t}} kamar", "PT1_DETAIL_OPTION_ITEM_FAV_TASKER": "Prioritaskan Tasker favorit", "FAVORITE": "Favourite", "POST_TASK_BEFORE_HOUR": "Anda harus memesan setidaknya **{{hour}} jam** sebelum waktu pemesanan untuk mengunakan fitur **pemilihan tasker manual**. <PERSON><PERSON> pilih waktu yang berbeda untuk memesan.", "WORK_TIME": "<PERSON><PERSON><PERSON>", "CHOOSE_DATE": "Tanggal", "ELDERLY_CARE_CHOOSE_TIME_TITLE": "<PERSON><PERSON><PERSON> waktu", "WORKING_SCHEDULE": "<PERSON><PERSON><PERSON> kerja", "CONFIRM": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CHILD_CARE_NOTE_TITLE": "Catatan untuk tasker", "TASK_NOTE_DESCRIPTION_CHILD_CARE": "Catatan terperinci dari orang tua akan membantu Tasker untuk mempersiapkan lebih baik.", "LABEL_NOTE_FOR_TASKER": "Catatan untuk pekerja", "TASK_NOTE_DESCRIPTION": "Catatan ini akan membantu Tasker melakukan peker<PERSON>an lebih baik", "TITLE_CHECKBOX_TASK_NOTE": "<PERSON><PERSON>n untuk pemesanan se<PERSON>", "DISINFECTION_SERVICE_NOTE_CONTENT": "<PERSON><PERSON> Anda memiliki permintaan lebih lan<PERSON>, silakan tambah di sini", "SUPPLY_DEMAND_COST_INCREASE": "<PERSON>rga naik karena permintaan yang tinggi saat ini.", "WEEKLY_REPEATER_IT_MEAN_TITLE": "Apa itu J<PERSON>wal <PERSON>?", "CANCEL": "Batalkan", "OK": "<PERSON><PERSON><PERSON>", "UNDERSTOOD": "saya men<PERSON>ti", "POST_TASK_CHECKBOX_REPEAT": "<PERSON><PERSON><PERSON> mingguan", "COUNTDOWN_DAY": "<PERSON>", "COUNTDOWN_HOUR": "Jam", "TERMS_AND_CONDITIONS_TH": "⚠️ T&C", "TERMS_AND_CONDITIONS_1_TH": "1. <PERSON><PERSON> konfi<PERSON><PERSON> bahwa orang yuristik di desa atau kondominium Anda akan mengizinkan pembersih atau teknisi untuk mengakses area tersebut untuk melakukan layanan sebelum melakukan reservasi setelah jam 4 sore pada hari kerja, <PERSON><PERSON><PERSON> hi<PERSON><PERSON>, atau pada hari libur umum.", "TERMS_AND_CONDITIONS_2_TH": "2. <PERSON><PERSON> teknisi atau pembersih tidak dapat memberikan layanan karena orang yuristik tidak diizinkan memasuki area tersebut, <PERSON><PERSON> harus dikenakan biaya 100% dari biaya layanan kepada perusahaan.", "PRE_BOOKING_WARNING_BOOKING_DATE": "{{eventName}} tersedia untuk pemesanan layanan pada {{startDate}} - {{endDate}}. Jika melakukan pemesanan di luar tanggal ini atau permintaan mendadak (dalam 72 jam), silakan pilih Regular Booking (bisa dipesan maksimal 7 hari sebelumnya).", "CHOOSE_TIME": "<PERSON><PERSON><PERSON>", "DIALOG_TITLE_INFORMATION": "Notif<PERSON><PERSON>", "TOMORROW": "Besok", "YESTERDAY": "<PERSON><PERSON><PERSON>", "PT2_POPUP_ERROR_TIME_CLOSE": "<PERSON><PERSON><PERSON>", "POSTTASK_STEP2_ERROR_TIME": "Waktu tidak sah. <PERSON><PERSON> pesan setidaknya {{t}} menit sebelumnya.", "PT2_POPUP_ERROR_TIME_INVALID_CONTENT": "<PERSON><PERSON><PERSON> pilih waktu kerja yang berbeda. Kami hanya mengizinkan waktu kerja dari {{from}} hing<PERSON> {{to}}.", "PT2_POPUP_ERROR_TIME_INVALID_CLOSE": "<PERSON><PERSON><PERSON>", "WEEKLY_REPEATER_BODY_1": "Opsi ini digunakan untuk pelanggan yang membutuhkan kebutuhan **pembersihan mingguan**.", "WEEKLY_REPEATER_BODY_2": "Sistem akan **otomatis mengatur tugas** pada hari-hari yang Anda pilih sebelumnya.", "WEEKLY_REPEATER_BODY_3": "Anda dapat mengubah waktu kerja dari tugas yang sudah diposting (di Tunggu); atau Anda dapat memperbarui informasi Jadwal Mingguan langsung di aplikasi.", "FAV_TASKER": {"TASKER": "Tasker", "SCHEDULE_OF_MORNING": "<PERSON><PERSON>", "SCHEDULE_OF_AFTERNOON": "Sore", "SCHEDULE_OF_EVENING": "Malam", "CONFLICT_TIME_NOTE": "Mohon maaf ! W<PERSON>tu yang Anda pilih bertabrakan dengan jadwal Tasker. <PERSON><PERSON><PERSON> periksa jadwal Tasker di bawah ini dan pilih slot waktu yang berbeda.", "OPTION_TIME": "<PERSON><PERSON><PERSON> {{stt}}", "ADD_OPTION_TIME": "Tambahkan waktu kerja tersedia", "ADD_OPTION_TIME_1": "Tambahkan pilihan", "ADD_OPTION_TIME_2": "Tidak, Terima kasih", "ADD_OPTION_TIME_CONTENT": "Anda juga dapat menyarankan hingga 3 jadwal dari waktu tambahan untuk Tasker agar mereka dapat memilih dengan fleksibel sesuai jadwal mereka. Aplikasi akan menampilkan waktu yang tersedia dari Tasker untuk membantu Anda membuat pilihan dengan mudah.", "VOUCHER_DISCOUNT": "Voucher diskon", "CHANGE_OPTION_TIME": "Ganti jam bekerja ", "WARING_FAV_TASKER_NOT_ACCEPT": "Jika Tasker favorit <PERSON><PERSON>, b<PERSON><PERSON><PERSON> menyar<PERSON><PERSON>a untuk mengirim ulang tugas ke Tasker lain dalam sistem.", "SEND_TO_OTHER_TASKER": "<PERSON><PERSON> tugas ke Tasker lain", "DESCRIPTION_SEND_TO_OTHER_TASKER": "<PERSON><PERSON> pilih waktu yang tersedia dan sesuai dengan kebutuhan <PERSON>a", "FIND_OTHER_TASKER": "<PERSON><PERSON> tasker lain", "CONTENT_CONFIRMED_FIND_OTHER_TASKER": "<PERSON><PERSON> meng<PERSON>si jika ingin mengirimkan tugas ini, untuk menemukan tasker lain", "WORKING_TIME": "<PERSON><PERSON><PERSON> kerja", "WORKING_DATE": "Tanggal", "PRICE": "<PERSON><PERSON>", "WARNING_PAYMENT_METHOD_CASH": "<PERSON>elah Tasker <PERSON><PERSON><PERSON>, sistem akan memperbarui harga layanan sesuai dengan opsi yang dipilih oleh Tasker.", "WARNING_PAYMENT_METHOD_CARD": "<PERSON><PERSON> layanan akan diperbarui sesuai dengan opsi yang dipilih oleh Tasker. Sistem hanya akan mengurangi jumlah bayar setelah Tasker men<PERSON>ma tugas.", "WARNING_PAYMENT_METHOD_PREPAID": "Untuk memesan tugas ini, <PERSON><PERSON> perlu membayar jumlah tertinggi di antara pilihan. Jika Tasker memilih opsi dengan harga lebih rendah, se<PERSON><PERSON><PERSON> akan dikembalikan ke akun yang digunakan untuk membayar yang sesuai.", "WAITING_TASKER_ACCEPT": "<PERSON><PERSON> menunggu {{tasker}} untuk menerima...", "WARNING_CHANGE_TASK_1": "<PERSON>a tidak dapat mengubah waktu kerja karena tugas telah dikirim ke Tasker favorit. <PERSON><PERSON>, Tasker akan segera member<PERSON>n respons kepada <PERSON>.", "WARNING_CHANGE_TASK_2": "Ji<PERSON> Anda ingin mengubah waktu kerja, silakan berk<PERSON><PERSON><PERSON>i dengan Tasker sebelum membatalkan tugas.", "CANCEL_TASK": "Batalkan", "SEE_TASKER_SCHEDULE": "<PERSON><PERSON> Tasker", "TASKER_WORKING_SCHEDULE": "<PERSON><PERSON><PERSON>er", "TASKER_NOT_SUPPORT_BOOKING": "<PERSON><PERSON>, <PERSON>a tidak dapat menggunakan fitur pemesanan ulang dengan Tasker favorit Anda untuk pesanan ini. <PERSON><PERSON> layanan yang saat ini disediakan oleh Tasker tersebut tidak mendukung fitur ini.", "WARNING_BOOKING_WITH_FAV_TASKER": "Anda perlu memesan minimal 3 jam sebelumnya agar Tasker Favorit Anda memiliki cukup waktu untuk menerima tugas tersebut. <PERSON>lak<PERSON> pilih waktu kerja yang berbeda.", "WARNING_PAYMENT_METHOD_KREDIVO": "Untuk memesan tugas ini, <PERSON><PERSON> perlu membayar jumlah tertinggi di antara pilihan waktu. Jika Tasker memilih pilihan waktu dengan harga lebih rendah, se<PERSON><PERSON><PERSON> akan dikembalikan ke dompet bPay Anda.", "TASKER_WILL_ARRIVE_AT_TIME": "Tasker akan datang pada pukul {{time}}", "ADD_OPTION": "Tambahkan lebih banyak pilihan", "ADD_OPTION_NOTE": "Jika Tasker menolak atau tidak merespons tugas ini dalam waktu yang ditentukan, apa<PERSON><PERSON> Anda ingin sistem mengirim ulang tugas ke Tasker lain?", "ADD_OPTION_NOTE_2": "Jika Anda tidak memilih opsi ini, kami akan memberi tahu Anda ketika Tasker menolak atau tidak merespons.", "ADD_OPTION_CHECK_BOOK": "<PERSON><PERSON> tug<PERSON> ini ke Tasker lain", "TIME_FRAME": "<PERSON><PERSON><PERSON>"}, "DIALOG": {"ERROR_TRY_AGAIN": "There was a error. Please try again[{{t}}]", "USERNAME_PASSWORD_INCORRECT": "Nomor telepon atau password salah. Mohon coba lagi!", "ERROR_TIME_INVALID_CONTENT": "<PERSON><PERSON><PERSON> pilih waktu kerja yang berbeda. Kami hanya mengizinkan waktu kerja dari {{from}} hing<PERSON> {{to}}."}, "CHAT_FAV": {"MISSED_CALL": "Panggilan Tak Terjawab", "INCOME_CALL": "Panggilan <PERSON>", "OUTGOING_CALL": "<PERSON>gg<PERSON><PERSON>", "CALL_AGAIN": "Klik untuk Menelepon Kembali", "WAITING_TASKER_ACCEPT": "Menunggu konfirmasi dari TaskerMenunggu konfirmasi dari Tasker", "SENT_FOR_TASKER": "Sistem telah mengirim pemberitahuan ke Tasker", "HOUR": {"one": "{{count}} jam", "other": "{{count}} jam"}, "MINUTE": {"one": "{{count}} menit", "other": "{{count}} menit"}, "SECOND": {"one": "{{count}} <PERSON><PERSON>", "other": "{{count}} <PERSON><PERSON>"}, "WAITING_ACCEPT": "<PERSON><PERSON><PERSON> konfi<PERSON><PERSON>", "COMING_SOON": "Confirmed", "CONFIRM_CANCEL": "<PERSON><PERSON><PERSON><PERSON> Anda ingin membatalkan tugas ini?", "TASK_EXPIRED_AFTER": "<PERSON><PERSON><PERSON> dalam ", "CHOOSE_DATE_OPTION": "<PERSON><PERSON><PERSON> jadwal kerja", "CHOOSE_DATE_OPTION_CONTENT": "<PERSON><PERSON><PERSON> pilih jadwal kerja sebelum mengirim pekerjaan ke Tasker lain", "SENT_TO_OTHER_TASKER": "<PERSON><PERSON> ke tasker lain", "SENT_TO_OTHER_TASKER_CONTENT": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin mengirim pekerjaan ke Tasker lain?", "BOOK_TASK": "<PERSON><PERSON>", "CONFIRM_CHANGE_DATE_OPTION": "<PERSON><PERSON><PERSON> untuk mengubah ke jadwal lain", "CONTENT_CONFIRM_CHANGE_DATE_OPTION": "<PERSON><PERSON> ke jadwal lain", "REJECT_CHANGE_DATE_OPTION": "<PERSON><PERSON> j<PERSON>wal", "CONTENT_REJECT_CHANGE_DATE_OPTION": "<PERSON><PERSON><PERSON><PERSON><PERSON> akan di<PERSON><PERSON> kembali ke Tasker dengan jadwal yang Anda pilih. <PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menolak jadwal yang diusulkan oleh Tasker?", "SENDING": "Sedang mengirim", "CONTENT_TO_FAV_TASKER": "Hubungi Tasker favoritmu dengan cepat untuk ngobrol sebelum memesan tugas!", "CHAT_WITH_FAV": "<PERSON>t dengan Tasker favoritmu", "WARNING_TIME_BOOK_TASK": "Untuk memastikan Tasker favorit <PERSON>a dapat men<PERSON>ma tugas, harap diskusikan waktu kerja dengan mereka sebelum melakukan pemesanan."}, "BOOK_TASK_AND_PAYMENT": "Pembayaran", "BUTTON_BOOKING": "<PERSON><PERSON>", "TET_BOOKING_TO_NORMAL_TASK": "<PERSON><PERSON><PERSON> ke pesanan reguler", "TET_BOOKING_TO_NORMAL_TASK_DESCRIPTION": "Klik di sini jika Anda tidak ingin membayar sekarang dan ingin kembali ke pemesanan reguler (Pembayaran tunai, pemesanan harus dilakukan hingga 7 hari sebelumnya)", "TET_BOOKING_TO_NOMAL_NOTE_TITLE": "Catatan", "TET_BOOKING_TO_NOMAL_NOTE_DONE": "Pembaruan", "TET_BOOKING_TO_NORMAL_TASK_CHANGE_DATE": "Tanggal tugas yang Anda pilih tidak sesuai. Anda perlu memperbarui waktu tugas agar sesuai dengan pemesanan reguler - Dalam 7 hari ke depan.", "STEP_4_UPDATE_CALENDAR_TITLE": "<PERSON> kerja", "STEP_4_UPDATE_TIME_TITLE": "Pilih jam kerja", "TOTAL": "<PERSON><PERSON><PERSON>", "LOCATION": "<PERSON><PERSON>", "HK_SV_ADDRESS_DETAIL": "<PERSON><PERSON><PERSON>", "EDIT": "Ubah", "CONTACT_INFO_MODAL_TITLE": "Hubung<PERSON>", "PHONE_NUMBER": "Nomor telepon", "CONTACT_NAME": "<PERSON><PERSON> k<PERSON>", "PRICE_SERVICE": "<PERSON><PERSON><PERSON>", "TOTAL_PAYMENT": "Total pembayaran", "TITLE_PAYMENT_DETAIL": "<PERSON><PERSON><PERSON>", "TAB_PROMOTION": "<PERSON><PERSON><PERSON>", "PAYMENT_METHOD": "<PERSON><PERSON>", "WORKLOAD": "<PERSON><PERSON> kerja", "HAVE_PET": "<PERSON><PERSON><PERSON> dengan hewan peli<PERSON>an", "ADD_ON_SERVICE": "<PERSON><PERSON><PERSON> ta<PERSON>", "IS_PREMIUM": "<PERSON><PERSON>", "TASK_PREMIUM": "Layanan Premium", "TASK_INFO": "Informasi pesanan", "WORK_IN": "<PERSON><PERSON><PERSON>", "WORKING_DAY": "Tanggal", "TIME_TO_WORK": "<PERSON><PERSON><PERSON>", "TERMS_OF_USED_SERVICES_1_TH": "1. <PERSON><PERSON> pelanggan dibayar oleh PromptPay tetapi tugas dibatalkan karena tidak ada penyedia layanan, uang akan secara otomatis dikembalikan ke akun BPAY dengan jumlah penuh.", "TERMS_OF_USED_SERVICES_2_TH": "2. <PERSON><PERSON> ingin menarik uang dari akun BPAY Anda, akan ada biaya penarikan 20% dari jumlah penarikan.", "TERMS_OF_USED_SERVICES_3_TH": "3. Penarikan dari akun BPAY yang memenuhi syarat untuk pengembalian uang 100% hanya dalam kondisi di bawah ini:", "TERMS_OF_USED_SERVICES_3_1_TH": "3.1 <PERSON><PERSON><PERSON> melalui PromptPay tetapi membatalkan layanan karena tidak ada penyedia layanan.", "TERMS_OF_USED_SERVICES_3_2_TH": "3.2 Penyedia layanan membatalkan tugas kurang dari 2 jam sebelum waktu layanan.", "TERMS_OF_USED_SERVICES_4_TH": "4. Penarikan dari akun BPAY akan memakan waktu 7 - 14 hari kerja untuk diproses.", "TERMS_OF_USED_SERVICES_5_TH": "5. Faktur atau invoice akan dikirimkan berdasarkan biaya platform bTaskee dan mencakup pajak pertambahan nilai (PPN).", "TERMS_OF_USED_SERVICES_TH": "Catatan", "TERM_OF_PAYMENT_METHOD_E_WALL": {"TERM_1": "1. If you cancel the task, the remaining balance, after deducting the cancellation fee, will be refunded to your e-Wallet.", "TERM_2": "2. You are eligible for a 100% refund only if the cancellation is made by the Tasker.", "TERM_3": "3. Refunds will be processed within 7-14 business days.", "TERM_4": "4. The service invoice will be issued based on the bTaskee platform fee and includes value-added tax (VAT)."}, "TERM_OF_PAYMENT_METHOD_CARD": {"TERM_1": "Your card will be charged after the task has been confirmed.", "TERM_2": "Cancellations and Refunds", "TERM_2_1": "1. If you cancel the task, the remaining balance, after deducting the cancellation fee, will be refunded to your card.", "TERM_2_2": "2. You are eligible for a 100% refund only if the cancellation is made by the Tasker.", "TERM_2_3": "3. Refunds will be processed within 7-14 business days.", "TERM_3": "The service invoice will be issued based on the bTaskee platform fee and includes value-added tax (VAT)."}, "LIMIT_DURATION": "<PERSON><PERSON><PERSON> maksimal ad<PERSON>h {{t}}jam. <PERSON><PERSON> kurangi waktu atau pesan 2 pesanan berturut.", "WORK_TIME_TITLE": "<PERSON><PERSON><PERSON>", "PT2_CONFIRM_HEADER_TITLE": "Konfirm dan bayar", "WORK_IN_TIME_FROM_A_TO_B": "{{t1}} jam, dari {{t2}} sampai {{t3}}", "DOG": "<PERSON><PERSON><PERSON>", "CAT": "Kucing", "PAYMENT_METHOD_CARD": "Visa / Master Card", "PAYMENT_METHOD_BANK_TRANSFER": "ATM / Internet banking", "PAYMENT_METHOD_CREDIT": "b<PERSON>ay", "PAYMENT_METHOD_MOMO": "<PERSON><PERSON>", "PAYMENT_METHOD_DIRECT_TRANSFER": "Transfer bank", "PAYMENT_METHOD_DIRECT_CASH": "Tunai", "PAYMENT_METHOD_ZALO_PAY": "Zalopay", "TASK_SAME_TIME_MESSAGE": "Anda sudah memesan satu Tasker saat ini. A<PERSON><PERSON>h Anda yakin ingin memesan 1 Tasker lagi?", "PAYMENT_METHOD_VIRTUAL_ACCOUNT": "Virtual Account", "MODAL_POST_TASK_SUCCESS_TITLE": "Pen<PERSON><PERSON> pesanan be<PERSON>", "MODAL_POST_TASK_SUCCESS_CONTENT": "Anda telah ber<PERSON>il memposting pesanan, Anda dapat mencentang pesanan ini di menu Aktivitas", "MODAL_POST_TASK_SUCCESS_BTN_FOLLOW_TASK": "Melacak pesanan", "MODAL_POST_TASK_SUCCESS_BTN_GO_HOME": "<PERSON><PERSON><PERSON> ke beranda", "HIDE_PREMIUM_TOOLS_DETAIL": "<PERSON><PERSON><PERSON><PERSON> lebih sedikit", "SEE_MORE": "<PERSON><PERSON>"}