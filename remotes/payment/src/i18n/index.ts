// eslint-disable-next-line no-restricted-imports
import { initReactI18next } from 'react-i18next';
import i18n from 'i18next';

import en from './localization/en.json';
import id from './localization/id.json';
import ko from './localization/ko.json';
import ms from './localization/ms.json';
import th from './localization/th.json';
import vi from './localization/vi.json';

const resources = {
  en: {
    translation: en,
  },
  vi: {
    translation: vi,
  },
  th: {
    translation: th,
  },
  ko: {
    translation: ko,
  },
  ms: {
    translation: ms,
  },
  id: {
    translation: id,
  },
};

type SupportedLanguages = keyof typeof resources;

// Initialize with default language (will be updated by host app)
i18n.use(initReactI18next).init({
  resources,
  lng: 'en', // Default language
  fallbackLng: 'en',
  interpolation: {
    escapeValue: false,
  },
});

// Function to change language from host app
export const setLanguage = (language: string) => {
  if (Object.keys(resources).includes(language)) {
    i18n.changeLanguage(language as SupportedLanguages);
  }
};

export default i18n;
