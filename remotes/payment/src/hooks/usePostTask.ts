import {
  bookTaskForceTasker,
  checkTaskSameTime,
  getPiceAPI,
  postTaskAPI,
} from '@apis';
import {
  AlertHolder,
  DateTimeHelpers,
  DeviceHelper,
  getPhoneNumber,
  handleError,
  PAYMENT_METHOD,
  usePostTaskStore,
} from '@btaskee/design-system';
import i18n from '@src/i18n';
import { IDataBooking, IParamsGetPrice } from '@types';
import { get, isEmpty } from 'lodash-es';

export const usePostTask = () => {
  const { setPrice, setLoadingPrice, setLoadingPostTask, user } =
    usePostTaskStore();

  const getDataPricing = () => {
    // Get the latest state directly from the store
    const currentState = usePostTaskStore.getState();
    const {
      address,
      date,
      duration,
      isAutoChooseTasker,
      service,
      forceTasker,
      dateOptions,
      addons,
      paymentMethod,
      requirements,
      isPremium,
      isoCode,
    } = currentState;

    if (!address || !date || !duration) {
      return null;
    }

    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

    // base info
    const task: IParamsGetPrice['task'] = {
      timezone,
      date: DateTimeHelpers.formatToString({ timezone, date }),
      autoChooseTasker: isAutoChooseTasker,
      taskPlace: {
        country: address?.country,
        city: address?.city,
        district: address?.district,
      },
      homeType: address?.homeType,
      duration: duration,
    };

    if (!isEmpty(forceTasker)) {
      task.forceTasker = forceTasker;
    }
    if (!isEmpty(dateOptions)) {
      task.dateOptions = dateOptions;
    }
    if (paymentMethod?.value) {
      task.payment = {
        method: paymentMethod?.value,
      };
    }
    // with promotion
    // if (promotion) {
    //   task.promotion = {
    //     code: promotion.code,
    //   };
    // } else if (paymentMethod?.promotionPaymentMethodCode) {
    //   // with promotion PaymentMethod
    //   task.promotion = {
    //     promotionPaymentMethodCode: paymentMethod?.promotionPaymentMethodCode,
    //   };
    // }
    // addOnService
    if (!isEmpty(requirements)) {
      task.requirements = requirements.map((req) => ({ type: req.type })); // send type only
    }
    if (!isEmpty(addons)) {
      task.addons = addons;
    }
    // Check premium service
    if (isPremium) {
      task.isPremium = true;
    }
    return { task, service: { _id: service._id }, isoCode };
  };

  const getPrice = async () => {
    // refactor data after call get price
    const data = getDataPricing();

    // data is null, no get price
    if (!data) {
      // set price is nul --> hide price button.
      return setPrice(null);
    }

    // show loading
    setLoadingPostTask(true);

    // show loading price
    setLoadingPrice(true);

    // call get price API
    const result = await getPiceAPI(data);

    // end get API, hide loading
    setLoadingPostTask(false);
    setLoadingPrice(false);

    if (result?.isSuccess) {
      setPrice(result?.data || null);
      return;
    }
    handleError(result?.error);
    setPrice(null);
  };

  const _refactorDataPostTask = () => {
    // Get the latest state directly from the store
    const currentState = usePostTaskStore.getState();
    const {
      address,
      date,
      duration,
      isAutoChooseTasker,
      service,
      forceTasker,
      dateOptions,
      addons,
      paymentMethod,
      requirements,
      isPremium,
      isoCode,
      isFavouriteTasker,
      isApplyNoteForAllTask,
      note,
      pet,
      relatedTask,
      user,
      schedule,
      isEnabledSchedule,
      gender,
    } = currentState;

    const isAddressMaybeWrong = Boolean(address?.isAddressMaybeWrong);
    const isTet = service?.isTet || false;
    const city = address?.city;
    const timezone = DateTimeHelpers.getTimezoneByCity(city);

    // base task info
    const task: IDataBooking = {
      address: address?.address,
      contactName: address?.contact || user?.name,
      lat: address?.lat,
      lng: address?.lng,
      phone: address?.phoneNumber || user?.phone,
      countryCode: address?.countryCode || user?.countryCode,
      description: address?.description,
      askerId: user?._id,
      autoChooseTasker: isAutoChooseTasker,
      date: DateTimeHelpers.formatToString({ date: date, timezone }),
      timezone,
      deviceInfo: DeviceHelper.getDeviceInfo(),
      duration: duration,
      homeType: address?.homeType,
      houseNumber: address?.description,
      isSendToFavTaskers: Boolean(isFavouriteTasker),
      isoCode,
      payment: {
        method: paymentMethod?.value,
      },
      serviceId: service?._id,
      taskPlace: {
        country: address?.country,
        city,
        district: address?.district,
      },
      updateTaskNoteToUser: isApplyNoteForAllTask,
      shortAddress: address?.shortAddress,
    };

    // Refactor phone number - refill 0 at first
    task.phone = getPhoneNumber(task.phone || '', task.countryCode || '');

    // Địa chỉ có thể bị sai => khi book task, send to slack cho team vận hành xử lý
    if (isAddressMaybeWrong) {
      task.taskPlace.isAddressMaybeWrong = isAddressMaybeWrong;
    }

    if (isTet) {
      task.isTetBooking = true;
    }

    if (note && note?.trim()) {
      task.taskNote = note?.trim();
    }

    // promotion
    // if (promotion) {
    //   task.promotion = { code: promotion?.code };
    // } else if (paymentMethod?.promotionPaymentMethodCode) {
    //   task.promotion = { promotionPaymentMethodCode: paymentMethod?.promotionPaymentMethodCode };
    // }

    // ----- Payment -----
    // payment with card
    if (task.payment?.method === PAYMENT_METHOD.card) {
      task.payment.cardId = paymentMethod?.cardInfo?._id;
    }

    // payment with virtualAccount
    if (task.payment?.method === PAYMENT_METHOD.virtualAccount) {
      task.payment.bank = paymentMethod?.bank;
    }

    // payment with truemoney wallet
    if (
      task.payment?.method === PAYMENT_METHOD.trueMoney &&
      paymentMethod?.walletInfo
    ) {
      task.payment = { ...task.payment, ...paymentMethod?.walletInfo };
    }

    // ----- End Payment -----

    // pet
    if (pet) {
      task.pet = pet;
    }

    // addOnService
    if (!isEmpty(requirements)) {
      task.requirements = requirements.map((req) => ({ type: req.type })); // send type only
    }
    // add schedule
    if (isEnabledSchedule && !isEmpty(schedule)) {
      task.weekday = schedule;
    }

    // premium service
    if (isPremium) {
      task.isPremium = isPremium;
    }

    // Asker tự chọn giới tính của Tasker
    if (gender) {
      task.gender = gender;
    }

    // Asker chọn Tasker
    if (forceTasker?._id) {
      delete task.isSendToFavTaskers;
      task.autoChooseTasker = true;
      task.forceTasker = {
        taskerId: forceTasker?._id,
        isResent: Boolean(forceTasker?.isResent),
      };
    }

    // Asker chọn lịch của Tasker
    if (dateOptions) {
      task.dateOptions = dateOptions;
    }

    // Nếu có addons thì thêm addons vào
    if (!isEmpty(addons)) {
      task.addons = addons;
    }

    if (!isEmpty(relatedTask?.relatedTaskId)) {
      task.source = {
        from: relatedTask?.service?.name,
        taskId: relatedTask?.relatedTaskId,
      };
    }

    return task;
  };

  const _postTaskProvider = async (dataTask: IDataBooking) => {
    if (isEmpty(dataTask?.dateOptions)) {
      delete dataTask.dateOptions;
    }
    if (!isEmpty(dataTask?.forceTasker)) {
      return await bookTaskForceTasker({ data: dataTask, token: user?.token });
    }
    return await postTaskAPI({ data: dataTask, token: user?.token });
  };

  const _addTask = async () => {
    const dataTask = _refactorDataPostTask();
    // call post task
    const result = await _postTaskProvider(dataTask);
    // hide loading
    setLoadingPostTask(false);
    // process respond
    if (result?.isSuccess) {
      // Tracking
      // const serviceText = getServiceTextToTracking(dataTask?.service?.name);
      // const dataUser = {
      //   lifetimeCount: get(app, 'user.taskDone', null),
      //   statusUser: getStatusUser(get(app, 'user.taskDone', null)),
      // };
      // const dataTaskTracking = cloneDeep(dataTask?.task);
      // dataTaskTracking.finalCost = postTask?.price?.finalCost;

      // Tracking CleverTap Post task success
      // await dispatch(setIsBookedTask(true));
      // trackingCleverTapTaskPostSuccess(dataUser, dataTaskTracking, serviceText, app?.configSpecialPreBooking?.name);
      //firebase Analytics
      // TrackFirebaseAnalytics.taskPostSuccess(postTask?.service?._id, app?.user?.taskDone);
      // End tracking

      // success
      // return dispatch(_setData(result.data));
      return result.data;
    }
    // error
    handleError(result?.error);
    // dispatch(_setData({ error: result?.error }));
    // const alertObj = {
    //   title: 'DIALOG_TITLE_INFORMATION',
    //   message: 'ERROR_TRY_AGAIN',
    //   actions: [{ text: 'CLOSE' }],
    // };
    // switch (result?.error?.code) {
    //   case 'NOT_ENOUGH_MONEY':
    //     alertObj.message = {
    //       text: 'BPAY_LACK_OF_MONEY',
    //       params: {
    //         cost: formatMoney(result?.error?.data?.amount),
    //         currency: getCurrency(result?.error?.data, 1),
    //       },
    //     };
    //     alertObj.actions = [
    //       { text: 'CLOSE', style: 'cancel' },
    //       { text: 'ADD_MONEY', onPress: () => navigation?.navigate(RouteName.UserBpay) },
    //     ];
    //     break;
    //   case 'NOT_ENOUGH_BPAY_BUSINESS':
    //     alertObj.title = 'TITLE_NOT_ENOUGH_MONEY';
    //     alertObj.message = {
    //       text: 'MESSAGE_NOT_ENOUGH_BPAY_BUSINESS',
    //       params: {
    //         cost: formatMoney(result?.error?.data?.amount),
    //         currency: getCurrency(result?.error?.data, 1),
    //       },
    //     };
    //     alertObj.actions = [{ text: 'CLOSE', style: 'cancel' }];
    //     break;
    //   case 'DATE_TIME_ERROR':
    //     alertObj.message = 'INCORRECT_DATE_TIME';
    //     break;
    //   case 'OUTSTANDING_PAYMENT_STATUS_NEW':
    //     return { code: 'OUTSTANDING_PAYMENT_STATUS_NEW' };
    //   case 'PAYMENT_CARD_EXPIRED':
    //     alertObj.message = 'PAYMENT_CARD_EXPIRED';
    //     break;
    //   case 'USER_STATUS_DISABLED':
    //     alertObj.message = 'CANCEL_TASK_DISABLED_CONTENT';
    //     break;
    //   case 'BOOKING_DATE_INVALID':
    //     alertObj.message = 'STEP4_ERROR_DATE_TIME';
    //     break;
    //   case 'BPAY_DEBT':
    //     alertObj.message = 'STEP4_BPAY_DEBT';
    //     alertObj.actions = [{ text: 'PAYMENT_TOP_UP', onPress: () => navigation?.navigate(RouteName.UserBpay) }];
    //     break;
    //   case 'SERVICE_PAUSE':
    //     const reason = result?.error?.data?.reason;
    //     if (reason) {
    //       alertObj.message = {
    //         text: reason[app.locale],
    //         notUsedI18n: true,
    //       };
    //     }
    //     break;
    //   case 'BOOKING_IS_DUPLICATE':
    //     alertObj.message = 'STEP4_ERROR_BOOKING_IS_DUPLICATE';
    //     break;
    //   case 'DESCRIPTION_LIMIT':
    //     alertObj.message = 'STEP4_ERROR_BOOKING_DESCRIPTION_LIMIT';
    //     break;
    //   case 'COUNTRY_CODE_INVALID':
    //     alertObj.message = 'COUNTRY_CODE_INVALID';
    //     alertObj.actions = [
    //       {
    //         text: 'OK',
    //         onPress: () => {
    //           navigation.popToTop();
    //           navigation?.navigate(RouteName.UserProfile);
    //         },
    //       },
    //     ];
    //     break;
    //   case 'SERVICE_NOT_STARTED':
    //     alertObj.message = {
    //       text: 'ERROR_SERVICE_NOT_STARTED',
    //       params: {
    //         t: DateTimeHelpers.formatToString({
    //           timezone: DateTimeHelpers.getTimezoneByCity(city),
    //           date: result?.error?.data?.beginDate,
    //           typeFormat: TypeFormatDate.DateShort,
    //           keepLocalTime: true,
    //         }),
    //       },
    //     };
    //     break;
    //   default:
    //     break;
    // }

    // Post to slack when post task error.
    // sendToSlackBookingError({ serviceName: 'cleaning', error: result });

    // show error
    // return AlertHolder.alert.open(alertObj);
  };

  /**
   * @description function booking task
   * @param payload {{object}} data of task
   */
  const postTask = async (callback?: () => void) => {
    const currentState = usePostTaskStore.getState();
    const { date, service, timezone } = currentState;
    // show loading app
    setLoadingPostTask(true);

    // check task same time
    const isSameTime = await checkTaskSameTime({
      taskDate: DateTimeHelpers.formatToString({ date, timezone }),
      serviceId: service?._id || '',
    });

    // time ok
    if (isSameTime?.isSuccess && !isSameTime.data) {
      // call api book task
      const postTaskResult = await _addTask();

      // success
      if (get(postTaskResult, 'bookingId', null)) {
        callback?.();
      }
      return postTaskResult;
    } // end check same time
    // time invalid

    // hide loading
    await setLoadingPostTask(false);

    // same time, alert for user
    return AlertHolder.alert.open({
      title: i18n.t('DIALOG_TITLE_INFORMATION'),
      message: i18n.t('TASK_SAME_TIME_MESSAGE'),
      actions: [
        { text: i18n.t('CLOSE'), style: 'cancel' },
        {
          text: i18n.t('OK'),
          onPress: async () => {
            // show loading
            await setLoadingPostTask(true);
            // wait modal close
            setTimeout(async () => {
              const postTaskResult = await _addTask();
              if (get(postTaskResult, 'bookingId', null)) {
                callback?.();
              }
            }, 300);
          },
        },
      ],
    });
  };

  return { getPrice, postTask };
};
