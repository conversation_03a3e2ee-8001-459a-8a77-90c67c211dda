import React from 'react';
import { SafeAreaView, StyleSheet } from 'react-native';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';
import { ColorsV2, DeviceHelper } from '@btaskee/design-system';
import LottieView from 'lottie-react-native';

import { appLoadingLottie } from '@lottie';

const SIZE_IMAGE = Math.ceil(DeviceHelper.WINDOW.WIDTH / 3);
export const LoadingMiniApp = () => {
  return (
    <SafeAreaView style={styles.container}>
      <Animated.View
        entering={FadeIn}
        exiting={FadeOut}
        style={styles.container}
      >
        <LottieView
          style={{ width: SIZE_IMAGE, height: SIZE_IMAGE }}
          source={appLoadingLottie}
          autoPlay
          loop
        />
      </Animated.View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: ColorsV2.neutralBackground,
  },
});
