import { useCallback, useEffect, useState } from 'react';
import { useAuth } from '@btaskee/auth-store';
import {
  ApiClient,
  CryptoHelpers,
  EndpointKeys,
  getAccessKey,
  useApiMutation,
  useAppStore,
} from '@btaskee/design-system';
import remoteConfig from '@react-native-firebase/remote-config';
import isEmpty from 'lodash-es/isEmpty';

import { AppConfig } from '@utils';

const getBooleanValue = (key: string) => {
  return remoteConfig().getValue(key).asBoolean();
};

const getStringValue = (key: string) => {
  return remoteConfig().getValue(key).asString();
};

export const useAppInit = () => {
  const { isoCode, setFeatureConfig, onChangeIsFirstOpen } = useAppStore();
  const { token, logout } = useAuth();
  const { mutate: getEnvAPI } = useApiMutation({
    key: EndpointKeys.getEnv,
    options: {
      onError: () => {
        setIsError(true);
      },
    },
  });

  const [isReady, setIsReady] = useState(false);
  const [isError, setIsError] = useState(false);

  const initSetting = useCallback(async () => {
    await remoteConfig().fetch(300);
    await remoteConfig().setConfigSettings({
      minimumFetchIntervalMillis: 30000,
    });
    const fetchedRemotely = await remoteConfig()
      .setDefaults({})
      .then(() => remoteConfig().fetchAndActivate());

    return fetchedRemotely;
  }, []);

  const intiRemoteConfig = useCallback(async () => {
    try {
      const configLocal = await AppConfig.getFromStorage();
      const isEmptyConfig = isEmpty(configLocal);
      if (
        !isEmptyConfig &&
        configLocal?.BASE_URL &&
        configLocal?.ACCESS_KEY &&
        configLocal.SECRET_KEY
      ) {
        ApiClient.create({
          baseURL: configLocal.BASE_URL,
          accessKey: getAccessKey(
            configLocal.ACCESS_KEY,
            configLocal.SECRET_KEY,
          ),
        });
        setIsReady(true);
        onChangeIsFirstOpen();
      }
      const isInitDone = await initSetting();

      if (!isInitDone || !isoCode) {
        throw new Error('Init remote config failed');
      }

      const baseURL = getStringValue('BASE_URL');
      const decryptKey = getStringValue('DECRYPT_KEY');
      const accessKeyEnv = JSON.parse(getStringValue('ACCESS_KEY_ENV'));
      const accessKeyGetEnv = accessKeyEnv[isoCode];

      if (!baseURL || !decryptKey || !accessKeyEnv || !accessKeyGetEnv) {
        throw new Error('Missing config');
      }

      ApiClient.create({
        baseURL,
        accessKeyEnv: accessKeyGetEnv,
      });

      getEnvAPI(null, {
        onSuccess: async (data) => {
          if (!data?.data) return;
          const configData = data
            ? JSON.parse(
                CryptoHelpers.decryptWithHMAC(data.data, decryptKey) || '{}',
              )
            : {};

          ApiClient.updateAccessKey(
            getAccessKey(configData.accessKey, configData.secretKey),
          );

          await AppConfig.saveToStorage({
            ACCESS_KEY_ENV: accessKeyEnv,
            DECRYPT_KEY: decryptKey,
            BASE_URL: baseURL,
            ACCESS_KEY: configData.accessKey,
            SECRET_KEY: configData.secretKey,
            AWS3: configData.aws3,
            ADYEN: configData.adyen,
            WEB_SOCKET_ENDPOINT: configData.webSocketEnpoint,
            API_MAINTAIN_URL: configData.apiMaintainURL,
            API_SERVER_URL: configData.apiServerURL,
            MIDTRANS: configData.midTrans,
            VNPAY: configData.vnpay,
            CONFIG_2C2P: configData.config2c2p,
            LOGIN_GOOGLE: configData.loginGoogle,
          });

          setFeatureConfig({
            ENABLE_NEW_FEATURE_PUZZLE_GAME: getBooleanValue(
              'ENABLE_NEW_FEATURE_PUZZLE_GAME',
            ),
            ENABLE_NEW_FEATURE_WATER_GUN: getBooleanValue(
              'ENABLE_NEW_FEATURE_WATER_GUN',
            ),
            ENABLE_NEW_FEATURE_OPEN_MALAYSIA: getBooleanValue(
              'ENABLE_NEW_FEATURE_OPEN_MALAYSIA',
            ),
          });
          setIsReady(true);
          onChangeIsFirstOpen();
        },
      });
    } catch (error) {
      setIsError(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isoCode]);

  useEffect(() => {
    ApiClient.setUnauthorizedHandler(logout);
  }, [logout]);

  useEffect(() => {
    ApiClient.updateIsoCode(isoCode);
  }, [isoCode]);

  useEffect(() => {
    ApiClient.updateToken(token);
  }, [token]);

  useEffect(() => {
    intiRemoteConfig();
    const unsubscribe = remoteConfig().onConfigUpdated(async (update) => {
      //TODO: Handle update remote config
    });
    return () => {
      unsubscribe();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return { intiRemoteConfig, isReady, isError };
};
