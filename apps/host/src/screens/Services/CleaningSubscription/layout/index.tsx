/**
 * Sample React Native App
 * https://github.com/facebook/react-native
 *
 * @format
 */

import React from 'react';

import { ErrorBoundary, LoadingMiniApp } from '@components';

const CleaningSubscription = React.lazy(
  () => import('cleaningSubscription/MainNavigator'),
);

export function CleaningSubscriptionScreen(): React.JSX.Element {
  return (
    <ErrorBoundary name="CleaningSubscriptionScreen">
      <React.Suspense fallback={<LoadingMiniApp />}>
        <CleaningSubscription />
      </React.Suspense>
    </ErrorBoundary>
  );
}
