import React from 'react';

import { ErrorBoundary, LoadingMiniApp } from '@components';

const PatientCare = React.lazy(() => import('patientCare/MainNavigator'));

export function PatientCareScreen(): React.JSX.Element {
  return (
    <ErrorBoundary name="PatientCareScreen">
      <React.Suspense fallback={<LoadingMiniApp />}>
        <PatientCare />
      </React.Suspense>
    </ErrorBoundary>
  );
}
