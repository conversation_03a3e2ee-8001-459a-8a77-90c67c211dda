import React from 'react';

import { ErrorBoundary, LoadingMiniApp } from '@components';

const ElderlyCare = React.lazy(() => import('elderlyCare/MainNavigator'));

export function ElderlyCareScreen(): React.JSX.Element {
  return (
    <ErrorBoundary name="ElderlyCareScreen">
      <React.Suspense fallback={<LoadingMiniApp />}>
        <ElderlyCare />
      </React.Suspense>
    </ErrorBoundary>
  );
}
