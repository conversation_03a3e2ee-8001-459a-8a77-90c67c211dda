/**
 * Sample React Native App
 * https://github.com/facebook/react-native
 *
 * @format
 */

import React, { useEffect } from 'react';
import {
  Dimensions,
  Image,
  ImageSourcePropType,
  ScrollView,
  StatusBar,
  StyleSheet,
} from 'react-native';
import Carousel from 'react-native-reanimated-carousel';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import {
  BlockView,
  Colors,
  CText,
  IconImage,
  LOCALES,
  Spacing,
  TouchableOpacity,
  useAppStore,
} from '@btaskee/design-system';

import { useAppNavigation, useGetSettings } from '@hooks';
import { icArrowRight, icExplore, icMessage } from '@images';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

const CARD_RADIUS = 16;
const CAROUSEL_HEIGHT = 150;
const REWARD_CARD_WIDTH = Dimensions.get('window').width / 2.5;
const REWARD_CARD_HEIGHT = Dimensions.get('window').width / 1.65;

const carouselImages = [
  require('../../../../assets/images/icons/home/<USER>'),
  require('../../../../assets/images/icons/home/<USER>'),
  require('../../../../assets/images/icons/home/<USER>'),
];

const REWARD = [
  {
    id: 1,
    name: 'Giảm 80,000đ cho Thủy Cung',
    points: 40,
    image: require('../../../../assets/images/icons/home/<USER>'),
  },
  {
    id: 2,
    name: 'Giảm 10,000đ cho Fahasa',
    points: 20,
    image: require('../../../../assets/images/icons/home/<USER>'),
  },
  {
    id: 3,
    name: 'Giảm 10,000đ cho Ahamove',
    points: 20,
    image: require('../../../../assets/images/icons/home/<USER>'),
  },
];

// Tạo mảng dịch vụ với 8 item để chia thành 2 hàng, mỗi hàng 4 cột
// Cập nhật tên dịch vụ để test 2 dòng
const services = [
  {
    key: 'CleaningService',
    name: 'Dọn dẹp nhà',
    description: 'ca lẻ',
    icon: 'https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/AJSeGd5v6fmsh7MaY',
  },
  {
    key: 'AirConditionerService',
    name: 'Vệ sinh máy lạnh',
    icon: 'https://btaskee-stag.s3.ap-southeast-1.amazonaws.com/app/asker/services/ve-sinh-van-phong.png',
  },
  {
    key: 'DeepCleaningService',
    name: 'Tổng vệ sinh',
    icon: 'https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/29WH9wztiWfbzNKiF',
  },
  {
    key: 'ChildCareService',
    name: 'Trông Trẻ',
    icon: 'https://btaskee-stag.s3.ap-southeast-1.amazonaws.com/app/asker/services/child-care.png',
  },
  {
    key: 'ElderlyCareService',
    name: 'Chăm sóc người già',
    icon: 'https://btaskee-stag.s3.ap-southeast-1.amazonaws.com/app/asker/services/elderly-care.png',
  },
  {
    key: 'PatientCareService',
    name: 'Chăm sóc người bệnh',
    icon: 'https://btaskee-stag.s3.ap-southeast-1.amazonaws.com/app/asker/services/patient-care.png',
  },
  {
    key: 'CleaningSubscriptionService',
    name: 'Dọn dẹp nhà',
    description: 'gói tháng',
    icon: 'https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/TRtmX8EucKRQCoo3h',
  },

  {
    key: 'ac',
    name: 'bBeauty',
    icon: 'https://btaskee-stag.s3.ap-southeast-1.amazonaws.com/app/asker/services/beauty_care/v1/icon/beauty_care_bg.png',
  },
  {
    key: 'laundry',
    name: 'Khám phá',
    icon: icExplore,
  },
];

export const userName = '';

type ServiceType = { key: string; name: string; icon: string };

export function HomeScreen(): React.JSX.Element {
  const {} = useGetSettings();
  const navigation = useAppNavigation();
  const insets = useSafeAreaInsets();
  const { setLocale } = useAppStore();

  //TODO: để tạm
  useEffect(() => {
    setLocale(LOCALES.vi);
  }, []);

  // Render service item
  const renderServiceItem = (item: ServiceType, index: number) => {
    const icon = typeof item.icon === 'string' ? { uri: item.icon } : item.icon;
    return (
      <TouchableOpacity
        onPress={() => navigation.navigate(item.key)}
        key={item.key}
        style={[styles.serviceItem, { marginLeft: !index ? 0 : 8 }]}
        activeOpacity={0.7}
      >
        <BlockView style={styles.serviceIconWrap}>
          <IconImage
            source={icon}
            style={styles.serviceIcon}
          />
        </BlockView>
        <CText
          style={styles.serviceName}
          numberOfLines={2}
          ellipsizeMode="tail"
        >
          {item.name}
        </CText>
        {item.description && (
          <CText
            style={[styles.serviceName, { color: '#FF8228' }]}
            numberOfLines={2}
            ellipsizeMode="tail"
          >
            {`(${item.description})`}
          </CText>
        )}
      </TouchableOpacity>
    );
  };

  return (
    <BlockView style={styles.safeArea}>
      <StatusBar
        barStyle="dark-content"
        backgroundColor="white"
      />
      <ScrollView
        style={[styles.container, { paddingTop: insets.top }]}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        <BlockView
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            paddingHorizontal: 16,
          }}
        >
          <CText style={styles.greeting}>Xin chào</CText>
          <TouchableOpacity onPress={() => navigation.navigate('Auth')}>
            <IconImage
              source={icMessage}
              style={{ width: 24, height: 24, marginLeft: 4 }}
            />
          </TouchableOpacity>
        </BlockView>
        <Carousel
          loop={true}
          width={SCREEN_WIDTH}
          height={CAROUSEL_HEIGHT}
          snapEnabled={true}
          pagingEnabled={true}
          autoPlayInterval={2000}
          mode="parallax"
          modeConfig={{
            parallaxScrollingScale: 0.92,
            parallaxScrollingOffset: 40,
          }}
          data={carouselImages}
          onSnapToItem={(index) => console.log('current index:', index)}
          renderItem={({
            item,
            index,
          }: {
            item: ImageSourcePropType;
            index: number;
          }) => (
            <Image
              key={index}
              source={item}
              style={styles.carouselImage}
              resizeMode="cover"
            />
          )}
        />

        <BlockView style={styles.serviceGridWrap}>
          <BlockView style={styles.serviceRow}>
            {services.slice(0, 4).map(renderServiceItem)}
          </BlockView>
          <BlockView style={styles.serviceRow}>
            {services.slice(4, 8).map(renderServiceItem)}
          </BlockView>
        </BlockView>

        {/* bRewards */}
        <BlockView style={styles.rewardsSection}>
          <BlockView style={styles.rewardsTitleRow}>
            <CText style={styles.rewardsTitle}>bRewards</CText>
            <TouchableOpacity style={styles.viewMoreButton}>
              <IconImage
                source={icArrowRight}
                style={{ width: 18, height: 18 }}
              />
            </TouchableOpacity>
          </BlockView>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            snapToInterval={REWARD_CARD_WIDTH + 20}
            pagingEnabled={true}
            style={styles.rewardsScroll}
            contentContainerStyle={styles.rewardsRow}
          >
            {REWARD.map((idx) => (
              <BlockView
                key={idx?.id}
                style={styles.rewardImage}
              >
                <Image
                  source={idx?.image}
                  resizeMode="cover"
                  style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    width: '100%',
                    height: '100%',
                  }}
                />
                <BlockView
                  style={{
                    paddingLeft: 6,
                    paddingRight: 12,
                    paddingBottom: 6,
                  }}
                >
                  <CText style={styles.rewardVoucherFigma}>{idx?.name}</CText>
                  <BlockView style={styles.rewardPointsFigma}>
                    <CText style={styles.rewardPointsTextFigma}>
                      {idx?.points}
                    </CText>
                  </BlockView>
                </BlockView>
              </BlockView>
            ))}
          </ScrollView>
        </BlockView>
      </ScrollView>
    </BlockView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: Colors.WHITE,
  },
  container: {
    flex: 1,
    backgroundColor: Colors.WHITE,
  },
  contentContainer: {
    paddingTop: 0,
    paddingBottom: 200,
  },
  // Greeting/Header
  greeting: {
    fontSize: 18,
    fontWeight: '700',
    color: '#061232',
    fontFamily: 'Montserrat-Bold',
    textAlign: 'left',
    marginTop: 16,
    marginBottom: 18,
    letterSpacing: -0.5,
    flex: 1,
  },
  carouselImage: {
    width: SCREEN_WIDTH,
    height: CAROUSEL_HEIGHT,
    borderRadius: CARD_RADIUS,
  },
  // Service Grid
  serviceGridWrap: {
    marginBottom: 12,
    width: '100%',
    paddingHorizontal: 16,
  },
  serviceRow: {
    flexDirection: 'row',
    marginBottom: 24,
    width: '100%',
  },
  serviceItem: {
    alignItems: 'center',
    justifyContent: 'flex-start',
    flex: 1,
    padding: 4,
  },
  serviceIconWrap: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 4,
  },
  serviceIcon: {
    width: 56,
    height: 56,
  },
  serviceName: {
    fontSize: 12,
    color: '#383838',
    fontFamily: 'Montserrat-SemiBold',
    textAlign: 'center',
    lineHeight: 14,
  },
  // bRewards
  rewardsSection: {
    marginBottom: 24,
  },
  rewardsTitleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  rewardsTitle: {
    fontSize: 16,
    color: '#383838',
    fontFamily: 'Montserrat-Bold',
    marginTop: 0,
    textAlign: 'left',
    fontWeight: '700',
  },
  viewMoreButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  viewMoreText: {
    fontSize: 14,
    color: '#4CAF50',
    fontWeight: '500',
  },
  rewardsScroll: {},
  rewardsRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12,
    paddingHorizontal: 16,
    paddingLeft: 16,
  },
  rewardImage: {
    width: REWARD_CARD_WIDTH,
    height: REWARD_CARD_HEIGHT,
    borderRadius: 8,
    overflow: 'hidden',
    justifyContent: 'flex-end',
  },
  rewardGradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: REWARD_CARD_HEIGHT / 3,
    bottom: 0,
  },
  rewardVoucherFigma: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFF',
    marginBottom: 4,
    fontFamily: 'Montserrat-Bold',
    lineHeight: 20,
  },
  rewardPointsFigma: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  rewardPointsIconFigma: {
    fontSize: 14,
    marginRight: 4,
    color: '#FFB300',
    fontFamily: 'Montserrat-Bold',
  },
  rewardPointsTextFigma: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#FF8327',
    fontFamily: 'Montserrat-Bold',
  },
  dotPagination: {
    width: 12,
    height: 4,
    borderRadius: Spacing.SPACE_20,
    backgroundColor: Colors.PRIMARY_COLOR,
    margin: 0,
  },
  containerPagination: {
    paddingTop: Spacing.SPACE_16,
    paddingBottom: 0,
  },
  dotInactionPagination: {
    width: 8,
    height: 8,
    borderRadius: Spacing.SPACE_16,
    backgroundColor: Colors.GREY,
    margin: 0,
  },
  dotContainer: {
    marginHorizontal: 2,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    width: '80%',
    maxWidth: 400,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
    color: '#383838',
  },
  languageOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  selectedLanguage: {
    backgroundColor: '#F2F2F4',
  },
  languageFlag: {
    width: 32,
    height: 32,
    marginRight: 12,
  },
  languageName: {
    fontSize: 16,
    color: '#383838',
    fontFamily: 'Montserrat-Regular',
  },
});
