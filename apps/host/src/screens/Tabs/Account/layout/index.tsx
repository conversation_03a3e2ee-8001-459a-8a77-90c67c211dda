import React from 'react';
import { Button, StyleSheet } from 'react-native';
import { useAuth } from '@btaskee/auth-store';
import { BlockView, CText } from '@btaskee/design-system';

interface AccountScreenProps {
  navigation: any;
}

export const AccountScreen = ({ navigation }: AccountScreenProps) => {
  const { logout, token } = useAuth();

  return (
    <BlockView
      flex
      center
      style={styles.container}
    >
      <CText>AccountScreen</CText>
      {token && (
        <>
          <CText>{token}</CText>
          <Button
            title="Logout"
            onPress={logout}
          />
        </>
      )}
      {!token && (
        <Button
          title="Login"
          onPress={() => navigation.navigate('Auth')}
        />
      )}
    </BlockView>
  );
};

const styles = StyleSheet.create({
  container: {},
});
