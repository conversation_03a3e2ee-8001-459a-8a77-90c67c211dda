import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';

import { AuthScreen } from '@screens/Auth';
import { AirConditionerScreen } from '@screens/Services/AirConditioner';
import { ChildCareScreen } from '@screens/Services/ChildCare';
import { CleaningScreen } from '@screens/Services/Cleaning';
import { CleaningSubscriptionScreen } from '@screens/Services/CleaningSubscription';
import { DeepCleaningScreen } from '@screens/Services/DeepCleaning';
import { ElderlyCareScreen } from '@screens/Services/ElderlyCare';
import { PatientCareScreen } from '@screens/Services/PatientCare';
import { VoiceChatScreen } from '@screens/VoiceChat';

import { RouteName } from './RouteName';
import { TabsNavigator } from './TabNavigator';
import { MainStackParamList } from './type';

const Main = createNativeStackNavigator<MainStackParamList>();

export const MainNavigator = () => {
  return (
    <Main.Navigator screenOptions={{ headerShown: false }}>
      <Main.Screen
        name={RouteName.TabNavigator}
        component={TabsNavigator}
      />
      <Main.Screen
        name={RouteName.CleaningSubscriptionService}
        component={CleaningSubscriptionScreen}
      />
      <Main.Screen
        name={RouteName.DeepCleaningService}
        component={DeepCleaningScreen}
      />
      <Main.Screen
        name={RouteName.CleaningService}
        component={CleaningScreen}
      />
      <Main.Screen
        name={RouteName.AirConditionerService}
        component={AirConditionerScreen}
      />
      <Main.Screen
        name={RouteName.ChildCareService}
        component={ChildCareScreen}
      />
      <Main.Screen
        name={RouteName.ElderlyCareService}
        component={ElderlyCareScreen}
      />
      <Main.Screen
        name={RouteName.PatientCareService}
        component={PatientCareScreen}
      />
      <Main.Screen
        name={RouteName.VoiceChat}
        component={VoiceChatScreen}
      />
      <Main.Screen
        name={RouteName.Auth}
        component={AuthScreen}
      />
    </Main.Navigator>
  );
};
