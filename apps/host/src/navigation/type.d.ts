import { NativeStackNavigationProp } from '@react-navigation/native-stack';

import { RouteName } from './RouteName';

type RootStackParamList = {
  [RouteName.IntroNavigator]: any;
  [RouteName.MainNavigator]: any;
};

export type IntroStackParamList = {
  [RouteName.AppIntro]: any;
  [RouteName.Landing]: any;
};

type TabsStackParamList = {
  [RouteName.TabHome]: any;
  [RouteName.TabActivity]: any;
  [RouteName.TabCommunity]: any;
  [RouteName.TabNotification]: any;
  [RouteName.TabAccount]: any;
};

type MainStackParamList = {
  [RouteName.TabNavigator]: any;
  [RouteName.CleaningService]: any;
  [RouteName.DeepCleaningService]: any;
  [RouteName.AirConditionerService]: any;
  [RouteName.ElderlyCareService]: any;
  [RouteName.PatientCareService]: any;
  [RouteName.ChildCareService]: any;
  [RouteName.VoiceChat]: any;
  [RouteName.Auth]: any;
  [RouteName.CleaningSubscriptionService]: any;
};

export type ParamsNavigationList = RootStackParamList &
  IntroStackParamList &
  MainStackParamList;

export type NavigationProps = NativeStackNavigationProp<ParamsNavigationList>;
