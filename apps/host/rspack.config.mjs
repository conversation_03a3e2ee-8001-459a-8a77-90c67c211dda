import pkg from '@btaskee/sdk';
import * as Repack from '@callstack/repack';
import { ReanimatedPlugin } from '@callstack/repack-plugin-reanimated';
import rspack from '@rspack/core';
import path from 'node:path';
import { fileURLToPath } from 'node:url';

const { getLocalIP, getSharedDependencies } = pkg;

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Rspack configuration enhanced with Re.Pack defaults for React Native.
 *
 * Learn about Rspack configuration: https://rspack.dev/config/
 * Learn about Re.Pack configuration: https://re-pack.dev/docs/guides/configuration
 */
export default (env) => {
  const { mode, platform = process.env.PLATFORM } = env;
  const hostIP = getLocalIP();

  return {
    mode,
    context: __dirname,
    entry: './index.js',
    experiments: {
      incremental: mode === 'development',
    },
    resolve: {
      ...Repack.getResolveOptions(),
      alias: {
        '@navigation': path.resolve(__dirname, './src/navigation'),
        '@screens': path.resolve(__dirname, './src/screens'),
        '@app': path.resolve(__dirname, './src/App.tsx'),
        '@hooks': path.resolve(__dirname, './src/hooks'),
        '@images': path.resolve(__dirname, './src/assets/images'),
        '@lottie': path.resolve(__dirname, './src/assets/lottie'),
        '@components': path.resolve(__dirname, './src/components'),
        '@utils': path.resolve(__dirname, './src/utils'),
        '@types': path.resolve(__dirname, './src/types'),
      },
    },
    output: {
      uniqueName: 'sas-host',
      path: path.resolve(__dirname, 'dist', platform),
    },
    module: {
      rules: [
        ...Repack.getJsTransformRules(),
        ...Repack.getAssetTransformRules(),
      ],
    },
    plugins: [
      new Repack.RepackPlugin(),
      new ReanimatedPlugin(),
      new Repack.plugins.ModuleFederationPluginV2({
        name: 'host',
        dts: false,
        remotes: {
          auth: `auth@http://${hostIP}:9001/${platform}/mf-manifest.json`,
          cleaning: `cleaning@http://${hostIP}:9002/${platform}/mf-manifest.json`,
          airConditioner: `airConditioner@http://${hostIP}:9005/${platform}/mf-manifest.json`,
          deepCleaning: `deepCleaning@http://${hostIP}:9006/${platform}/mf-manifest.json`,
          taskManagement: `taskManagement@http://${hostIP}:9011/${platform}/mf-manifest.json`,
          voiceChat: `voiceChat@http://${hostIP}:9004/${platform}/mf-manifest.json`,
          childCare: `childCare@http://${hostIP}:9008/${platform}/mf-manifest.json`,
          elderlyCare: `elderlyCare@http://${hostIP}:9007/${platform}/mf-manifest.json`,
          patientCare: `patientCare@http://${hostIP}:9012/${platform}/mf-manifest.json`,
          cleaningSubscription: `cleaningSubscription@http://${hostIP}:9010/${platform}/mf-manifest.json`,
        },
        shared: getSharedDependencies({ eager: true }),
      }),
      // silence missing @react-native-masked-view optionally required by @react-navigation/elements
      new rspack.IgnorePlugin({
        resourceRegExp: /^@react-native-masked-view/,
      }),
    ],
  };
};
