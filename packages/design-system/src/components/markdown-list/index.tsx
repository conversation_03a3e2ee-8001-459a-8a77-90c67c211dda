import React, { ReactNode } from 'react';
import { ViewStyle } from 'react-native';
import { MarkdownProps } from 'react-native-markdown-display';

import { Spacing } from '../../tokens';
import { BlockView } from '../block-view';
import { Markdown } from '../markdown';
import { styles } from './styles';

type MarkdownListProps = {
  data?: string[];
  containerStyle?: ViewStyle;
  markdownStyle?: MarkdownProps['style'];
  ItemSeparatorComponent?: ReactNode;
};

export const MarkdownList = ({ data, ...props }: MarkdownListProps) => {
  if (!data?.length) return null;

  return (
    <BlockView
      padding={{ horizontal: Spacing.SPACE_16 }}
      style={props?.containerStyle}
    >
      {data.map((item, index) => {
        const isLast = index >= data?.length - 1;
        return (
          <BlockView key={index?.toString()}>
            <Markdown
              text={item}
              paragraphStyle={styles.textCenter}
              {...props?.markdownStyle}
            />
            {!isLast && props?.ItemSeparatorComponent
              ? props?.ItemSeparatorComponent
              : null}
          </BlockView>
        );
      })}
    </BlockView>
  );
};
