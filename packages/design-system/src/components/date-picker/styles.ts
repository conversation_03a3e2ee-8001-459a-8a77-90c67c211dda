import { StyleSheet } from 'react-native';

import { <PERSON><PERSON><PERSON>elper } from '../../helpers/device.helper';
import { Colors, FontSizes, Shadows, Spacing } from '../../tokens';

const width = DeviceHelper.WINDOW.WIDTH;
const SIZE_ITEM = Math.round(width / 11);
// get list date
export const styles = StyleSheet.create({
  txtDateTime: {
    fontSize: FontSizes.SIZE_14,
    textTransform: 'capitalize',
  },
  panel: {
    color: Colors.BLACK,
    fontSize: Spacing.SPACE_20,
    marginLeft: Spacing.SPACE_16,
    marginVertical: Spacing.SPACE_24,
  },
  wrapper: {
    paddingHorizontal: Spacing.SPACE_16,
    paddingBottom: Spacing.SPACE_16,
  },
  textActive: {
    color: Colors.WHITE,
  },
  backgroundActive: {
    backgroundColor: Colors.PRIMARY_COLOR,
  },
  btnChooseDate: {
    paddingTop: Spacing.SPACE_08,
    marginRight: Spacing.SPACE_16,
  },
  txtWeekday: {
    color: Colors.GREY,
    width: SIZE_ITEM,
    fontSize: FontSizes.SIZE_14,
    textAlign: 'center',
  },
  txtDay: {
    color: Colors.BLACK,
    fontSize: FontSizes.SIZE_14,
    marginBottom: Spacing.SPACE_16,
  },
  wrapperItem: {
    paddingVertical: Spacing.SPACE_08,
    paddingHorizontal: Spacing.SPACE_04,
    borderRadius: 6,
    borderColor: Colors.BORDER_COLOR,
    borderWidth: 1,
    backgroundColor: Colors.WHITE,
    minWidth: 48,
  },
  today: {
    ...Shadows.SHADOW_1,
    shadowColor: Colors.RED,
    height: 10,
    width: 10,
    borderRadius: 5,
    backgroundColor: Colors.GREEN,
    position: 'absolute',
    top: -5,
    right: -5,
  },
  txtDate: {
    // textTransform: 'uppercase',
    color: Colors.GREY,
  },
  wrapperPanel: {
    paddingHorizontal: Spacing.SPACE_16,
    justifyContent: 'space-between',
  },
  disabled: {
    backgroundColor: Colors.BORDER_COLOR,
  },
});
