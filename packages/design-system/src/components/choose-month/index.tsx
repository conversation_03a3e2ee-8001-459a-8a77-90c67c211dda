import React, { useMemo } from 'react';
import { Dimensions, TouchableOpacity } from 'react-native';
import _, { isEmpty } from 'lodash';

import { getMonthListDefaultByService } from '../../helpers';
import { useI18n } from '../../hooks';
import { ColorsV2, FontSizes, Spacing } from '../../tokens';
import { IService } from '../../types';
import { SERVICES_SUBSCRIPTION } from '../../utils/constant';
import { BlockView } from '../block-view';
import { CText } from '../text';
import { styles } from './styles';

const { width } = Dimensions.get('window');
const WIDTH_ITEM = Math.round(width / 2 - Spacing.SPACE_20);

type DiscountByMonth = {
  month: number;
  discountPercentage: number;
};

interface ChooseMonthProps {
  month: number;
  onChange: (month: number) => void;
  disabled?: boolean;
  service: IService;
  discountByMonth?: DiscountByMonth[];
}

export function ChooseMonth({
  month,
  onChange,
  disabled,
  service,
  discountByMonth,
}: ChooseMonthProps) {
  const { t } = useI18n('common');

  const monthList = useMemo(() => {
    return getMonthListDefaultByService({
      monthlyOptions: service?.monthlyOptions || [],
      serviceName: service?.name as SERVICES_SUBSCRIPTION,
    });
  }, [service]);

  const _getDiscountByMonth = (txMonth: number) => {
    if (!discountByMonth) {
      return null;
    }
    const discount = _.find(discountByMonth, { month: txMonth });
    return discount && discount?.discountPercentage > 0
      ? discount.discountPercentage
      : null;
  };

  if (isEmpty(monthList)) return null;

  return (
    <BlockView style={styles.containerType}>
      <CText style={styles.txtPanel}>{t('SUBSCRIPTION_TYPE')}</CText>
      <BlockView style={styles.containerMonth}>
        {monthList?.map((e, index) => {
          const active = Boolean(month === e?.duration);
          const discount = _getDiscountByMonth(e?.duration);
          const monthText = t('SUBSCRIPTION_MONTH', { count: e?.duration });

          return (
            <TouchableOpacity
              key={index}
              disabled={disabled}
              testID={`moth${index}`}
              onPress={() => onChange(e?.duration)}
              style={{ width: WIDTH_ITEM }}
            >
              <BlockView
                jBetween
                row
                horizontal
                style={[styles.itemType, active && styles.borderActive]}
              >
                <CText
                  bold
                  style={[styles.txtType, active && styles.textActive]}
                >
                  {monthText}
                </CText>

                {discount ? (
                  <BlockView style={styles.boxDiscount}>
                    <CText
                      bold
                      size={FontSizes.SIZE_16}
                      color={ColorsV2.neutralWhite}
                    >
                      {`-${discount * 100}%`}
                    </CText>
                  </BlockView>
                ) : null}
              </BlockView>
            </TouchableOpacity>
          );
        })}
      </BlockView>
    </BlockView>
  );
}
