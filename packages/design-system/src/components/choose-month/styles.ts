import { StyleSheet } from 'react-native';

import { BorderRadius, ColorsV2, FontSizes, Spacing } from '../../tokens';

export const styles = StyleSheet.create({
  containerType: {},

  txtPanel: {
    fontWeight: 'bold',
    fontFamily: 'Montserrat-Bold',
    color: ColorsV2.neutral800,
    fontSize: FontSizes.SIZE_16,
    marginVertical: Spacing.SPACE_16,
  },
  containerMonth: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  itemType: {
    padding: Spacing.SPACE_20,
    borderRadius: BorderRadius.RADIUS_08,
    borderWidth: 1,
    borderColor: ColorsV2.neutral100,
    marginBottom: Spacing.SPACE_16,
    backgroundColor: ColorsV2.neutralWhite,
  },
  borderActive: {
    borderColor: ColorsV2.orange500,
    backgroundColor: ColorsV2.orange50,
  },
  txtType: {
    fontSize: FontSizes.SIZE_16,
  },
  textActive: {
    color: ColorsV2.orange500,
  },
  boxDiscount: {
    borderRadius: 18,
    paddingVertical: 2,
    paddingHorizontal: 8,
    backgroundColor: ColorsV2.orange500,
  },
});
