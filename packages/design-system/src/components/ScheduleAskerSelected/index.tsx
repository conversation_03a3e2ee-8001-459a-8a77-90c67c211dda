import React from 'react';
import { capitalize, isEmpty } from 'lodash-es';

import { DateTimeHelpers, ITimezone, TypeFormatDate } from '../../helpers';
import { useI18n } from '../../hooks';
import { Colors, FontSizes } from '../../tokens';
import { SERVICES } from '../../utils';
import { BlockView } from '../block-view';
import { ConditionView } from '../condition-view';
import { CText } from '../text';
import { TouchableOpacity } from '../touchable-opacity';
import { styles } from './styles';

interface ScheduleAskerSelectedProps {
  timezone: ITimezone;
  serviceName: SERVICES;
  scheduleSelected?: {
    date: Date;
  }[];
  duration: number;
  onChangeSchedule: () => void;
}

export const ScheduleAskerSelected = ({
  timezone,
  scheduleSelected,
  serviceName,
  onChangeSchedule,
  duration,
}: ScheduleAskerSelectedProps) => {
  const { t } = useI18n('common');

  if (isEmpty(scheduleSelected)) return null;

  return (
    <>
      <BlockView style={styles.container}>
        {scheduleSelected?.map((item, index) => {
          let hourTxt = capitalize(
            DateTimeHelpers.formatToString({
              timezone,
              date: item.date,
              typeFormat: TypeFormatDate.TimeHourMinute,
            }),
          );
          let hourTaskerArrive = capitalize(
            DateTimeHelpers.formatToString({
              timezone,
              date: DateTimeHelpers.toDateTz({
                timezone,
                date: item.date,
              }).subtract(duration, 'hour'),
              typeFormat: TypeFormatDate.TimeHourMinute,
            }),
          );
          const gmt = DateTimeHelpers.getGMTByCompareTzDefault(timezone);
          if (gmt) {
            hourTxt = `${hourTxt} ${gmt}`;
            hourTaskerArrive = `${hourTaskerArrive} ${gmt}`;
          }

          return (
            <BlockView
              key={index}
              style={index < scheduleSelected.length - 1 && styles.borderBottom}
            >
              <CText
                bold
                color={Colors.PRIMARY_COLOR}
                size={FontSizes.SIZE_16}
              >
                {t('FAV_TASKER.OPTION_TIME', { stt: index + 1 })}
              </CText>
              <BlockView
                style={styles.content}
                jBetween
                row
              >
                <CText color={Colors.GREY}>{t('COUNTDOWN_DAY')}</CText>
                <CText>
                  {capitalize(
                    DateTimeHelpers.formatToString({
                      timezone,
                      date: item.date,
                      typeFormat: TypeFormatDate.DateFullWithDay,
                    }),
                  )}
                </CText>
              </BlockView>
              <BlockView
                style={styles.content}
                jBetween
                row
              >
                <CText color={Colors.GREY}>{t('COUNTDOWN_HOUR')}</CText>
                <CText>{hourTxt}</CText>
              </BlockView>
              <ConditionView
                condition={serviceName === SERVICES.HOME_COOKING}
                viewTrue={
                  <BlockView
                    style={styles.content}
                    jBetween
                    row
                  >
                    <CText color={Colors.GREEN}>
                      {t('FAV_TASKER.TASKER_WILL_ARRIVE_AT_TIME', {
                        time: hourTaskerArrive,
                      })}
                    </CText>
                  </BlockView>
                }
              />
            </BlockView>
          );
        })}
      </BlockView>
      <TouchableOpacity
        onPress={onChangeSchedule}
        style={styles.btnChange}
      >
        <CText color={Colors.SECONDARY_COLOR}>
          {t('FAV_TASKER.CHANGE_OPTION_TIME')}
        </CText>
      </TouchableOpacity>
    </>
  );
};
