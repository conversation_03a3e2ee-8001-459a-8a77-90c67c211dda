/**
 * <AUTHOR>
 * @email [<EMAIL>]
 * @create date 2023-11-24 11:28:12
 * @modify date 2023-11-24 11:28:12
 * @desc Choose favourite tasker
 */
import React from 'react';

import { useI18n } from '../../hooks';
import { OPTIONAL_CHOOSE_FAV_TASKER } from '../../utils';
import { AlertHolder } from '../alert-holder';
import { OptionItemPostTask } from '../OptionItemPostTask';

interface ChooseFavouriteTaskerProps {
  blacklist?: any;
  isFavouriteTasker: boolean;
  setIsFavouriteTasker: (value: boolean) => void;
}

export const OptionalChooseFavouriteTasker = ({
  blacklist,
  isFavouriteTasker,
  setIsFavouriteTasker,
}: ChooseFavouriteTaskerProps) => {
  const { t } = useI18n('common');

  const showDescriptionForFavoriteTasker = () => {
    return AlertHolder.alert?.open?.({
      title: t('FAV_TASKER_TITLE'),
      message: [
        t('FAV_TASKER_DESCRIPTION_EXPLAIN_1'),
        t('FAV_TASKER_DESCRIPTION_EXPLAIN_2'),
      ],
      actions: [{ text: t('PT1_DETAIL_CHOOSE_MANUAL_UNDERSTOOD') }],
    });
  };

  const onValueChange = (checked: boolean) => {
    setIsFavouriteTasker(checked);
  };

  if (blacklist && blacklist.indexOf('FAV_TASKER') !== -1) {
    return null;
  }
  return (
    <OptionItemPostTask
      value={isFavouriteTasker}
      onValueChange={onValueChange}
      optionalName={OPTIONAL_CHOOSE_FAV_TASKER}
      showDescription={showDescriptionForFavoriteTasker}
    />
  );
};
