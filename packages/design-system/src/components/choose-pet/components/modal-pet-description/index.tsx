import React from 'react';
import { ScrollView } from 'react-native';

import { bgPet } from '../../../../assets';
import { DeviceHelper, formatMoney } from '../../../../helpers';
import { useI18n } from '../../../../hooks';
import { useSettingsStore } from '../../../../stores';
import { Spacing } from '../../../../tokens';
import { BlockView } from '../../../block-view';
import { ConditionView } from '../../../condition-view';
import { CustomMarkDown } from '../../../custom-markdown';
import { FastImage } from '../../../fast-image';
import { MarkdownList } from '../../../markdown-list';
import { CText } from '../../../text';
import styles from './styles';

interface IModalPetDescriptionProps {
  petOption?: any;
}

export const ModalPetDescription = ({
  petOption,
}: IModalPetDescriptionProps) => {
  const { t } = useI18n('common');
  const { settings } = useSettingsStore();

  return (
    <BlockView maxHeight={Math.round(DeviceHelper.WINDOW.HEIGHT / 1.5)}>
      <ScrollView scrollIndicatorInsets={{ right: 1 }}>
        <BlockView
          center
          margin={{ bottom: Spacing.SPACE_16 }}
        >
          <FastImage
            source={bgPet}
            style={styles.bgImg}
          />
        </BlockView>
        <BlockView margin={{ horizontal: Spacing.SPACE_16 }}>
          <ConditionView
            condition={Boolean(petOption?.cost)}
            viewTrue={
              <CustomMarkDown
                testID="txtPetDescriptionPet"
                text={t('POST_TASK_STEP_2.OPTION_PET_FEE')}
                params={[
                  {
                    key: 'price',
                    value: t('COST_AND_CURRENCY', {
                      cost: formatMoney(petOption?.cost),
                      currency: settings?.currency?.sign,
                    }),
                    style: styles.txtPrice,
                  },
                ]}
                containerStyles={styles.txtDescription}
              />
            }
          />
          <CText bold>{t('POST_TASK_STEP_2.SOME_RECOMMEND')}</CText>
          <MarkdownList
            data={[
              t('POST_TASK_STEP_2.RECOMMEND_1'),
              t('POST_TASK_STEP_2.RECOMMEND_2'),
            ]}
            containerStyle={styles.containerMarkdown}
            markdownStyle={{
              textStyle: styles.textStyle,
            }}
            ItemSeparatorComponent={<BlockView height={Spacing.SPACE_08} />}
          />
        </BlockView>
      </ScrollView>
    </BlockView>
  );
};
