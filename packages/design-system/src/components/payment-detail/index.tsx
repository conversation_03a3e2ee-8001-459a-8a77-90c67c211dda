/**
 * <AUTHOR>
 * @email [<EMAIL>]
 * @create date 2023-05-11 14:35:32
 * @modify date 2023-10-17 16:11:25
 * @desc [Detail payment]
 */

import React from 'react';

import { formatMoney } from '../../helpers';
import { useI18n } from '../../hooks';
import { useSettingsStore } from '../../stores';
import { BlockView } from '../block-view';
import { Card } from '../card';
import { ConditionView } from '../condition-view';
import { SizedBox } from '../sized-box';
import { CText } from '../text';
// import { Divider } from 'react-native-elements';
import styles from './styles';

const ItemDetail = ({
  title,
  content,
  customStyle,
}: {
  title: string;
  content: string;
  customStyle?: any;
}) => {
  return (
    <BlockView style={styles.group}>
      <CText style={styles.txtVLabel}>{title}</CText>
      <CText style={[styles.txtValue, customStyle]}>{content}</CText>
    </BlockView>
  );
};
export const PaymentDetail = ({ price }: { price: any }) => {
  const { t } = useI18n('common');
  const { currency } = useSettingsStore()?.settings;

  const _renderPromotion = () => {
    if (price?.cost > price?.finalCost) {
      const promotionPriceText = `-${t('COST_AND_CURRENCY', {
        cost: formatMoney(price?.cost - price?.finalCost),
        currency: currency?.sign,
      })}`;
      return (
        <ItemDetail
          title={t('FAV_TASKER.VOUCHER_DISCOUNT')}
          content={promotionPriceText}
        />
      );
    }
    return null;
  };

  return (
    <BlockView>
      <BlockView
        row
        style={styles.wrapPanel}
      >
        <CText
          bold
          style={styles.txtPanel}
        >
          {t('TITLE_PAYMENT_DETAIL')}
        </CText>
      </BlockView>
      <Card style={styles.wrapperPayment}>
        <ItemDetail
          title={t('BASE_COST_SERVICE')}
          content={t('COST_AND_CURRENCY', {
            cost: formatMoney(price?.cost - price?.vat),
            currency: currency?.sign,
          })}
        />
        {_renderPromotion()}
        <ConditionView
          condition={Boolean(price?.vat)}
          viewTrue={
            <ItemDetail
              title={t('VAT_COST')}
              content={t('COST_AND_CURRENCY', {
                cost: formatMoney(price?.vat),
                currency: currency?.sign,
              })}
            />
          }
        />

        {/* <Divider style={styles.dividerStyle} /> */}
        <SizedBox
          height={1}
          style={styles.dividerStyle}
        />
        <ItemDetail
          title={t('TOTAL_PAYMENT')}
          content={t('COST_AND_CURRENCY', {
            cost: formatMoney(price?.finalCost),
            currency: currency?.sign,
          })}
          customStyle={styles.txtSuccess}
        />
      </Card>
    </BlockView>
  );
};
