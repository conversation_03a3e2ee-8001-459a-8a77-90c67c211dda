import { StyleSheet } from 'react-native';

import { Colors, Spacing } from '../../tokens';

export default StyleSheet.create({
  group: {
    flexDirection: 'row',
    paddingVertical: 5,
    // alignItems: 'center',
  },
  txtVLabel: {
    width: '50%',
    paddingRight: 10,
    color: Colors.GREY,
  },
  txtValue: {
    width: '50%',
    textAlign: 'right',
  },
  wrapPanel: {
    marginTop: Spacing.SPACE_24,
    marginBottom: Spacing.SPACE_16,
  },
  txtPanel: {
    fontSize: 20,
  },
  wrapperPayment: {
    paddingHorizontal: Spacing.SPACE_16,
    paddingVertical: Spacing.SPACE_08,
  },
  dividerStyle: {
    marginVertical: Spacing.SPACE_04,
    backgroundColor: Colors.BORDER_COLOR,
  },
  txtSuccess: {
    color: Colors.GREEN,
  },
});
