import React from 'react';
import { StyleSheet, TouchableOpacity } from 'react-native';

import { Colors, Spacing } from '../../tokens';
import { BlockView } from '../block-view';
import { FastImage, FastImageComponentProps } from '../fast-image';
import { Icon } from '../icon';
import { CText } from '../text';

export interface FooterInfoItemProps {
  title: string;
  icon?: FastImageComponentProps['source'];
  onPress?: () => void;
}

export const FooterInfoItem = ({
  title,
  icon,
  onPress,
}: FooterInfoItemProps) => {
  return (
    <TouchableOpacity
      disabled={!onPress}
      onPress={onPress}
      style={styles.questionContainer}
    >
      {icon ? (
        <FastImage
          source={icon}
          style={styles.questionLeftIcon}
        />
      ) : null}
      <BlockView flex>
        <CText
          style={styles.questionTitle}
          bold
        >
          {title}
        </CText>
      </BlockView>

      {onPress ? (
        <Icon
          name={'icArrowRight'}
          color={Colors.GREEN}
          style={styles.questionRightIcon}
        />
      ) : null}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  questionContainer: {
    flexDirection: 'row',
    borderRadius: 8,
    backgroundColor: Colors.GREY_3,
    alignItems: 'center',
    paddingLeft: Spacing.SPACE_16,
    paddingRight: 22,
    paddingVertical: Spacing.SPACE_08,
    marginTop: Spacing.SPACE_16,
  },
  questionLeftIcon: {
    width: 32,
    height: 32,
    marginRight: Spacing.SPACE_16,
  },
  questionTitle: {
    lineHeight: 20,
  },
  questionRightIcon: {
    width: 26,
    height: 26,
    marginLeft: Spacing.SPACE_16,
  },
});
