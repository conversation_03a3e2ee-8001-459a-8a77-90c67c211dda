import React, { useCallback, useEffect, useRef } from 'react';
import { Animated, Easing, ViewStyle } from 'react-native';

import { icCheckPlus, IconAssets } from '../../assets';
import { Alert } from '../../helpers';
import { useI18n } from '../../hooks';
import { useAppStore } from '../../stores';
import { BorderRadius, ColorsV2, Spacing } from '../../tokens';
import { ISO_CODE } from '../../utils';
import { BlockView } from '../block-view';
import { IconImage } from '../icon-image';
import { CText } from '../text';
import { TouchableOpacity } from '../touchable-opacity';
import { ModalListTools } from './modal-list-tools';

/**
 * Props for the CleaningTools component
 */
interface CleaningToolsProps {
  isPremiumSelected?: boolean;
  isSupportPremium?: boolean;
  containerStyle?: ViewStyle;
}

/**
 * Component that displays cleaning tools selection button
 * Opens a modal with available cleaning tools when pressed
 */
export const CleaningTools: React.FC<CleaningToolsProps> = ({
  isPremiumSelected = false,
  isSupportPremium = false,
  containerStyle = {},
}) => {
  const { t } = useI18n('common');
  const { isoCode } = useAppStore();
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const prevText = useRef(isPremiumSelected);
  const containerAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    if (prevText.current !== isPremiumSelected) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 150,
          easing: Easing.bezier(0.4, 0.0, 0.2, 1),
          useNativeDriver: true,
        }),
        Animated.timing(containerAnim, {
          toValue: 0.995,
          duration: 150,
          easing: Easing.bezier(0.4, 0.0, 0.2, 1),
          useNativeDriver: true,
        }),
      ]).start(() => {
        prevText.current = isPremiumSelected;
        Animated.parallel([
          Animated.timing(fadeAnim, {
            toValue: 1,
            duration: 150,
            easing: Easing.bezier(0.4, 0.0, 0.2, 1),
            useNativeDriver: true,
          }),
          Animated.timing(containerAnim, {
            toValue: 1,
            duration: 150,
            easing: Easing.bezier(0.4, 0.0, 0.2, 1),
            useNativeDriver: true,
          }),
        ]).start();
      });
    }
  }, [isPremiumSelected]);

  // Handle opening the tools modal
  const handleShow = useCallback(() => {
    Alert?.alert?.open({
      title: t('POST_TASK_STEP_2.TITLE_MODAL_LIST_TOOLS'),
      message: (
        <ModalListTools
          isPremiumSelected={isPremiumSelected}
          isSupportPremium={isSupportPremium}
        />
      ),
      actions: [{ text: t('CLOSE') }],
    });
  }, [isPremiumSelected, isSupportPremium, t]);

  // Early return if not in Vietnam (currently only supported in VN)
  if (isoCode !== ISO_CODE.VN) {
    return null;
  }

  return (
    <TouchableOpacity
      testID="btnShowCleaningTools"
      onPress={handleShow}
      activeOpacity={0.7}
      style={containerStyle}
    >
      <Animated.View
        style={{
          transform: [{ scale: containerAnim }],
        }}
      >
        <BlockView
          row
          flex
          padding={Spacing.SPACE_16}
          margin={{ vertical: isSupportPremium ? Spacing.SPACE_16 : 0 }}
          backgroundColor={ColorsV2.neutral50}
          radius={BorderRadius.RADIUS_08}
        >
          <IconImage
            source={icCheckPlus}
            size={18}
            color={ColorsV2.green500}
          />
          <BlockView
            flex
            row
            horizontal
          >
            <Animated.View
              // eslint-disable-next-line react-native/no-inline-styles
              style={{
                flex: 1,
                opacity: fadeAnim,
                transform: [
                  {
                    translateY: fadeAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: [2, 0],
                    }),
                  },
                ],
              }}
            >
              <CText
                flex
                margin={{ horizontal: Spacing.SPACE_12 }}
              >
                {isPremiumSelected
                  ? t('POST_TASK_STEP_2.LIST_TOOL_TASK_PREMIUM')
                  : t('POST_TASK_STEP_2.LIST_TOOL_TASK_NORMAL')}
              </CText>
            </Animated.View>
            <IconImage
              source={IconAssets.icArrowDown}
              size={18}
              color={ColorsV2.neutral800}
            />
          </BlockView>
        </BlockView>
      </Animated.View>
    </TouchableOpacity>
  );
};
