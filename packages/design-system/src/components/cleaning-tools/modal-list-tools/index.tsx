import React, { useMemo } from 'react';
import { ScrollView } from 'react-native';

import {
  iconPremiumOptional,
  toolBroom,
  toolBucket,
  toolCif,
  toolCloth,
  toolDishWashingLiquid,
  toolFloorCleaner,
  toolGift,
  toolGlove,
  toolMop,
  toolReedGlass,
  toolToiletScrub,
  toolVacuum,
  toolVim,
  toolWashingDish,
} from '../../../assets';
import { DeviceHelper } from '../../../helpers';
import { useI18n } from '../../../hooks';
import { BorderRadius, ColorsV2, FontSizes, Spacing } from '../../../tokens';
import { BlockView } from '../../block-view';
import { ConditionView } from '../../condition-view';
import { FastImage } from '../../fast-image';
import { CText } from '../../text';
import styles from './styles';

export const CLEANING_TOOLS = [
  {
    name: 'vacuum',
    image: toolVacuum,
    isPremium: true,
  },
  {
    name: 'broom',
    image: toolBroom,
    isPremium: false,
  },
  {
    name: 'glove',
    image: toolGlove,
    isPremium: false,
  },
  {
    name: 'mop',
    image: toolMop,
    isPremium: false,
  },
  {
    name: 'cloth',
    image: toolCloth,
    isPremium: false,
  },
  {
    name: 'toiletScrub',
    image: toolToiletScrub,
    isPremium: false,
  },
  {
    name: 'washingDish',
    image: toolWashingDish,
    isPremium: false,
  },
  {
    name: 'dishWashingLiquid',
    image: toolDishWashingLiquid,
    isPremium: false,
  },
  {
    name: 'cif',
    image: toolCif,
    isPremium: false,
  },
  {
    name: 'vim',
    image: toolVim,
    isPremium: false,
  },
  {
    name: 'floorCleaner',
    image: toolFloorCleaner,
    isPremium: false,
  },
  {
    name: 'gift',
    image: toolGift,
    isPremium: false,
  },
  {
    name: 'bucket',
    image: toolBucket,
    isPremium: false,
  },
  {
    name: 'reed_glass',
    image: toolReedGlass,
    isPremium: false,
  },
];

/**
 * Props for the ModalListTools component
 */
interface ModalListToolsProps {
  isPremiumSelected?: boolean;
  isSupportPremium?: boolean;
}

/**
 * Component that displays a grid of cleaning tools in a modal
 * Shows premium tools based on premium selection status
 */
export const ModalListTools: React.FC<ModalListToolsProps> = ({
  isPremiumSelected = false,
  isSupportPremium = false,
}) => {
  const { t } = useI18n('common');

  // Process cleaning tools into rows of 3 items for grid display
  // Filter out premium tools if premium is not supported
  const listToolsConverted = useMemo(() => {
    const cleaningTools = isSupportPremium
      ? CLEANING_TOOLS
      : CLEANING_TOOLS.filter((item) => !item.isPremium);

    const rows = [];
    for (let i = 0; i < cleaningTools.length; i += 3) {
      rows.push(cleaningTools.slice(i, i + 3));
    }
    return rows;
  }, [isSupportPremium]);

  // Calculate maximum height for the scrollview based on device height
  const maxHeight = useMemo(
    () => Math.round(DeviceHelper.WINDOW.HEIGHT * 0.6),
    [],
  );

  return (
    <BlockView
      maxHeight={maxHeight}
      margin={{ top: Spacing.SPACE_16 }}
    >
      <ScrollView showsVerticalScrollIndicator={false}>
        <BlockView
          alignSelf="center"
          margin={{ top: Spacing.SPACE_16 }}
        >
          {listToolsConverted.map((row, rowIndex) => (
            <BlockView
              row
              key={`row${rowIndex}`}
            >
              {row.map((item) => {
                const isOnlyPremium = item.isPremium && !isPremiumSelected;
                const testID = isOnlyPremium
                  ? `disabled_tool_premium_${item?.name}`
                  : `enabled_tool_premium_${item?.name}`;

                return (
                  <BlockView
                    testID={testID}
                    key={item?.name}
                  >
                    <BlockView
                      margin={DeviceHelper.WINDOW.WIDTH * 0.01}
                      padding={DeviceHelper.WINDOW.WIDTH * 0.02}
                      radius={BorderRadius.RADIUS_08}
                      border={{
                        width: 1,
                        color: isOnlyPremium
                          ? ColorsV2.orange500
                          : ColorsV2.neutral100,
                      }}
                    >
                      <FastImage
                        source={item?.image}
                        style={styles.toolImage}
                      />
                    </BlockView>
                    <ConditionView
                      condition={isOnlyPremium}
                      viewTrue={
                        <BlockView
                          row
                          style={styles.wrapPremium}
                        >
                          <FastImage
                            source={iconPremiumOptional}
                            style={styles.icon}
                          />
                          <CText
                            size={FontSizes.SIZE_12}
                            color={ColorsV2.orange500}
                          >
                            {t('POST_TASK_STEP_2.PREMIUM')}
                          </CText>
                        </BlockView>
                      }
                    />
                  </BlockView>
                );
              })}
            </BlockView>
          ))}
        </BlockView>
      </ScrollView>
    </BlockView>
  );
};

export default React.memo(ModalListTools);
