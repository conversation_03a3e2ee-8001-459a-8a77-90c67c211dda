import React from 'react';

import { useI18n } from '../../hooks';
import { PAYMENT_METHOD } from '../../utils';
import { BlockView } from '../block-view';
import { CText } from '../text';
import styles from './styles';

type WarningTaskDateOptionsProps = {
  paymentMethod?: {
    value?: string;
  };
};

export const WarningTaskDateOptions = ({
  paymentMethod,
}: WarningTaskDateOptionsProps) => {
  const { t } = useI18n('common');
  let warning = t('FAV_TASKER.WARNING_PAYMENT_METHOD_PREPAID');

  switch (paymentMethod?.value) {
    case PAYMENT_METHOD.cash:
      warning = t('FAV_TASKER.WARNING_PAYMENT_METHOD_CASH');
      break;
    case PAYMENT_METHOD.credit:
      warning = t('FAV_TASKER.WARNING_PAYMENT_METHOD_CASH');
      break;

    case PAYMENT_METHOD.card:
      warning = t('FAV_TASKER.WARNING_PAYMENT_METHOD_CARD');
      break;
    case PAYMENT_METHOD.kredivo:
      warning = t('FAV_TASKER.WARNING_PAYMENT_METHOD_KREDIVO');
      break;
    default:
      break;
  }
  return (
    <BlockView style={styles.container}>
      <CText>{warning}</CText>
    </BlockView>
  );
};
