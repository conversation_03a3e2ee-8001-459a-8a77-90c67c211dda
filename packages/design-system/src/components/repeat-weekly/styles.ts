/**
 * Styles for the RepeatWeekly component
 */
import { StyleSheet } from 'react-native';

import { <PERSON>ceHelper } from '../../helpers/device.helper';
import { BorderRadius, ColorsV2, Spacing } from '../../tokens';

// Calculate the size of each day button based on screen width
export const SIZE_ITEM = Math.round(DeviceHelper.WINDOW.WIDTH / 9.5);

export const styles = StyleSheet.create({
  // Button styles
  btnInfo: {
    paddingLeft: 5,
    paddingRight: 10,
  },

  // Layout styles
  left: {
    alignItems: 'center',
  },
  wrap_weekly: {
    justifyContent: 'space-between',
  },

  // Text styles
  txtDay: {
    color: ColorsV2.neutral800,
    fontSize: 13,
  },

  // Container styles
  wrapper: {
    borderWidth: 1,
    borderColor: ColorsV2.neutral100,
    justifyContent: 'space-between',
    padding: Spacing.SPACE_12,
    borderRadius: BorderRadius.RADIUS_08,
    backgroundColor: ColorsV2.neutralWhite,
    marginTop: Spacing.SPACE_24,
    flexDirection: 'row',
  },

  // Active state styles
  textActive: {
    color: ColorsV2.neutralWhite,
  },
  backgroundActive: {
    backgroundColor: ColorsV2.orange500,
  },

  // Day button styles
  wrapperButton: {
    height: SIZE_ITEM,
    width: SIZE_ITEM,
    borderRadius: SIZE_ITEM / 2,
    overflow: 'hidden',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: ColorsV2.neutral100,
  },

  // Title text style
  txtWeekly: {
    color: ColorsV2.neutral800,
    fontSize: 14,
  },

  // Main container style
  container: {
    marginHorizontal: Spacing.SPACE_16,
    marginTop: Spacing.SPACE_24,
  },

  // Icon styles
  icCalendarSchedule: {
    width: 24,
    height: 24,
    marginRight: 10,
  },
});
