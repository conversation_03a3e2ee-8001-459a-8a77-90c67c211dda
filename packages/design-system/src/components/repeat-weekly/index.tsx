/**
 * RepeatWeekly Component
 *
 * A component that allows users to select days of the week for recurring tasks.
 * It displays a toggle switch and day selection buttons when enabled.
 */
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { TextStyle, ViewStyle } from 'react-native';
import Animated, {
  FadeInDown,
  FadeOutDown,
  useAnimatedStyle,
  useSharedValue,
  withSequence,
  withSpring,
  withTiming,
} from 'react-native-reanimated';

import { IconAssets } from '../../assets';
import {
  Al<PERSON>,
  AnimationHelpers,
  DateTimeHelpers,
  TypeFormatDate,
} from '../../helpers';
import { useI18n } from '../../hooks';
import { ColorsV2, HitSlop, Spacing } from '../../tokens';
import { BlockView } from '../block-view';
import { IconImage } from '../icon-image';
import { MarkdownList } from '../markdown-list';
import { SizedBox } from '../sized-box';
import { Switch } from '../switch';
import { CText } from '../text';
import { TouchableOpacity } from '../touchable-opacity';
import { styles } from './styles';

interface RepeatWeeklyProps {
  weeklyRepeater?: number[];
  isEnabled?: boolean;
  isHideWeekly?: boolean;
  disabled?: boolean;
  timezone?: string;
  title?: string;
  titleStyle?: TextStyle;
  contentStyle?: ViewStyle;
  style?: ViewStyle;
  message?: string[];
  onChange?: (days: number[]) => void;
  setEnabled?: (enabled: boolean) => Promise<void>;
}

// Define days of week as a constant outside the component
const DAYS_OF_WEEK = [0, 1, 2, 3, 4, 5, 6] as const; // by locale, Sunday is 0 and Saturday is 6

interface DayButtonProps {
  day: number;
  active: boolean;
  disabled?: boolean;
  timezone?: string;
  onPress: (day: number) => void;
}

const DayButton = React.memo(
  ({ day, active, disabled, timezone, onPress }: DayButtonProps) => {
    const scale = useSharedValue(1);

    const animatedStyle = useAnimatedStyle(() => ({
      transform: [{ scale: scale.value }],
    }));

    const handlePress = useCallback(() => {
      'worklet';
      // Animate based on the new state (opposite of current active state)
      const targetScale = active ? 1 : 0.9;
      scale.value = withSequence(
        withTiming(targetScale, {
          duration: 150,
        }),
        withSpring(1, {
          damping: 12,
          stiffness: 100,
          mass: 0.8,
        }),
      );
      onPress(day);
    }, [day, onPress, active]);

    return (
      <TouchableOpacity
        disabled={disabled}
        testID={`DayOfWeek${day}`}
        onPress={handlePress}
        hitSlop={HitSlop.MEDIUM}
      >
        <Animated.View style={animatedStyle}>
          <BlockView
            style={[
              styles.wrapperButton,
              active ? styles.backgroundActive : {},
            ]}
          >
            <CText
              bold
              style={[styles.txtDay, active ? styles.textActive : {}]}
            >
              {DateTimeHelpers.formatToString({
                timezone,
                date: DateTimeHelpers.toDayTz({
                  timezone,
                }).day(day),
                typeFormat: TypeFormatDate.DayAbbreviated,
              })}
            </CText>
          </BlockView>
        </Animated.View>
      </TouchableOpacity>
    );
  },
);

/**
 * RepeatWeekly component for selecting recurring days of the week
 */
export const RepeatWeekly: React.FC<RepeatWeeklyProps> = (props) => {
  const { t } = useI18n('common');
  const [selectedValue, setSelectedValue] = useState<number[]>(
    props?.weeklyRepeater || [],
  );
  const [isVisible, setIsVisible] = useState<boolean>(
    Boolean(props?.isEnabled),
  );

  /**
   * Handles day selection
   */
  const handleClick = useCallback(
    (day: number) => {
      setSelectedValue((prevSelected) => {
        const newSelected = [...prevSelected];
        const index = newSelected.indexOf(day);

        if (index > -1) {
          newSelected.splice(index, 1);
        } else {
          newSelected.push(day);
        }

        const sortedSelected = newSelected.sort();
        props?.onChange?.(sortedSelected);
        return sortedSelected;
      });
    },
    [props?.onChange],
  );

  /**
   * Shows an alert explaining weekly repeating
   */
  const showAlertQuestion = useCallback(() => {
    return Alert.alert.open({
      title: t('WEEKLY_REPEATER_IT_MEAN_TITLE'),
      message: <MarkdownList data={props?.message} />,
      actions: [
        {
          text: t('CANCEL'),
          style: 'cancel',
          onPress: async () => {
            await props?.setEnabled?.(false);
            setIsVisible(false);
          },
        },
        { text: t('OK') },
      ],
    });
  }, [t, props?.message, props.setEnabled]);

  /**
   * Shows an informational alert about weekly repeating
   */
  const showIntro = useCallback(() => {
    Alert.alert.open({
      title: t('WEEKLY_REPEATER_IT_MEAN_TITLE'),
      message: <MarkdownList data={props?.message} />,
      actions: [{ text: t('UNDERSTOOD') }],
    });
  }, [t, props?.message]);

  /**
   * Handles the switch toggle
   */
  const handleSwitchChange = useCallback(
    async (checked: boolean) => {
      await props?.setEnabled?.(checked);
      setIsVisible(checked);
      if (checked) {
        showAlertQuestion();
      }
    },
    [props?.setEnabled, showAlertQuestion],
  );

  /**
   * Renders the toggle switch
   */
  const renderSwitch = useMemo(() => {
    if (props?.isHideWeekly) {
      return null;
    }
    return (
      <Switch
        testID="cbWeeklyRepeater"
        value={isVisible}
        onValueChange={handleSwitchChange}
      />
    );
  }, [props?.isHideWeekly, isVisible, handleSwitchChange]);

  /**
   * Renders the day selection buttons
   */
  const shouldRenderDayOfWeek = useMemo(() => {
    if (!isVisible) {
      return null;
    }

    return (
      <Animated.View
        entering={FadeInDown}
        exiting={FadeOutDown}
        style={[styles.wrapper, props?.contentStyle]}
      >
        {DAYS_OF_WEEK.map((day) => (
          <DayButton
            key={day}
            day={day}
            active={selectedValue.includes(day)}
            disabled={props?.disabled}
            timezone={props?.timezone}
            onPress={handleClick}
          />
        ))}
      </Animated.View>
    );
  }, [
    isVisible,
    selectedValue,
    props?.timezone,
    props?.disabled,
    props?.contentStyle,
    handleClick,
  ]);

  /**
   * Renders the title and info button
   */
  const shouldRenderButtonShowInfo = useMemo(() => {
    return (
      <BlockView
        row
        style={styles.left}
      >
        <BlockView
          row
          horizontal
        >
          <IconImage source={IconAssets.iconCalendarSchedule} />
          <SizedBox width={Spacing.SPACE_08} />
          <CText
            style={[props?.titleStyle ? props?.titleStyle : styles.txtWeekly]}
          >
            {props?.title ? props?.title : t('POST_TASK_CHECKBOX_REPEAT')}
          </CText>
        </BlockView>

        {!props?.isHideWeekly && (
          <TouchableOpacity
            testID="whatIsWeekly"
            onPress={showIntro}
            style={styles.btnInfo}
            hitSlop={HitSlop.MEDIUM}
          >
            <IconImage
              source={IconAssets.icQuestion}
              size={16}
              color={ColorsV2.green500}
            />
          </TouchableOpacity>
        )}
      </BlockView>
    );
  }, [props?.isHideWeekly, props?.title, props?.titleStyle, showIntro, t]);

  return (
    <BlockView style={[styles.container, props?.style]}>
      <BlockView
        row
        style={styles.wrap_weekly}
      >
        {shouldRenderButtonShowInfo}
        {renderSwitch}
      </BlockView>
      {shouldRenderDayOfWeek}
    </BlockView>
  );
};
