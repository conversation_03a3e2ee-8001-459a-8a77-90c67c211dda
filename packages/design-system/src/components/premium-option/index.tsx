/**
 * PremiumOptional Component
 *
 * A component that allows users to toggle premium service options.
 * It displays a premium service option with a description modal that can be opened.
 */
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { TextStyle, ViewStyle } from 'react-native';
import { includes, isEmpty } from 'lodash-es';

import { IconAssets, iconPremiumOptional } from '../../assets';
import { AnimationHelpers } from '../../helpers';
import { useI18n } from '../../hooks';
import { ColorsV2 } from '../../tokens';
import { IAddress, IService } from '../../types';
import { BlockView } from '../block-view';
import { CModal, CModalHandle } from '../custom-modal';
import { FastImage } from '../fast-image';
import { IconImage } from '../icon-image';
import { Switch } from '../switch';
import { CText } from '../text';
import { TouchableOpacity } from '../touchable-opacity';
import { ModalDescriptionPremium } from './modal-description-premium';
import styles from './styles';

interface IPremiumProps {
  isPremium?: boolean;
  onChangePremium: (value: boolean) => void;
  service?: IService;
  address?: IAddress;
  txtPropStyle?: TextStyle;
  style?: ViewStyle;
}

/**
 * PremiumOptional component that displays a premium service option toggle
 * with a description modal that can be opened for more information.
 */
export const PremiumOptional: React.FC<IPremiumProps> = (props) => {
  const { t } = useI18n('common');

  const modalRef = useRef<CModalHandle>(null);
  const [isPremium, setIsPremium] = useState(Boolean(props.isPremium));

  /**
   * Update local state when props change
   */
  useEffect(() => {
    if (isPremium !== props?.isPremium) {
      setIsPremium(Boolean(props.isPremium));
    }
  }, [props.isPremium, isPremium]);

  /**
   * Hide premium description modal
   */
  const handleClose = useCallback(() => {
    modalRef?.current?.close?.();
  }, []);

  /**
   * Show premium description modal
   */
  const handleOpen = useCallback(() => {
    modalRef?.current?.open?.();
  }, []);

  /**
   * Handle premium toggle switch change
   */
  const onChangeSwitch = useCallback(
    (checked: boolean) => {
      AnimationHelpers.runLayoutAnimation();
      setIsPremium(checked);
      props.onChangePremium(checked);
      // Uncomment to automatically open modal when premium is selected
      // if (checked) {
      //   handleOpen();
      // }
    },
    [props],
  );

  // Get cities where premium option is available
  const applyForCities = props?.service?.premiumOptions?.applyForCities || [];

  // Don't render if premium is not available for the current city
  if (!includes(applyForCities, props?.address?.city)) {
    return null;
  }

  // Don't render if premium options are not configured or address is missing
  if (isEmpty(props?.service?.premiumOptions) || isEmpty(props?.address)) {
    return null;
  }

  return (
    <>
      <BlockView style={[styles.container, props?.style]}>
        <BlockView>
          <CText
            bold
            style={props.txtPropStyle}
          >
            {t('PREMIUM_TITLE_OPTIONAL')}
          </CText>
        </BlockView>
        <BlockView style={styles.content}>
          <BlockView
            row
            style={styles.group}
          >
            <BlockView
              row
              style={styles.left}
            >
              <BlockView>
                <FastImage
                  source={iconPremiumOptional}
                  style={styles.iconImage}
                />
              </BlockView>
              <CText
                testID="txtChoosePremiumOptional"
                bold
                style={styles.txtLabel}
              >
                {t('PREMIUM_CONTENT_OPTIONAL')}
              </CText>
              <TouchableOpacity
                testID="choosePremiumDescription"
                onPress={handleOpen}
                style={styles.btnInfo}
                hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
              >
                <IconImage
                  source={IconAssets.icQuestion}
                  size={16}
                  color={ColorsV2.green500}
                />
              </TouchableOpacity>
            </BlockView>
            <Switch
              testID="choosePremium"
              value={isPremium}
              onValueChange={onChangeSwitch}
            />
          </BlockView>
        </BlockView>
      </BlockView>
      <CModal
        ref={modalRef}
        hideButtonClose
        contentContainerStyle={styles.containerModal}
      >
        <ModalDescriptionPremium _handleClose={handleClose} />
      </CModal>
    </>
  );
};
