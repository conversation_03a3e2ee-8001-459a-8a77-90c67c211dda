import { StyleSheet } from 'react-native';

import { ColorsV2, FontSizes, Spacing } from '../../tokens';

export default StyleSheet.create({
  borderActive: {
    borderColor: ColorsV2.orange500,
  },
  textActive: {
    color: ColorsV2.orange500,
  },
  textDisabled: {
    color: ColorsV2.neutralDisable,
  },
  txtArea: {
    color: ColorsV2.neutral400,
  },
  txtDuration: {
    marginBottom: Spacing.SPACE_08,
    fontSize: FontSizes.SIZE_16,
  },
  wrapButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.SPACE_16,
  },
  leftContent: {
    flex: 1,
  },
});
