import React from 'react';
import {
  ActivityIndicator,
  TouchableWithoutFeedback,
  ViewStyle,
} from 'react-native';
import Animated, { FadeInDown, FadeOutDown } from 'react-native-reanimated';

import { formatMoney, getIncreasePiceTet } from '../../helpers';
import { useI18n } from '../../hooks';
import { useSettingsStore } from '../../stores';
import { ColorsV2, Spacing } from '../../tokens';
import { IPriceSub } from '../../types';
import { BlockView } from '../block-view';
import { CText } from '../text';
import { styles } from './styles';

interface PriceButtonSubscriptionProps {
  isLoading: boolean;
  price?: IPriceSub;
  onPress: () => void;
  nextButtonContainer?: ViewStyle;
  testID?: string;
  numberIncreasement?: number;
}

export const PriceButtonSubscription = ({
  isLoading,
  price,
  onPress,
  nextButtonContainer,
  testID,
  numberIncreasement,
}: PriceButtonSubscriptionProps) => {
  const { t: tCommon } = useI18n('common');
  const { t } = useI18n('cleaningSub');
  const { settings } = useSettingsStore();

  const shouldRenderPrice = React.useMemo(() => {
    if (!price) {
      return null;
    }
    // if data task not change, get price from task
    let priceText = '';
    let originPriceText = '';
    const disabledButton = Boolean(isLoading || (price && price?.error));

    const isChangeBackground = Boolean(price && price?.error);

    if (price?.finalCost) {
      priceText = `${tCommon('COST_AND_CURRENCY', {
        cost: formatMoney(price?.finalCost),
        currency: settings?.currency?.sign,
      })}`;
    }

    // with promotion
    if (
      price &&
      price?.cost &&
      price?.finalCost &&
      price?.cost > price?.finalCost
    ) {
      originPriceText = `${tCommon('COST_AND_CURRENCY', {
        cost: formatMoney(price?.cost),
        currency: settings?.currency?.sign,
      })}`;
    }

    const handleClick = () => {
      onPress && onPress();
    };

    const _renderIncreaseText = () => {
      const money = getIncreasePiceTet(price?.pricing);
      const increasePrice = `${tCommon('COST_AND_CURRENCY', {
        cost: formatMoney(money),
        currency: settings?.currency?.sign,
      })}`;
      const txtIncreasePrice =
        money > 0 ? t('SUBSCRIPTION_INCREASE_PRICE', { t: increasePrice }) : '';
      return (
        <BlockView
          flex
          center
          padding={{ left: Spacing.SPACE_04 }}
        >
          <CText
            bold
            style={{ color: ColorsV2.orange500 }}
          >
            {`${t('SUBSCRIPTION_PRICE_INCREASEMENT', {
              count: numberIncreasement,
            })} ${txtIncreasePrice}`}
          </CText>
        </BlockView>
      );
    };

    return (
      <Animated.View
        entering={FadeInDown}
        exiting={FadeOutDown}
      >
        <BlockView
          inset="bottom"
          style={[styles.container, nextButtonContainer]}
        >
          {/* {numberIncreasement && numberIncreasement > 0
            ? _renderIncreaseText()
            : null} */}

          <TouchableWithoutFeedback
            onPress={handleClick}
            disabled={disabledButton}
          >
            <BlockView
              row
              style={[styles.btn, isChangeBackground && styles.btnDisabled]}
            >
              <BlockView style={styles.left}>
                {isLoading ? (
                  <BlockView style={styles.loading}>
                    <ActivityIndicator color={ColorsV2.neutralWhite} />
                  </BlockView>
                ) : (
                  <BlockView
                    row
                    horizontal
                  >
                    <BlockView>
                      {originPriceText ? (
                        <CText
                          testID="lbOriginPrice"
                          style={styles.txtOriginPrice}
                        >
                          {originPriceText}
                        </CText>
                      ) : null}
                      <CText
                        testID="lbPrice"
                        bold
                        style={styles.txtPrice}
                      >
                        {priceText}
                      </CText>
                    </BlockView>
                    <BlockView style={styles.session}>
                      <CText
                        testID={'numberSessions'}
                        style={styles.txtSession}
                      >
                        {t('SESSION', { t: price?.session })}
                      </CText>
                    </BlockView>
                  </BlockView>
                )}
              </BlockView>
              <BlockView right>
                <CText
                  testID={testID}
                  style={styles.txtNext}
                >
                  {t('NEXT')}
                </CText>
              </BlockView>
            </BlockView>
          </TouchableWithoutFeedback>
        </BlockView>
      </Animated.View>
    );
  }, [price, isLoading]);

  return shouldRenderPrice;
};
