import { StyleSheet } from 'react-native';

import {
  BorderRadius,
  ColorsV2,
  FontSizes,
  Shadows,
  Spacing,
} from '../../tokens';

export const styles = StyleSheet.create({
  txtSession: {
    color: ColorsV2.neutralWhite,
  },
  session: {
    marginLeft: 10,
  },
  loading: {
    alignItems: 'flex-start',
    paddingLeft: '20%',
  },
  txtOriginPrice: {
    textDecorationLine: 'line-through',
    color: ColorsV2.neutralWhite,
  },
  txtNext: {
    color: ColorsV2.neutralWhite,
    fontSize: FontSizes.SIZE_16,
  },
  txtPrice: {
    color: ColorsV2.neutralWhite,
    fontSize: FontSizes.SIZE_16,
  },
  container: {
    position: 'absolute',
    left: 0,
    bottom: 0,
    right: 0,
    // borderTopLeftRadius: constant.BORDER_RADIUS,
    // borderTopRightRadius: constant.BORDER_RADIUS,
    backgroundColor: ColorsV2.neutralWhite,
    padding: Spacing.SPACE_16,
    ...Shadows.SHADOW_1,
  },
  btnDisabled: {
    backgroundColor: ColorsV2.neutralDisable,
    shadowColor: ColorsV2.neutralDisable,
  },
  left: {
    flex: 1,
    height: 22,
    justifyContent: 'center',
  },
  btn: {
    ...Shadows.SHADOW_1,
    shadowColor: ColorsV2.green500,
    backgroundColor: ColorsV2.green500,
    borderRadius: BorderRadius.RADIUS_08,
    padding: Spacing.SPACE_16,
    alignItems: 'center',
  },
});
