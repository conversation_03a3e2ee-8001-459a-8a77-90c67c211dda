import { StyleSheet } from 'react-native';

import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '../../helpers/device.helper';
import { BorderRadius, ColorsV2, Spacing } from '../../tokens';

const SIXTY_PERCENT = 0.6;
const MAX_HEIGHT_CALENDAR = Math.round(
  DeviceHelper.WINDOW.HEIGHT * SIXTY_PERCENT,
);

export const styles = StyleSheet.create({
  txtCheck: {
    color: ColorsV2.orange500,
  },
  wrap_note: {
    flexDirection: 'row',
    marginHorizontal: Spacing.SPACE_16,
    padding: Spacing.SPACE_12,
    borderColor: ColorsV2.neutral100,
    borderWidth: 1,
    borderRadius: BorderRadius.RADIUS_08,
  },
  wrap_icon: {
    marginRight: Spacing.SPACE_12,
    backgroundColor: ColorsV2.orange100,
    width: Spacing.SPACE_32,
    height: Spacing.SPACE_32,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: BorderRadius.RADIUS_FULL,
  },
  wrap_content: {
    flex: 1,
  },
  imageCheckBook: {
    width: 32,
    height: 32,
  },
  boxContent: {
    height: Spacing.SPACE_48,
    borderRadius: BorderRadius.RADIUS_08,
    paddingHorizontal: Spacing.SPACE_16,
    backgroundColor: ColorsV2.orange50,
  },
  calendarList: {
    maxHeight: MAX_HEIGHT_CALENDAR,
    padding: 0,
    margin: 0,
  },
  contentContainer: {
    paddingHorizontal: 0,
    paddingBottom: 0,
  },
});
