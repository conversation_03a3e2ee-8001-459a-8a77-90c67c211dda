/**
 * @Filename:
 * @Description:
 * @CreatedAt: 15/11/2020
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @UpdatedAt: 2/1/2021
 * @UpdatedBy: HongKhan<PERSON>, <PERSON><PERSON>
 **/

// import react-native
import React, { useMemo } from 'react';
import { CalendarList, DateData, LocaleConfig } from 'react-native-calendars';
import { get, isEmpty, last, sortBy } from 'lodash-es';

import { IconAssets } from '../../assets';
import {
  Alert,
  DateTimeHelpers,
  IDate,
  ITimezone,
  TypeFormatDate,
} from '../../helpers';
import { useI18n } from '../../hooks';
import { useAppStore } from '../../stores';
import { ColorsV2, FontSizes, Spacing } from '../../tokens';
import { IPriceSub, IService } from '../../types';
import { ISO_CODE } from '../../utils';
import { BlockView } from '../block-view';
import { FastImage } from '../fast-image';
import { PrimaryButton } from '../primary-button';
import { theme } from '../tet-booking-calendar/styles';
import { CText } from '../text';
import { styles } from './styles';

// let timeoutId: any = null;

type ScheduleComponentProps = {
  timezone: ITimezone;
  schedule?: IDate[];
  getPriceSubscription?: () => void;
  updateScheduleSubscription: (schedule: string[]) => void;
  weekDays?: number[];
  pricing?: IPriceSub['pricing'];
  service?: IService;
  setStartDate: (date: IDate) => void;
  isShowReviewSchedule?: boolean;
};

export const ChangeScheduleSub = (props: ScheduleComponentProps) => {
  const {
    schedule,
    getPriceSubscription,
    updateScheduleSubscription,
    pricing,
    service,
    setStartDate,
    timezone,
  } = props;

  const { t } = useI18n('cleaningSub');
  const { t: tCommon } = useI18n('common');
  const { locale, isoCode } = useAppStore();

  const [newSchedule, setNewSchedule] = React.useState(schedule);

  LocaleConfig.defaultLocale = locale;
  const minTask = service?.minTaskOfSubscription || 4;
  const minDayOfBooking = DateTimeHelpers.toDateTz({
    timezone,
    date: new Date(),
  }).add(3, 'day');

  const onChangeSchedule = (day: DateData) => {
    // save new schedule
    if (isEmpty(newSchedule)) {
      return;
    }
    const existsDay = newSchedule?.find((sche) => {
      return (
        DateTimeHelpers.formatToString({
          timezone,
          date: sche,
          typeFormat: TypeFormatDate.IsoDate,
        }) === day.dateString
      );
    });

    // New day exists => pull
    // Limit the number of jobs in subscription
    if (existsDay && newSchedule?.length && newSchedule?.length > minTask) {
      // Remove the choosen day
      const _tempSchedule = newSchedule?.filter((sche) => {
        return (
          DateTimeHelpers.formatToString({
            timezone,
            date: sche,
            typeFormat: TypeFormatDate.IsoDate,
          }) !== day.dateString
        );
      });

      return setNewSchedule(_tempSchedule);
    }
    // Check newDate in schedule and between startDate and endDate
    if (!existsDay) {
      // New day not exists => push and sort
      const oldDate = DateTimeHelpers.toDateTz({
        timezone,
        date: schedule?.[0],
      });
      // Combine newDate from choosen day and hour, minute from oldDate
      const newDate = DateTimeHelpers.toDateTz({
        timezone,
        date: day.dateString,
        format: TypeFormatDate.IsoDate,
      })
        .hour(oldDate.hour())
        .minute(oldDate.minute())
        .startOf('minute')
        .toDate();
      // Push and sort
      const _tempSchedule = sortBy([
        ...(Array.isArray(newSchedule) ? newSchedule : []),
        DateTimeHelpers.formatToString({ timezone, date: newDate }),
      ]);
      return setNewSchedule(_tempSchedule);
    }
  };

  const recalculatePrice = async () => {
    Alert.alert.close();
    if (newSchedule && newSchedule.length > 0) {
      setStartDate(
        DateTimeHelpers.formatToString({ timezone, date: newSchedule[0] }),
      );
    }

    updateScheduleSubscription(
      (newSchedule || []).map((e) =>
        DateTimeHelpers.formatToString({ timezone, date: e }),
      ),
    );
    getPriceSubscription && getPriceSubscription();
  };

  // Create a memoized map of dates that need special styling
  const specialDatesMap = useMemo(() => {
    const map = new Map<string, boolean>();
    if (pricing) {
      pricing.forEach((item) => {
        const date = DateTimeHelpers.formatToString({
          timezone,
          date: item.date,
          typeFormat: TypeFormatDate.IsoDate,
        });
        const increaseReasonArray = get(item, 'costDetail.increaseReasons', []);
        if (increaseReasonArray.length > 0) {
          const hasSpecialFee = increaseReasonArray.some(
            (reason) => reason.key === 'SPECIAL_TIME' && reason.value > 0,
          );
          map.set(date, hasSpecialFee);
        }
      });
    }
    return map;
  }, [pricing, timezone]);

  // Optimized markedDates calculation
  const markedDates = useMemo(() => {
    if (isEmpty(newSchedule)) {
      return {};
    }

    const dates: Record<string, any> = {};
    const scheduleLength = (newSchedule || []).length;

    // Pre-allocate the object size
    for (let i = 0; i < scheduleLength; i++) {
      const date = DateTimeHelpers.formatToString({
        timezone,
        date: (newSchedule || [])[i],
        typeFormat: TypeFormatDate.IsoDate,
      });

      // Use the pre-computed map instead of calling isIncrease
      dates[date] = specialDatesMap.get(date)
        ? {
            selected: true,
            selectedColor: ColorsV2.orange400,
            selectedTextColor: 'black',
          }
        : { selected: true };
    }

    return dates;
  }, [newSchedule, timezone, specialDatesMap]);

  // Memoize the note content to prevent unnecessary re-renders
  const noteContent = useMemo(
    () => (
      <BlockView style={styles.wrap_note}>
        <BlockView style={styles.wrap_icon}>
          <FastImage
            style={styles.imageCheckBook}
            resizeMode="cover"
            source={IconAssets.icCheckboxChecked}
          />
        </BlockView>
        <BlockView
          style={styles.wrap_content}
          vertical
        >
          <CText color={ColorsV2.neutral500}>
            - {t('SUBSCRIPTION_NOTE', { t: minTask })}
          </CText>
          {isoCode === ISO_CODE.TH ? null : (
            <CText color={ColorsV2.neutral500}>
              - {t('SUBSCRIPTION_PRICE_INCREASEMENT_HIGHLIGHT')}
            </CText>
          )}
        </BlockView>
      </BlockView>
    ),
    [t, minTask, isoCode],
  );

  return (
    <BlockView>
      {noteContent}
      <CalendarList
        testID="calendar"
        minDate={DateTimeHelpers.formatToString({
          timezone,
          date: minDayOfBooking,
          typeFormat: TypeFormatDate.IsoDate,
        })}
        style={styles.calendarList}
        pastScrollRange={Math.floor(
          DateTimeHelpers.diffDate({
            timezone,
            firstDate: last(schedule),
            secondDate: DateTimeHelpers.formatToString({
              timezone,
              date: minDayOfBooking,
              typeFormat: TypeFormatDate.StartOfYear,
            }),
            unit: 'year',
          }),
        )}
        futureScrollRange={Math.floor(
          DateTimeHelpers.diffDate({
            timezone,
            firstDate: last(schedule),
            secondDate: DateTimeHelpers.formatToString({
              timezone,
              date: minDayOfBooking,
              typeFormat: TypeFormatDate.StartOfMonth,
            }),
            unit: 'month',
          }),
        )}
        scrollEnabled={true}
        showScrollIndicator={false}
        markedDates={markedDates}
        onDayPress={onChangeSchedule}
        theme={{
          ...theme,
          textMonthFontSize: FontSizes.SIZE_16,
        }}
        current={DateTimeHelpers.formatToString({
          timezone,
          date: minDayOfBooking,
          typeFormat: TypeFormatDate.IsoDate,
        })}
      />
      <PrimaryButton
        title={tCommon('OK')}
        onPress={recalculatePrice}
        style={{
          marginTop: Spacing.SPACE_16,
          marginHorizontal: Spacing.SPACE_16,
        }}
      />
    </BlockView>
  );
};
