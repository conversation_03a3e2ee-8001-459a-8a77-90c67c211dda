import { StyleSheet } from 'react-native';

import { fontFamilySetup } from '@component/custom-text';
import constant from '@constant';
import { <PERSON><PERSON>Helper } from '@helper/device.helper';

export default StyleSheet.create({
  container: {
    padding: constant.PADDING_CONTENT,
  },
  imageStyle: {
    width: DeviceHelper.WINDOW.WIDTH / 3,
    height: DeviceHelper.WINDOW.WIDTH / 3,
  },
  contentTxt: {
    color: constant.COLOR.black,
    textAlign: 'center',
  },
  txtBtn: {
    fontWeight: 'bold',
    fontFamily: fontFamilySetup.montserratBold,
    fontSize: constant.FONT_SIZE.medium,
  },
  btn: {
    padding: constant.MARGIN.tiny,
  },
  wrapTime: {
    paddingVertical: constant.MARGIN.tiny,
    paddingHorizontal: constant.MARGIN.medium,
    borderRadius: constant.BORDER_RADIUS_MEDIUM,
    backgroundColor: constant.COLOR.lightGrey2,
    marginBottom: constant.MARGIN.medium2,
    alignItems: 'center',
    marginHorizontal: constant.MARGIN.small2,
  },
  containerTime: {
    marginTop: constant.MARGIN.medium,
    flexWrap: 'wrap',
  },
  scheduleContainer: {
    // backgroundColor: 'red',
    paddingHorizontal: constant.MARGIN.medium,
    borderRadius: constant.BORDER_RADIUS,
    borderWidth: 1,
    borderColor: constant.BORDER_COLOR,
  },
  timeActive: {
    backgroundColor: constant.SECONDARY_COLOR,
  },
  wrapSchedule: {
    marginTop: constant.MARGIN.small,
  },
  borderBottom: {
    borderBottomColor: constant.BORDER_COLOR,
    borderBottomWidth: 1,
  },
  icon: {
    width: 20,
    height: 20,
  },
  header: {
    backgroundColor: constant.COLOR.grey3,
    marginBottom: constant.MARGIN.large,
    borderRadius: constant.BORDER_RADIUS,
    justifyContent: 'space-between',
  },

  wrapIcon: {
    width: 45,
    height: 45,
  },

  containerScheduleSelected: {
    backgroundColor: constant.COLOR.lightGrey2,
    padding: constant.MARGIN.medium,
    marginTop: constant.MARGIN.medium,
    borderRadius: constant.BORDER_RADIUS,
  },
  iconCloseContainer: {
    backgroundColor: constant.COLOR.grey,
    borderRadius: constant.BORDER_RADIUS_MEDIUM,
    marginLeft: constant.MARGIN.medium,
  },
  wrapTaskTime: {
    alignItems: 'center',
    marginTop: constant.MARGIN.tiny,
    // width: '50%',
  },
  txtTaskTime: {
    marginLeft: constant.MARGIN.tiny,
  },
});
