{"INTRO_PATIENT_CARE_NAME": "Caring for the sick", "INTRO_PATIENT_CARE": "Help customers take care of their loved ones with the best conditions and let them work with peace of mind, by providing customers with attentive, experienced and sincere bTaskers. The customer's relatives are fully supported with medical care, while living in the comfort and familiarity of their own home. Living together in the love of family and children will also help the elderly boost energy level and lead healthy lifestyle. Loving and caring for the elderly is a wonderful tradition honoring the Vietnamese filial piety. Let b<PERSON><PERSON>ee take care and shower love to your loved ones on your behalf.", "INTRO_PATIENT_CARE_1": "Loving and caring for one's loved ones has always been a great tradition which displays the filial piety of the Vietnamese. Let b<PERSON><PERSON>ee take good care and shower love to your loved ones on your behalf.", "INTRO_PATIENT_CARE_2": "After leaving contact information and the need to use the service, b<PERSON><PERSON><PERSON> will quickly contact and respond to you within 60 minutes (during office hours)", "INTRO_PATIENT_CARE_THE_WORK_PERFORM": "Terms and Conditions", "INTRO_PATIENT_CARE_THE_WORK_PERFORM_1": "Look after and take care of the sick.", "INTRO_PATIENT_CARE_THE_WORK_PERFORM_2": "Feed the patient.", "INTRO_PATIENT_CARE_THE_WORK_PERFORM_3": "Assist patient with personal hygiene (oral hygiene, hair washing, bathing support)", "INTRO_PATIENT_CARE_THE_WORK_PERFORM_4": "Position and lift the patient.", "INTRO_PATIENT_CARE_THE_WORK_PERFORM_5": "Help patients in moving for medical examination.", "INTRO_PATIENT_CARE_THE_WORK_PERFORM_6": "Assist the patient with elimination.", "INTRO_PATIENT_CARE_THE_WORK_PERFORM_7": "Empty the patient's potty contents.", "INTRO_PATIENT_CARE_THE_WORK_PERFORM_8": "Sanitize the patient's waste containers.", "INTRO_PATIENT_CARE_THE_WORK_PERFORM_9": "Support, supervise the elderly or patients in taking prescription drugs", "INTRO_PATIENT_CARE_THE_WORK_PERFORM_10": "Massage and pat the painful area.", "INTRO_PATIENT_CARE_THE_WORK_PERFORM_11": "Keep watch of vital signs (heart, pulse, blood pressure, temperature)", "INTRO_PATIENT_CARE_THE_WORK_PERFORM_12": "Notify the patient's condition to family, doctors when necessary.", "INTRO_PATIENT_CARE_THE_WORK_PERFORM_13": "Support administrative procedures at the hospital.", "INTRO_PATIENT_CARE_NOT_PERFORM": "Tasks that won't be performed", "INTRO_PATIENT_CARE_NOT_PERFORM_1": "Injections, drip-feed, stitches removing, bandage changing, wound cleaning, colonic/enema for the elderly and invasive procedures.", "INTRO_PATIENT_CARE_NOT_PERFORM_2": "Do not perform any examination on areas of the body such as urinary tract condition, genital hygiene check.", "INTRO_PATIENT_CARE_NOT_PERFORM_3": "Place urinary catheter, gastric tube, acupressure, acupuncture.", "INTRO_PATIENT_CARE_NOT_PERFORM_4": "Perform autogenic drainage to restore respiratory function for the elderly.", "INTRO_PATIENT_CARE_QA": "What tasks will bTaskers perform?", "INTRO_PATIENT_CARE_QA_1": "Tasks that bTaskers don't perform", "POST_TASK_PATIENT_CARE_STEP_3_CHOOSE_DATE_NOTE": "Maximum {{t}} hours/day", "POST_TASK_PATIENT_CARE_STEP_3_CHOOSE_DATE": "<PERSON><PERSON><PERSON><PERSON> sesi", "POST_TASK_PATIENT_CARE_STEP_3_CHOOSE_DATE_1": "<PERSON> siang hari", "POST_TASK_PATIENT_CARE_STEP_4_IN_HOUSE": "Caring for the sick", "TITLE_SERVICE_TASK_DAY": "<PERSON><PERSON><PERSON>/<PERSON>", "CONTENT_CHOOSE_TASK_DAY1": "Pelanggan dapat fleksibel untuk melakukan pemesanan berdasar<PERSON> sesi, dengan cepat dan lebih nyaman.", "CONTENT_CHOOSE_TASK_DAY2": "Membantu pelanggan secara fleksibel memilih sesi mana yang akan dipesan, dengan cepat dan mudah.", "CONTENT_CHOOSE_SUBSCRIPTION1": "<PERSON><PERSON> pelanggan dengan Tasker tetap.", "CONTENT_CHOOSE_SUBSCRIPTION2": "<PERSON><PERSON><PERSON><PERSON> jadwal berdasarkan ketersediaan pelanggan. <PERSON><PERSON> waktu pem<PERSON>, hindari pembayaran berganda.", "TITLE_SERVICE_SUBSCRIPTION": "<PERSON><PERSON><PERSON> dengan <PERSON>"}