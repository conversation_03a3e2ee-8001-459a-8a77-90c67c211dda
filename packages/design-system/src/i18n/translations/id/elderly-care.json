{"INTRO_ELDERLY_CARE_NAME": "Merawat orang tua di rumah", "INTRO_ELDERLY_CARE": "Bantu pelanggan merawat orang yang mereka cintai dengan kondisi terbaik dan biarkan mereka bekerja dengan tenang, bersa<PERSON> dengan Tasker yang be<PERSON>, perhatian dan tulus kepada pelanggan. Dimana pelanggan didukung oleh perawatan medis, sambil mendapatkan kenyamanan dan keakraban di rumah mereka sendiri. Hidup bersama saling mencintai keluarga dan anak-anak akan membantu meningkatkan energi lansia serta menjalani gaya hidup sehat. Mencintai dan merawat orang tua adalah tradisi yang luar biasa untuk menghormati bakti orang Vietnam. Biarkan bTaskee merawat dan menyampaikan kasih sayang kepada orang yang Anda cintai atas nama Anda.", "INTRO_ELDERLY_CARE_1": "<PERSON><PERSON><PERSON> Anda memberikan informasi dan kebutuhan pada layanan, b<PERSON><PERSON><PERSON> akan segera menghubungi - men<PERSON><PERSON><PERSON> Anda dalam waktu 60 menit (saat jam kerja)", "TITLE_SERVICE_TASK_DAY": "<PERSON><PERSON><PERSON>/<PERSON>", "CONTENT_CHOOSE_TASK_DAY1": "Pelanggan dapat fleksibel untuk melakukan pemesanan berdasar<PERSON> sesi, dengan cepat dan lebih nyaman.", "CONTENT_CHOOSE_TASK_DAY2": "Membantu pelanggan secara fleksibel memilih sesi mana yang akan dipesan, dengan cepat dan mudah.", "CONTENT_CHOOSE_SUBSCRIPTION1": "<PERSON><PERSON> pelanggan dengan Tasker tetap.", "CONTENT_CHOOSE_SUBSCRIPTION2": "<PERSON><PERSON><PERSON><PERSON> jadwal berdasarkan ketersediaan pelanggan. <PERSON><PERSON> waktu pem<PERSON>, hindari pembayaran berganda.", "TITLE_SERVICE_SUBSCRIPTION": "<PERSON><PERSON><PERSON> dengan <PERSON>", "POST_TASK_ELDERLY_CARE_STEP_3_CHOOSE_DATE_NOTE": "<PERSON><PERSON><PERSON><PERSON> (t) jam/hari", "POST_TASK_ELDERLY_CARE_STEP_3_CHOOSE_DATE": "<PERSON><PERSON><PERSON><PERSON> sesi", "POST_TASK_ELDERLY_CARE_STEP_3_CHOOSE_DATE_1": "<PERSON> siang hari", "INTRO_ELDERLY_CARE_THE_WORK_PERFORM": "Tugas yang harus dikerjakan", "INTRO_ELDERLY_CARE_THE_WORK_PERFORM_1": "Perawatan terbaik untuk lansia oleh Tasker yang terlatih.", "INTRO_ELDERLY_CARE_THE_WORK_PERFORM_2": "Merawat orang tua", "INTRO_ELDERLY_CARE_THE_WORK_PERFORM_3": "Memberi makan orang tua.", "INTRO_ELDERLY_CARE_THE_WORK_PERFORM_4": "Melakukan perawatan kebersihan lansia (keb<PERSON><PERSON><PERSON> mulut, membantunya keramas, mandi).", "INTRO_ELDERLY_CARE_THE_WORK_PERFORM_5": "<PERSON>tu angkat dan berbalik saat lansia tidak bisa bergerak", "INTRO_ELDERLY_CARE_THE_WORK_PERFORM_6": "Kosongkan pispot dan kotoran lansia saat mereka tidak bisa ke toilet sendiri.", "INTRO_ELDERLY_CARE_THE_WORK_PERFORM_7": "<PERSON><PERSON>, te<PERSON>an da<PERSON>, nadi", "INTRO_ELDERLY_CARE_THE_WORK_PERFORM_8": "<PERSON>tu dan awasi lansia atau pasien yang mengonsumsi obat resep dari dokter.", "INTRO_ELDERLY_CARE_THE_WORK_PERFORM_9": "<PERSON><PERSON>t dan tepuk-tepuk bagian yang nyeri.", "INTRO_ELDERLY_CARE_THE_WORK_PERFORM_10": "<PERSON><PERSON> tahu kondisi kepada petugas medis, kera<PERSON>, do<PERSON>er, dan hubungi bantuan jika diperlukan.", "INTRO_ELDERLY_CARE_NOT_PERFORM": "<PERSON><PERSON><PERSON> yang tidak akan di<PERSON>an", "INTRO_ELDERLY_CARE_NOT_PERFORM_1": "<PERSON><PERSON><PERSON>, in<PERSON>s, lepas jahitan, pengg<PERSON><PERSON> perban, pem<PERSON><PERSON>an luka, kolon/enema untuk lansia dan prosedur invasif.", "INTRO_ELDERLY_CARE_NOT_PERFORM_2": "Jangan melakukan pemeriksaan pada area tubuh seperti saluran kemih, periksa kebersihan genital.", "INTRO_ELDERLY_CARE_NOT_PERFORM_3": "Tempatkan kateter urin, tabung lambung, akupresur, akupunktur.", "INTRO_ELDERLY_CARE_NOT_PERFORM_4": "Lakukan drainase autogenik untuk mengembalikan fungsi pernapasan bagi lansia.", "INTRO_ELDERLY_CARE_QA": "Tugas apa yang akan dilakukan oleh Tasker?", "INTRO_ELDERLY_CARE_QA_1": "<PERSON><PERSON><PERSON><PERSON><PERSON> yang tidak perlu dilakukan oleh bTasker", "POST_TASK_ELDERLY_CARE_STEP_4_IN_HOUSE": "Merawat orang tua di rumah"}