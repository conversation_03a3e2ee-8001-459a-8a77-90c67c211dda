{"INTRO_PATIENT_CARE_NAME": "Penjagaan pesakit di hospital", "INTRO_PATIENT_CARE": "Membantu pelanggan merawat orang tersayang dengan keadaan terbaik sambil dapat bekerja dengan tenang, dengan menghantar kakitangan berp<PERSON>, ju<PERSON><PERSON>, dan peka. Orang tersayang pelanggan akan mendapat sokongan dalam penjagaan perubatan sambil hidup dalam keselesaan, di r<PERSON>h mereka, bersa<PERSON> dengan kehangatan, cinta dari keluarga dan cucu yang akan membantu mereka merasa lebih baik dan hidup dengan lebih sihat. Mengasihi dan menjaga orang tua adalah tradisi yang hebat yang menunjukkan bakti orang Vietnam. Biarkan bTaskee menjaga dan menunjukkan kasih sayang kepada orang tersayang anda.", "INTRO_PATIENT_CARE_1": "Menyayangi dan menjaga orang tersayang adalah satu tradisi yang hebat yang menunjukkan nilai murni masyarakat Vietnam. Biarkan bTaskee membantu anda menjaga dan menunjukkan kasih sayang kepada orang tersayang.", "INTRO_PATIENT_CARE_2": "<PERSON><PERSON><PERSON> men<PERSON>kan maklumat kontak dan k<PERSON><PERSON><PERSON> per<PERSON>, b<PERSON><PERSON><PERSON> akan menghubungi dan memberi maklum balas kepada anda dalam masa 60 minit (dalam waktu pejabat)", "INTRO_PATIENT_CARE_THE_WORK_PERFORM": "<PERSON><PERSON><PERSON> yang akan di<PERSON>n", "INTRO_PATIENT_CARE_THE_WORK_PERFORM_1": "<PERSON><PERSON><PERSON> dan menjaga pesakit.", "INTRO_PATIENT_CARE_THE_WORK_PERFORM_2": "<PERSON>i makan kepada pesakit.", "INTRO_PATIENT_CARE_THE_WORK_PERFORM_3": "<PERSON><PERSON><PERSON> keb<PERSON>an pesakit (pen<PERSON><PERSON><PERSON> mulut, men<PERSON><PERSON> rambut, membantu mandi pesakit)", "INTRO_PATIENT_CARE_THE_WORK_PERFORM_4": "Membant<PERSON> pesakit bergerak.", "INTRO_PATIENT_CARE_THE_WORK_PERFORM_5": "Mengangkut pesakit untuk pemeriksaan perubatan.", "INTRO_PATIENT_CARE_THE_WORK_PERFORM_6": "<PERSON><PERSON><PERSON><PERSON> pesakit dalam proses pembuangan.", "INTRO_PATIENT_CARE_THE_WORK_PERFORM_7": "<PERSON><PERSON><PERSON> najis dan sisa pesakit.", "INTRO_PATIENT_CARE_THE_WORK_PERFORM_8": "<PERSON><PERSON><PERSON> keb<PERSON>ihan alat pengumpul sisa pesakit.", "INTRO_PATIENT_CARE_THE_WORK_PERFORM_9": "Membantu dan memantau pesakit atau warga emas mengambil ubat mengikut preskripsi.", "INTRO_PATIENT_CARE_THE_WORK_PERFORM_10": "<PERSON><PERSON><PERSON><PERSON> kawasan yang sakit, dan menggeleng.", "INTRO_PATIENT_CARE_THE_WORK_PERFORM_11": "<PERSON><PERSON><PERSON><PERSON> tanda-tanda vital (j<PERSON><PERSON>, nadi, tekanan darah, suhu)", "INTRO_PATIENT_CARE_THE_WORK_PERFORM_12": "<PERSON><PERSON><PERSON> keadaan pesakit kepada keluarga dan doktor apabila perlu.", "INTRO_PATIENT_CARE_THE_WORK_PERFORM_13": "Membantu prosedur administratif di hospital.", "INTRO_PATIENT_CARE_NOT_PERFORM": "<PERSON><PERSON><PERSON> yang tidak akan di<PERSON>n", "INTRO_PATIENT_CARE_NOT_PERFORM_1": "<PERSON><PERSON><PERSON> u<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> pem<PERSON>, men<PERSON><PERSON> luka, mencabut tiub kolon untuk orang tua dan prosedur pembedahan yang invasif.", "INTRO_PATIENT_CARE_NOT_PERFORM_2": "Tidak melakukan pemeri<PERSON>, pem<PERSON><PERSON><PERSON> kawasan di badan seperti keadaan saluran kencing, memer<PERSON>sa keadaan kebersihan alat genital.", "INTRO_PATIENT_CARE_NOT_PERFORM_3": "Memasukkan tiub k<PERSON>, tiub perut, aku<PERSON>nk<PERSON>, bekam.", "INTRO_PATIENT_CARE_NOT_PERFORM_4": "Mencuci kahak untuk pemulihan fungsi pernafasan bagi orang tua.", "INTRO_PATIENT_CARE_QA": "Apa kerja yang akan dilakukan oleh staf bTaskee?", "INTRO_PATIENT_CARE_QA_1": "<PERSON><PERSON><PERSON> yang tidak akan di<PERSON>ukan oleh staf bTaskee", "POST_TASK_PATIENT_CARE_STEP_3_CHOOSE_DATE_NOTE": "<PERSON><PERSON> kerja maksimum {{t}} jam/hari", "POST_TASK_PATIENT_CARE_STEP_3_CHOOSE_DATE": "Mengi<PERSON><PERSON> sesi", "POST_TASK_PATIENT_CARE_STEP_3_CHOOSE_DATE_1": "<PERSON><PERSON><PERSON><PERSON> hari", "POST_TASK_PATIENT_CARE_STEP_4_IN_HOUSE": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>t", "TITLE_SERVICE_TASK_DAY": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CONTENT_CHOOSE_TASK_DAY1": "Membolehkan pelanggan membuat tempahan dengan cepat dan mudah, mengikut keperluan waktu.", "CONTENT_CHOOSE_TASK_DAY2": "Pelanggan boleh mendaftar tugas dalam hanya 60 saat. <PERSON><PERSON>han fleksibel 4 jam atau 8 jam.", "CONTENT_CHOOSE_SUBSCRIPTION1": "Sokongan Tasker tetap berkualiti tinggi untuk pelanggan.", "CONTENT_CHOOSE_SUBSCRIPTION2": "Penjadualan kerja yang tetap dan fleksibel mengikut masa anda. Menjimatkan masa pendaftaran tugas dan mengelakkan pembayaran berulang.", "TITLE_SERVICE_SUBSCRIPTION": "<PERSON>kh<PERSON><PERSON><PERSON>"}