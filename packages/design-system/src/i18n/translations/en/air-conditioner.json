{"AC_INTRO_TITLE": "A/C Cleaning service", "AC_INTRO_CONTENT": "Cleaning your A/C regulary will help keep the air clean, reduce the cost of electricity and improve the life span of your A/C. We recommend cleaning your A/C every 3-4 months for best result.", "AC_INTRO_PRO_TITLE": "Profesional", "AC_INTRO_PRO_CONTENT": "Our exprienced technician are carefully selected. They will follow the bTaskee standard process to ensure your A/C is in good condition after the service is done.", "AC_INTRO_SAFE_TITLE": "Warranty policy", "AC_INTRO_SAFE_CONTENT": "If your A/C has any problem with in 7 days after the service is done, we will come back and fix it for free.", "AC_INTRO_READY_TITLE": "Always ready", "AC_INTRO_READY_CONTENT": "Book it when you need it, our team is always ready to serve you", "AC_INTRO_PRICE_TITLE": "No hidden fees", "AC_INTRO_PRICE_CONTENT": "Only pay the amount shown in the app. You will see the total cost before confirming your booking.", "QUANTITY_GAS_REFILL": "The number of devices needing gas pumping", "PRICE_REFILL": "+{{cost}}{{currency}} each device", "GAS_REFILL": "Devices need gas pumping", "APARTMENT_TITLE": "Surcharges for services performed at the apartment", "APARTMENT_CONTENT_1": "Our Partners needs to complete declaration and registration procedures with the Apartment Management Board before performing the Task. The registration process will take approximately 30 minutes, with an surcharge of **{{currency}}{{fee}}** for this item.", "APARTMENT_CONTENT_2": "To avoid affecting the progress of the task, please provide information about the type of house so that our Partners can better assist you.", "APARTMENT_NOTE": "If the information about the type of house is incorrect, our Partners may charge an additional surcharge.", "WATTAGE": "Type", "POST_TASK_AC_CHOOSE_TYPE": "Choose type A/C Cleaning", "AC_NOTE_BUILD_IN": "1 grille  (only service filter, duct and heat exchanger)", "AC_PROCESS_TITLE": "bTaskee standard procedure", "AC_PROCESS_ITEM": "Steps to clean the A/C", "AC_PROCESS_CONTENT": "Note: for build-in bulkhead A/C, we only clean the filter, air duct and the heat coils", "AC_PROCESS_STEP_1": "**Step 1**: Turn on the A/C to check the condition", "AC_PROCESS_STEP_2": "**Step 2**: Clean the filter and the cover.\nTurn off the power; Remove the cover and filter; Use water compressor to spay off the dust; Clean and disinfect the filter and cover using safe cleaning product; Use tower to dry them off.", "AC_PROCESS_STEP_3": "**Step 3**: Clean the indoor unit.\nCover the electrical board; Cover the unit with professional cleaning bag to make sure water do not come out; Use water compressor to wash off all the dust on the metal blades inside the unit; Remove the bag and dispose the water.", "AC_PROCESS_STEP_4": "**Step 4**: Clean the outdoor unit.\nSafely turn off the power at the circuit breaker; use water compressor to wash the heat coils and the surrounding area.", "AC_PROCESS_STEP_5": "**Step 5**: Refill the refigerant (optional).", "AC_PROCESS_STEP_6": "**Step 6**: Final check.\nClean the filter and cover using dry tower; Reassemble them accordingly; Turn on the power and the A/C, wait and check for cool air.", "POST_TASK_AC_HEADER_TITLE": "A/C cleaning", "SV_AC_SCR1_DETAIL_CAPACITY_LESS_THAN": "Less than {{t1}} {{t2}}", "SV_AC_SCR1_DETAIL_CAPACITY_GREATER_THAN": "{{t1}} {{t2}} and more", "SV_AC_SCR1_DETAIL_ITEM_AMOUNT": "Quantity", "SV_AC_SCR2_DATE_TIME_POPUP_NOTE_PLACE_HOLDER": "ex : \n- the unit on 1st floor is not cooling, might need more gas. ", "SV_AC_SCR2_POPUP_MAXIUM_GAS_TITLE": "Messege from bTaskee", "SV_AC_SCR2_POPUP_MAXIUM_GAS_REFILL": "Filling gas to the maximum of {{maxPSI}} PSI", "SV_AC_SCR2_POPUP_MAXIUM_GAS_CLOSE": "Close", "AIR_CONDITIONER_NOTE_GAS_REFILL": "Gas refill service - 30 PSI maximum"}