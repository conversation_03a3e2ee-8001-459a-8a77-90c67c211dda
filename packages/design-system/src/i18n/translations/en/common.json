{"DIALOG_TITLE_INFORMATION": "Information", "TASK_SAME_TIME_MESSAGE": "You already book one cleaner at this time. Are you sure you want to book one more cleaner?", "CLOSE": "Close", "OK": "OK", "CONFIRM": "Confirm", "BTN_BACK": "Back", "PT1_MAP_POPUP_HOME_TYPE_ITEM_APARTMENT": "Apt/Condo", "PT1_MAP_POPUP_OFFICE_TYPE_ITEM": "Details", "PT1_MAP_POPUP_HOME_TYPE_ITEM_VILLA": "Villa", "PT1_MAP_POPUP_HOME_TYPE_HOME_DESCRIPTION_TITLE": "House number", "SV_DC_SCR1_MAP_POPUP_VILLA_DESCRIPTION_HOLDER": "House number 1", "CHOSE_HOUSE_TYPE_APARTMENT_PLACE_HOLDER": "block A, 1st floor, room 102", "LIST_OF_LOCATIONS": "List of places", "SERVICE_GUARANTEED": "The service is guaranteed for 7 days.", "COST_AND_CURRENCY": "{{cost}} {{currency}}", "INCLUDE_FEE_SHIP": "Shipping included", "LAUNDRY_EMERGENCY_FEE": "{{t1}} {{t2}} (same day drop-off fee)", "OPTIONAL": "Options", "NEXT": "Next", "UPDATE": "Update", "WORK_TIME": "Working time", "WORK_TIME_TITLE": "Choose working time", "LAST_POST_TASK_TITLE": "Recent Task", "CHOOSE_DATE": "Date", "CHOOSE_TIME": "Time", "SERVICE_REQUEST_GPS": "Please turn on GPS during the booking!", "OPEN_SETTINGS": "Open Settings", "HOUSE_TYPE_TITLE": "House type", "COMPANY_TYPE_TITLE": "Type", "LABEL_CANCELED": "Cancelled", "NOT_YET_PAYMENT": "Not paid yet", "LABEL_CONFIRMED": "Confirmed", "LABEL_DONE": "Done", "LABEL_EXPIRED": "Expired", "LABEL_POSTED": "Posted", "LABEL_WAITING_ASKER_CONFIRMATION": "Awaiting your confirmation", "LABEL_WAITING_ASKER_CONFIRMATION_TASK_LEADER": "Waiting Taskers to accept", "LABEL_NOTE_FOR_TASKER": "Notes for Tasker", "SKIP": "<PERSON><PERSON>", "TASK_NOTE_HINT_TEXT": "Ex :\n- Clean 2 bedrooms, 1 living room, kitchen ..\n- Be careful when cleaning hardwood floor", "CONTINUE": "Continue", "TITLE_CHECKBOX_TASK_NOTE": "Use for next booking", "TASK_NOTE_DESCRIPTION": "This note will help Task<PERSON> do a better job", "POST_TASK_STEP_4_TITLE": "Confirm and pay", "TITLE_NO_PROMOTION": "No Promotion at this time", "MONEY_IN_ACCOUNT": "Balance", "MONEY_IN_ACCOUNT_1": "Main account balance", "MONEY_IN_ACCOUNT_BUSINESS": "Business account", "FAS_PAY_PURCHASE_ORDER": "Pay monthly package", "FAS_REFUND": "Refund", "FAS_CANCEL_TASK": "Cancel Task", "FAS_TOP_UP_CREDIT": "Top up bPay", "TITLE_TRANSACTION_HISTORY": "Transaction history", "TITLE_RECENT_TRANSACTION": "Recent transactions", "BUTTON_BOOKING": "Book", "TOTAL": "Total", "SERVICE_NOTE_CONTENT": "If you have any further requests, please enter them here", "SELECTED_DEVICE": "The selected device", "AMOUNT": "Quantity", "CAPACITY_LESS_THAN": "Less than {{t1}} {{t2}}", "CAPACITY_GREATER_THAN": "{{t1}} {{t2}} and more", "SUPPLY_DEMAND_COST_INCREASE": "Supply and demand cost increase", "UNDERSTOOD": "Understood", "TASK_INFO": "Task Info", "TASK_DETAIL": "Task details", "WOKING_DAY": "Date", "WORK_IN": "Duration", "TIME_TO_WORK": "Working time", "HK_SV_ADDRESS_DETAIL": "Address details", "CONTACT_INFO_MODAL_TITLE": "Contact", "PHONE_NUMBER": "Phone number", "CONTACT_NAME": "Contact name", "LOCATION": "Location", "WORK_IN_TIME_FROM_A_TO_B": "{{t1}} hours, from {{t2}} to {{t3}}", "HK_SV_ADDRESS_PHONE_NUMBER": "Phone number: {{t}}", "HK_SV_LOCATION_EMPTY_TITLE": "List of locations is empty", "HK_SV_LOCATION_EMPTY_CONTENT": "Please add a new location so b<PERSON><PERSON><PERSON> can arrive at the correct working address.", "ERROR_SETTING": "Sorry, an error occurred!", "ERROR_SETTING_RELOAD": "Please try reloading the app.", "ERROR_SETTING_BUTTON_RELOAD": "Reload the page", "OVERVIEW": "Overview", "PT2_CONFIRM_HEADER_TITLE": "Confirm and pay", "MODAL_POST_TASK_SUCCESS_TITLE": "Your task is successfully posted.", "MODAL_POST_TASK_SUCCESS_CONTENT": "You have successfully posted the Task. You can check for details on the Activity menu.", "MODAL_POST_TASK_SUCCESS_BTN_FOLLOW_TASK": "Task tracking", "MODAL_POST_TASK_SUCCESS_BTN_GO_HOME": "Back to homepage", "PAYMENT_METHOD": "Payment method", "TAB_PROMOTION": "Promotion", "CT_VIET_NAM": "Vietnam", "CT_THAI_LAND": "Thailand", "CT_INDONESIA": "Indonesia", "CT_MALAYSIA": "Malaysia", "LANGUAGE": "Language", "PT1_DETAIL_OPTION_ITEM_CHOOSE_MANUAL": "Manually choose Tasker", "PT1_DETAIL_OPTION_ITEM_FAV_TASKER": "Prioritize favorite Taskers", "PT1_DETAIL_OPTION_HAVE_PET": "House with pets", "CHOOSE_GENDER": "<PERSON><PERSON> Tasker Gender", "FAV_TASKER_TITLE": "What is 'Prioritize favorite Taskers' ?", "FAV_TASKER_DESCRIPTION_EXPLAIN_1": "This is the default function when you have a list of favorite Taskers.", "FAV_TASKER_DESCRIPTION_EXPLAIN_2": "Your favorite Taskers will be prioritized. If a favorite Tasker is busy or not taking the task, the system will send your task to Taskers nearby.", "PT1_DETAIL_CHOOSE_MANUAL_UNDERSTOOD": "I understood", "CHOOSE_DURATION_TITLE": "Duration", "NUMBER_OF_HOURS": "{{t}} hours", "ERROR_TRY_AGAIN": "There was a error. Please try again", "PAYMENT_METHOD_CARD": "Visa/Master Card", "PAYMENT_METHOD_BANK_TRANSFER": "ATM / Internet banking", "PAYMENT_METHOD_CREDIT": "b<PERSON>ay", "PAYMENT_METHOD_MOMO": "<PERSON><PERSON>", "PAYMENT_METHOD_DIRECT_TRANSFER": "Bank transfer", "PAYMENT_METHOD_DIRECT_CASH": "Cash", "PAYMENT_METHOD_ZALO_PAY": "Zalopay", "PAYMENT_METHOD_VIRTUAL_ACCOUNT": "Virtual Account", "HIDE_PREMIUM_TOOLS_DETAIL": "Show less", "SEE_MORE": "See more", "PT1_DETAIL_OPTION_TITLE": "Options", "PT1_MAP_POPUP_ADD_PET_SUBMIT": "Ok", "OTHER": "Others", "POST_TASK_STEP_2": {"SOME_RECOMMEND": "Some suggestions for you:", "RECOMMEND_1": "- Some Taskers are allergic to pet hair and cannot perform the task. Please specify the type of pet for the best support.", "RECOMMEND_2": "- To ensure the safety of both Tasker and your pets, please keep your pets in a cage or a separate area while the Tasker is working.", "OPTION_PET_FEE": "To clean the pet area effectively, Taskers need to be equipped with special tools and chemicals. Therefore, when selecting this option, an additional fee of {{price}} will be applied.", "OPTION_PET_NOTE": "Some Taskers are allergic to pet hair and cannot perform the task. Please specify the type of pet for the best support.", "TITLE_MODAL_LIST_TOOLS": "Tools, equipment, and chemicals", "LIST_TOOL_TASK_PREMIUM": "The service includes standard cleaning supplies and a handheld vacuum cleaner.", "LIST_TOOL_TASK_NORMAL": "The service includes standard cleaning supplies", "PREMIUM": "Premium"}, "PT1_DETAIL_CHOOSE_TASKER_EXPLAIN_3_TH": "Two hour before the task starts, if you don't choose, the system will automatically choose a tasker and the fee of add-on service will still be charged.", "PT1_DETAIL_CHOOSE_TASKER_EXPLAIN_3": "One hour before the task starts, if you don't choose, the system will automatically choose a tasker and the fee of add-on service will still be charged.", "PT1_DETAIL_CHOOSE_MANUAL_FEES": "Fees", "PT1_DETAIL_CHOOSE_MANUAL_COST_AND_CURRENCY": "{{cost}} {{currency}}", "PT1_DETAIL_CHOOSE_TASKER_EXPLAIN_1": "This function allows multiple Taskers to accept your task. You can choose one of these Taskers to clean your house.", "PT1_DETAIL_CHOOSE_TASKER_EXPLAIN_2": "You need to book at least **{{hour}} hours** in advance to use this feature.", "COUNTDOWN_DAY": "Days", "COUNTDOWN_HOUR": "Hours", "POST_TASK_CHECKBOX_REPEAT": "Weekly Schedule", "PT2_POPUP_ERROR_TIME_CLOSE": "Close", "POSTTASK_STEP2_ERROR_TIME": "Time invalid. Please book at least {{t}} minutes in advance.", "FAV_TASKER": {"TASKER": "Tasker", "SCHEDULE_OF_MORNING": "Morning", "SCHEDULE_OF_AFTERNOON": "Afternoon", "SCHEDULE_OF_EVENING": "Evening", "CONFLICT_TIME_NOTE": "Sorry! The time slot you selected conflicts with the Tasker's schedule. Please check the Tasker's schedule below and choose a different time slot.", "OPTION_TIME": "Option {{stt}}", "ADD_OPTION_TIME": "Add working time slot", "ADD_OPTION_TIME_1": "Add option", "ADD_OPTION_TIME_2": "No, thanks", "ADD_OPTION_TIME_CONTENT": "You can also suggest up to 3 additional time slots for Taskers to flexibly choose according to their schedules. The app will display Taskers' available time slots to help you easily make selections.", "VOUCHER_DISCOUNT": "Voucher giảm giá", "CHANGE_OPTION_TIME": "Change working time", "WARING_FAV_TASKER_NOT_ACCEPT": "In case your favorite Tasker has not yet been confirmed, b<PERSON><PERSON><PERSON> encourages you to resend the task to other Taskers on the system.", "SEND_TO_OTHER_TASKER": "Resend the task to other Taskers", "DESCRIPTION_SEND_TO_OTHER_TASKER": "Please select the time slots that suits you!", "FIND_OTHER_TASKER": "Find other Taskers", "CONTENT_CONFIRMED_FIND_OTHER_TASKER": "You confirm that you want to resend this task to the system to find other Taskers.", "WORKING_TIME": "Working time", "WORKING_DATE": "Date", "PRICE": "Price", "WARNING_PAYMENT_METHOD_CASH": "After Task<PERSON> accepts the task, the system will update the service price according to the option that <PERSON><PERSON> has chosen.", "WARNING_PAYMENT_METHOD_CARD": "The service price will be updated according to the option that Tasker has selected. The system will only deduct money after Tasker accepts the task.", "WARNING_PAYMENT_METHOD_PREPAID": "To book this task, you need to pay the highest amount within the option. If Tasker chooses a option with a lower price, the difference will be refunded to the corresponding e-wallet.", "WAITING_TASKER_ACCEPT": "The task is awaiting for {{tasker}} to accept...", "WARNING_CHANGE_TASK_1": "You can't change the working time because the task has been sent to a favorite Tasker. Please wait, the Tasker will respond to you as soon as possible.", "WARNING_CHANGE_TASK_2": "If you want to change the working time, please chat with the Tasker before canceling the task.", "CANCEL_TASK": "Cancel", "SEE_TASKER_SCHEDULE": "View Tasker's schedule", "TASKER_WORKING_SCHEDULE": "Tasker's schedule", "TASKER_NOT_SUPPORT_BOOKING": "Sorry, you can't use the rebooking feature with your favorite Tasker for this Tasker. Due to the services Tasker is currently providing do not support this feature.", "WARNING_BOOKING_WITH_FAV_TASKER": "You need to book at least 3 hours in advance so that your Favorite Tasker has enough time to accept the task. Please select a different working time.", "WARNING_PAYMENT_METHOD_KREDIVO": "To book this task, you need to pay the highest amount within the available time slots. If the Tasker chooses a time slot with a lower price, the difference will be refunded to your bPay wallet.", "TASKER_WILL_ARRIVE_AT_TIME": "Tasker will arrive at {{time}}", "ADD_OPTION": "Add more options", "ADD_OPTION_NOTE": "If the Tasker declines or does not respond to this task within the specified time, would you like the system to resend task to another Tasker?", "ADD_OPTION_NOTE_2": "If you do not choose this option, we will notify you when the Tasker declines or does not respond.", "ADD_OPTION_CHECK_BOOK": "Send this task to another Tasker", "TIME_FRAME": "Option"}, "WEEKLY_REPEATER_BODY_1": "This option is used for customers who have **weekly service use** demand.", "WEEKLY_REPEATER_BODY_2": "This option is used for customers who want to **use service every week**.", "WEEKLY_REPEATER_BODY_3": "You can proactively change the working time of posted tasks (in the Waiting); or you can update the information of the Weekly Schedule right on the app.", "TASK_DETAIL_TASK_DONE": "Task", "AUTHENTICATED_EXPIRED": "Your login session has expired. Please log in again.", "INTRO_START_EXPERIENCE": "Start the experience", "WEEKLY_REPEATER_IT_MEAN_TITLE": "What is Weekly Schedule?", "CURTAIN_CHOOSE_SERVICE": "Choose service", "DURATION": "Duration", "TITLE_PAYMENT_DETAIL": "Payment Details", "PRICE_SERVICE": "Service cost", "TOTAL_PAYMENT": "Total Payment", "SUBSCRIPTION_TYPE": "Service duration", "SUBSCRIPTION_MONTH_one": "{{count}} month", "SUBSCRIPTION_MONTH_other": "{{count}} months", "DURATION_TEXT": "Maximum {{t1}} or {{t2}}", "PREMIUM_TITLE_OPTIONAL": "Choose Premium Service", "PREMIUM_CONTENT_OPTIONAL": "Premium Service", "PREMIUM_DETAIL_1": "What does Premium Service entail?", "PREMIUM_DETAIL_2": "Performed by premium Taskers who have received advanced training and a Premium license from bTaskee.", "PREMIUM_DETAIL_3": "Standard Cleaning Tools: A handheld vacuum cleaner is included.", "PREMIUM.DESCRIPTION_1": "Use gentle, nice-smelling oils or natural essential oils", "PREMIUM.DESCRIPTION_2": "Disposable wipes or single-use towels", "PREMIUM.DESCRIPTION_3": "All Tasker have special training and official certificates from bTaskee", "CANCEL": "Cancel", "DOG": "Dog", "CAT": "Cat", "NUMBER_OF_ROOMS": "{{t}} rooms", "SUBSCRIPTION_INCREASE_PRICE": "[{{t}} increase due to Holiday]", "SUBSCRIPTION_PRICE_INCREASEMENT_one": "There is {{count}} task in this service package with an adjusted price due to the Holiday.", "SUBSCRIPTION_PRICE_INCREASEMENT_other": "There are {{count}} tasks in this service package with an adjusted price due to the Holiday."}