{"INTRO_ELDERLY_CARE_NAME": "Caring for the elderly at home", "INTRO_ELDERLY_CARE": "Help customers take care of their loved ones with the best conditions and let them work with peace of mind, by providing customers with attentive, experienced and sincere bTaskers. The customer's relatives are fully supported with medical care, while living in the comfort and familiarity of their own home. Living together in the love of family and children will also help the elderly boost energy level and lead healthy lifestyle. Loving and caring for the elderly is a wonderful tradition honoring the Vietnamese filial piety. Let b<PERSON><PERSON>ee take care and shower love to your loved ones on your behalf.", "INTRO_ELDERLY_CARE_1": "After leaving contact information and the need to use the Service, b<PERSON><PERSON><PERSON> will quickly contact - respond to you within 60 minutes (during office hours)", "TITLE_SERVICE_TASK_DAY": "Service by Session/Day", "CONTENT_CHOOSE_TASK_DAY1": "You can book the service more flexible and and faster.", "CONTENT_CHOOSE_TASK_DAY2": "The booking can be easily completed by 60 seconds with flexible options suits your convenience.", "CONTENT_CHOOSE_SUBSCRIPTION1": "Support customer with fixed Tasker.", "CONTENT_CHOOSE_SUBSCRIPTION2": "Customize the schedule based on customer availability. Save booking time, avoid multiple payment.", "TITLE_SERVICE_SUBSCRIPTION": "Service by Monthly Plan", "POST_TASK_ELDERLY_CARE_STEP_3_CHOOSE_DATE_NOTE": "Maximum {{t}} hours/day", "POST_TASK_ELDERLY_CARE_STEP_3_CHOOSE_DATE": "By session", "POST_TASK_ELDERLY_CARE_STEP_3_CHOOSE_DATE_1": "By day", "INTRO_ELDERLY_CARE_THE_WORK_PERFORM": "Tasks to be performed", "INTRO_ELDERLY_CARE_THE_WORK_PERFORM_1": "Comprehensive care for the elderly by well-trained bTaskers.", "INTRO_ELDERLY_CARE_THE_WORK_PERFORM_2": "Look after the elderly.", "INTRO_ELDERLY_CARE_THE_WORK_PERFORM_3": "Feed the elderly.", "INTRO_ELDERLY_CARE_THE_WORK_PERFORM_4": "Perform hygiene care for the elderly (oral hygiene, assisting the elderly to wash their hair, take a bath).", "INTRO_ELDERLY_CARE_THE_WORK_PERFORM_5": "Lift and turn the elderly when being unable to move", "INTRO_ELDERLY_CARE_THE_WORK_PERFORM_6": "Empty the elderly's potty and waste contents when they can't go to the toilet by themselves.", "INTRO_ELDERLY_CARE_THE_WORK_PERFORM_7": "Observe temperature, blood pressure, pulse", "INTRO_ELDERLY_CARE_THE_WORK_PERFORM_8": "Support, supervise the elderly or patients taking prescription drugs.", "INTRO_ELDERLY_CARE_THE_WORK_PERFORM_9": "Massage and pat the painful area.", "INTRO_ELDERLY_CARE_THE_WORK_PERFORM_10": "Notify the medical condition to relatives, doctors and call for support when necessary.", "INTRO_ELDERLY_CARE_NOT_PERFORM": "Tasks that won't be executed", "INTRO_ELDERLY_CARE_NOT_PERFORM_1": "Injections, drip-feed, stitches removing, bandage changing, wound cleaning, colonic/enema for the elderly and invasive procedures.", "INTRO_ELDERLY_CARE_NOT_PERFORM_2": "Do not perform examination  of areas of the body such as urinary tract status, genital hygiene check.", "INTRO_ELDERLY_CARE_NOT_PERFORM_3": "Place urinary catheter, gastric tube, acupressure, acupuncture.", "INTRO_ELDERLY_CARE_NOT_PERFORM_4": "Perform autogenic drainage to restore respiratory function for the elderly.", "INTRO_ELDERLY_CARE_QA": "What tasks will bTaskers perform?", "INTRO_ELDERLY_CARE_QA_1": "Tasks that bTaskers don't perform", "POST_TASK_ELDERLY_CARE_STEP_4_IN_HOUSE": "Caring for the elderly at home"}