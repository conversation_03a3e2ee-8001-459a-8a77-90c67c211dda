{"INTRO_PATIENT_CARE_NAME": "Merawat orang sakit", "INTRO_ELDERLY_CARE": "Bantu pelanggan merawat orang yang mereka cintai dengan kondisi terbaik dan biarkan mereka bekerja dengan tenang, bersa<PERSON> dengan Tasker yang be<PERSON>, perhatian dan tulus kepada pelanggan. Dimana pelanggan didukung oleh perawatan medis, sambil mendapatkan kenyamanan dan keakraban di rumah mereka sendiri. Hidup bersama saling mencintai keluarga dan anak-anak akan membantu meningkatkan energi lansia serta menjalani gaya hidup sehat. Mencintai dan merawat orang tua adalah tradisi yang luar biasa untuk menghormati bakti orang Vietnam. Biarkan bTaskee merawat dan menyampaikan kasih sayang kepada orang yang Anda cintai atas nama Anda.", "INTRO_PATIENT_CARE_1": "Mencintai dan merawat orang yang dicintai adalah tradisi besar yang menunjukkan bakti orang Vietnam. Biarkan bTaskee merawat dan memberikan cinta kepada orang yang Anda cintai atas nama Anda.", "INTRO_PATIENT_CARE_2": "<PERSON><PERSON><PERSON> Anda memberikan informasi dan k<PERSON><PERSON><PERSON><PERSON>n, b<PERSON><PERSON><PERSON> akan segera menghubungi dan menanggapi Anda dalam waktu 60 menit (saat jam kerja)", "INTRO_PATIENT_CARE_THE_WORK_PERFORM": "<PERSON><PERSON><PERSON> dan <PERSON>", "INTRO_PATIENT_CARE_THE_WORK_PERFORM_1": "Menjaga dan merawat orang sakit.", "INTRO_PATIENT_CARE_THE_WORK_PERFORM_2": "<PERSON><PERSON> makan pasien.", "INTRO_PATIENT_CARE_THE_WORK_PERFORM_3": "<PERSON><PERSON> pasien dengan kebersihan pribadi (kebers<PERSON><PERSON> mulut, cuci rambut, dan mandi)", "INTRO_PATIENT_CARE_THE_WORK_PERFORM_4": "<PERSON>sisikan dan angkat pasien.", "INTRO_PATIENT_CARE_THE_WORK_PERFORM_5": "Membantu pasien bergerak saat melakukan pemeriksaan medis.", "INTRO_PATIENT_CARE_THE_WORK_PERFORM_6": "Membantu pasien dalam eliminasi.", "INTRO_PATIENT_CARE_THE_WORK_PERFORM_7": "Kosongkan isi pispot pasien.", "INTRO_PATIENT_CARE_THE_WORK_PERFORM_8": "<PERSON><PERSON>i wadah kotoran pasien.", "INTRO_PATIENT_CARE_THE_WORK_PERFORM_9": "<PERSON><PERSON><PERSON>, awasi lansia atau pasien dalam mengonsumsi resep obat", "INTRO_PATIENT_CARE_THE_WORK_PERFORM_10": "<PERSON><PERSON>t dan tepuk-tepuk bagian yang nyeri.", "INTRO_PATIENT_CARE_THE_WORK_PERFORM_11": "<PERSON><PERSON><PERSON> tanda-tanda vital (j<PERSON><PERSON>, nadi, te<PERSON>an darah, suhu)", "INTRO_PATIENT_CARE_THE_WORK_PERFORM_12": "Memberi tau kondisi pasien kepada keluarga, dokter bila <PERSON>.", "INTRO_PATIENT_CARE_THE_WORK_PERFORM_13": "Mengikuti prosedur administrasi di rumah sakit.", "INTRO_PATIENT_CARE_NOT_PERFORM": "Tugas yang tidak akan di<PERSON>ukan", "INTRO_PATIENT_CARE_NOT_PERFORM_1": "<PERSON><PERSON><PERSON>, in<PERSON><PERSON>, pele<PERSON>an jahitan, pengg<PERSON>an perban, pem<PERSON><PERSON>an luka, kolon/enema untuk lansia dan prosedur invasif.", "INTRO_PATIENT_CARE_NOT_PERFORM_2": "Tidak melakukan pemeriksaan apapun pada bagian tubuh seperti kondisi saluran kemih, pemeriks<PERSON> kebersihan alat kelamin.", "INTRO_PATIENT_CARE_NOT_PERFORM_3": "Tidak perlu lakukan kateter urin, tabung lambung, akupresur, akupunktur.", "INTRO_PATIENT_CARE_NOT_PERFORM_4": "Lakukan drainase autogenik untuk mengembalikan fungsi pernapasan bagi lansia.", "INTRO_PATIENT_CARE_QA": "Tugas apa yang akan dilakukan oleh Tasker?", "INTRO_PATIENT_CARE_QA_1": "Tugas yang tidak di<PERSON>ukan o<PERSON>h b<PERSON>asker", "POST_TASK_PATIENT_CARE_STEP_3_CHOOSE_DATE_NOTE": "Maximum {{t}} hours/day", "POST_TASK_PATIENT_CARE_STEP_3_CHOOSE_DATE": "By session", "POST_TASK_PATIENT_CARE_STEP_3_CHOOSE_DATE_1": "By day", "POST_TASK_PATIENT_CARE_STEP_4_IN_HOUSE": "Caring for the elderly at home", "TITLE_SERVICE_TASK_DAY": "Service by Session/Day", "CONTENT_CHOOSE_TASK_DAY1": "You can book the service more flexible and and faster.", "CONTENT_CHOOSE_TASK_DAY2": "The booking can be easily completed by 60 seconds with flexible options suits your convenience.", "CONTENT_CHOOSE_SUBSCRIPTION1": "Support customer with fixed Tasker.", "CONTENT_CHOOSE_SUBSCRIPTION2": "Customize the schedule based on customer availability. Save booking time, avoid multiple payment.", "TITLE_SERVICE_SUBSCRIPTION": "Service by Monthly Plan"}