const enTranslations = {
  deepCleaning: require('./en/deep-cleaning.json'),
  airConditioner: require('./en/air-conditioner.json'),
  common: require('./en/common.json'),
  host: require('./en/host.json'),
  childCare: require('./en/child-care.json'),
  elderlyCare: require('./en/elderly-care.json'),
  patientCare: require('./en/patient-care.json'),
  officeCleaning: require('./en/office-cleaning.json'),
  cleaningSub: require('./en/cleaning-subscription.json'),
};

const koTranslations = {
  deepCleaning: require('./ko/deep-cleaning.json'),
  airConditioner: require('./ko/air-conditioner.json'),
  common: require('./ko/common.json'),
  host: require('./ko/host.json'),
  childCare: require('./ko/child-care.json'),
  elderlyCare: require('./ko/elderly-care.json'),
  patientCare: require('./ko/patient-care.json'),
  officeCleaning: require('./ko/office-cleaning.json'),
  cleaningSub: require('./ko/cleaning-subscription.json'),
};

const idTranslations = {
  deepCleaning: require('./id/deep-cleaning.json'),
  airConditioner: require('./id/air-conditioner.json'),
  common: require('./id/common.json'),
  host: require('./id/host.json'),
  childCare: require('./id/child-care.json'),
  elderlyCare: require('./id/elderly-care.json'),
  patientCare: require('./id/patient-care.json'),
  officeCleaning: require('./id/office-cleaning.json'),
  cleaningSub: require('./id/cleaning-subscription.json'),
};

const msTranslations = {
  deepCleaning: require('./ms/deep-cleaning.json'),
  airConditioner: require('./ms/air-conditioner.json'),
  common: require('./ms/common.json'),
  host: require('./ms/host.json'),
  childCare: require('./ms/child-care.json'),
  elderlyCare: require('./ms/elderly-care.json'),
  patientCare: require('./ms/patient-care.json'),
  officeCleaning: require('./ms/office-cleaning.json'),
  cleaningSub: require('./ms/cleaning-subscription.json'),
};

const thTranslations = {
  deepCleaning: require('./th/deep-cleaning.json'),
  airConditioner: require('./th/air-conditioner.json'),
  common: require('./th/common.json'),
  host: require('./th/host.json'),
  childCare: require('./th/child-care.json'),
  elderlyCare: require('./th/elderly-care.json'),
  patientCare: require('./th/patient-care.json'),
  officeCleaning: require('./th/office-cleaning.json'),
  cleaningSub: require('./th/cleaning-subscription.json'),
};

const viTranslations = {
  deepCleaning: require('./vi/deep-cleaning.json'),
  airConditioner: require('./vi/air-conditioner.json'),
  common: require('./vi/common.json'),
  host: require('./vi/host.json'),
  childCare: require('./vi/child-care.json'),
  elderlyCare: require('./vi/elderly-care.json'),
  patientCare: require('./vi/patient-care.json'),
  officeCleaning: require('./vi/office-cleaning.json'),
  cleaningSub: require('./vi/cleaning-subscription.json'),
};

export const translations = {
  en: enTranslations,
  ko: koTranslations,
  id: idTranslations,
  ms: msTranslations,
  th: thTranslations,
  vi: viTranslations,
};
