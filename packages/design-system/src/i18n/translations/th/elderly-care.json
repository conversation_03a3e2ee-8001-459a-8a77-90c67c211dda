{"INTRO_ELDERLY_CARE_NAME": "ดูแลผู้สูงอายุที่บ้าน", "INTRO_ELDERLY_CARE": "ช่วยเหลือคุณลูกค้าดูแลคนที่คุณรักด้วยบริการที่ดีที่สุด เพื่อให้คุณสามารถทำงานได้อย่างไร้กังวล ลูกค้าจะได้รับการบริการทางการแพทย์อย่างเต็มรูปแบบตลอดช่วงเวลาการพักฟื้นอยู่ที่บ้านของคุณ การใช้ชีวิตอยู่กับครอบครัวที่คุณรักจะช่วยให้ผู้สูงอายุฟื้นฟูสภาพร่างกายและจิตใจได้รวดเร็วยิ่งขึ้น ซึ่งการมอบความรักและความห่วงใยเปรียบเสมือนวัฒนธรรมที่ชาวไทยมอบให้กันและกันมาโดยตลอด ให้ bTaskee ได้ดูแลและมอบความรักให้กับคนที่คุณรักแทนคุณ", "INTRO_ELDERLY_CARE_1": "หลังจากแจ้งข้อมูลติดต่อและความต้องการในการใช้บริการแล้ว b<PERSON><PERSON>ee จะรีบติดต่อคุณกลับภายใน 60 นาที (ในช่วงเวลาทำการ)", "TITLE_SERVICE_TASK_DAY": "ให้บริการแบบรายครั้ง/วัน", "CONTENT_CHOOSE_TASK_DAY1": "คุณสามารถจองบริการตามช่วงเวลาที่ต้องการได้อย่างสะดวกและรวดเร็วยิ่งขึ้น", "CONTENT_CHOOSE_TASK_DAY2": "คุณสามารถจองบริการสำเร็จภายใน 60 วินาที โดยกำหนดช่วงเวลารับบริการได้ตามความต้องการสำหรับบริการรายครั้ง", "CONTENT_CHOOSE_SUBSCRIPTION1": "ให้ความช่วยเหลือลูกค้าด้วยผู้ให้บริการแบบประจำ", "CONTENT_CHOOSE_SUBSCRIPTION2": "สามารถปรับเปลี่ยนตารางเวลาให้ตรงกับความต้องการของลูกค้า เพื่อประหยัดเวลาจองงานและหลีกเลี่ยงการชำระเงินหลายครั้ง", "TITLE_SERVICE_SUBSCRIPTION": "ให้บริการแบบรายเดือน", "POST_TASK_ELDERLY_CARE_STEP_3_CHOOSE_DATE_NOTE": "เวลาทำงานสูงสุด {{t}} ชั่วโมง/วัน", "POST_TASK_ELDERLY_CARE_STEP_3_CHOOSE_DATE": "จำนวนครั้ง", "POST_TASK_ELDERLY_CARE_STEP_3_CHOOSE_DATE_1": "จำนวนวัน", "INTRO_ELDERLY_CARE_THE_WORK_PERFORM": "งานที่ให้บริการ", "INTRO_ELDERLY_CARE_THE_WORK_PERFORM_1": "ดูแลผู้สูงอายุอย่างเชี่ยวชาญ โดย bTasker ที่ผ่านการฝึกอบรมมาเป็นอย่างดี", "INTRO_ELDERLY_CARE_THE_WORK_PERFORM_2": "คอยดูแลผู้สูงอายุ", "INTRO_ELDERLY_CARE_THE_WORK_PERFORM_3": "ป้อนอาหาร", "INTRO_ELDERLY_CARE_THE_WORK_PERFORM_4": "ช่วยเหลือผู้สูงอายุเรื่องสุขอนามัยส่วนตัว (แปรงฟัน,สระผม,อาบน้ำ)", "INTRO_ELDERLY_CARE_THE_WORK_PERFORM_5": "ช่วยอุ้มยก เคลื่อนย้าย และพลิกตัว", "INTRO_ELDERLY_CARE_THE_WORK_PERFORM_6": "ทำความสะอาดกระโถนและล้างของเสีย เมื่อผู้สูงอายุไม่สามารถเข้าห้องน้ำได้ด้วยตัวเอง", "INTRO_ELDERLY_CARE_THE_WORK_PERFORM_7": "สังเกตอุณหภูมิ ความดันโลหิต และชีพจร", "INTRO_ELDERLY_CARE_THE_WORK_PERFORM_8": "ช่วยเหลือและดูแลให้ผู้สูงอายุรับประทานยาตามที่แพทย์สั่ง", "INTRO_ELDERLY_CARE_THE_WORK_PERFORM_9": "คอยบีบนวดในบริเวณที่ผู้สูงอายุปวด", "INTRO_ELDERLY_CARE_THE_WORK_PERFORM_10": "แจ้งอาการของผู้ป่วยให้ครอบครัวและแพทย์ เมื่อถึงเหตุจำเป็น", "INTRO_ELDERLY_CARE_NOT_PERFORM": "งานที่ไม่ได้ให้บริการ", "INTRO_ELDERLY_CARE_NOT_PERFORM_1": "ฉีดยา, ให้อาหารทางสายยาง, เย็บแผลและเปลี่ยนผ้าพันแผล, ล้างแผล, สวนสำไส้ใหญ่ให้กับผู้สูงอายุ", "INTRO_ELDERLY_CARE_NOT_PERFORM_2": "ตรวจร่างกายในส่วนต่างๆ เช่น ระบบทางเดินปัสสาวะ, ตรวจเช็กบริเวณอวัยวะเพศ", "INTRO_ELDERLY_CARE_NOT_PERFORM_3": "สวนท่อปัสสาวะ, ใส่ท่อหรือสายให้อาหาร, กดจุดและฝังเข็ม", "INTRO_ELDERLY_CARE_NOT_PERFORM_4": "ควบคุมลมหายใจเพื่อฟื้นฟูระบบทางเดินหายใจกับผู้สูงอายุ", "INTRO_ELDERLY_CARE_QA": "bTasker ให้ความช่วยเหลืออะไรบ้าง?", "INTRO_ELDERLY_CARE_QA_1": "งานที่ bTasker ไม่ได้ให้บริการ", "POST_TASK_ELDERLY_CARE_STEP_4_IN_HOUSE": "ดูแลผู้สูงอายุที่บ้าน"}