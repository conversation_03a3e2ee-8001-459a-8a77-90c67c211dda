{"_id": "x4ec1916bdb09721221e2c863a7e61bQuanTest", "avatar": "https://toanphambucket.s3.amazonaws.com/mystore%2Fasker-x4ec1916bdb09721221e2c863a7e61bQuanTest-itsts", "bPoint": {"TH": 347}, "cities": [{"city": "<PERSON><PERSON> <PERSON>", "country": "VN"}, {"city": "Bangkok", "country": "TH"}, {"city": "Jakarta", "country": "ID"}, {"city": "<PERSON><PERSON>", "country": "VN"}, {"city": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "country": "VN"}, {"city": "<PERSON><PERSON>", "country": "VN"}], "countryCode": "+84", "fAccountId": "x1e0d96b1b1f81ab95d09f53eb2d46c30QuanTest", "homeMovingLocations": [{"_id": "x25a86865909a7fc991163cb26ff935f2", "address": "QM37+<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 11, <PERSON><PERSON><PERSON><PERSON> 5, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON><PERSON> <PERSON>", "contact": "ba test", "country": "VN", "countryCode": "+84", "description": "1", "district": "Quận 5", "isoCode": "VN", "lat": 10.7539547, "lng": 106.6632918, "phoneNumber": "**********", "shortAddress": "QM37+H8M <PERSON><PERSON><PERSON><PERSON> 5 hâh"}, {"_id": "xac6995f94cfff8c3088cbac269a4c3f0", "address": "<PERSON><PERSON><PERSON><PERSON> 1, <PERSON><PERSON>, Việt Nam", "city": "<PERSON><PERSON> <PERSON>", "contact": "ba test", "country": "VN", "countryCode": "+84", "description": "1", "district": "Quận 1", "isoCode": "VN", "lat": 10.7756587, "lng": 106.7004238, "phoneNumber": "**********", "shortAddress": "Quận 1 Hồ Chí Minh"}, {"_id": "x0d43aa946b90b7d0aee58e9c05a42e4d", "address": "<PERSON><PERSON><PERSON> ty <PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Việt Nam", "city": "<PERSON><PERSON> <PERSON>", "contact": "ba test", "country": "VN", "countryCode": "+84", "description": "số 9", "district": "<PERSON><PERSON><PERSON>", "isoCode": "VN", "lat": 10.7866056, "lng": 106.7298182, "phoneNumber": "**********", "shortAddress": "Tòa nhà HQ Tower 09"}, {"_id": "xce06eea676ac1ae114f84916710be9b5", "address": "VẠN PHÚC CITY, <PERSON><PERSON><PERSON><PERSON> 13, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> Nam", "city": "<PERSON><PERSON> <PERSON>", "contact": "ba test", "country": "VN", "countryCode": "+84", "description": "số 22", "district": "<PERSON><PERSON><PERSON>", "isoCode": "VN", "lat": 10.8403244, "lng": 106.7160982, "phoneNumber": "**********", "shortAddress": "375 Qu<PERSON>c lộ 13"}], "hospitalLocations": [{"_id": "x83197743a299b4a4658f831efe10a56f", "address": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> D1, <PERSON><PERSON> <PERSON><PERSON> thị <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 7, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON><PERSON> <PERSON>", "country": "VN", "countryCode": "+84", "description": "d fads fa sdf df df", "district": "Quận 7", "isoCode": "VN", "lat": 10.7394465, "lng": 106.696324, "phoneNumber": "0397411511", "shortAddress": "df"}], "housekeepingLocations": [{"_id": "xceca5d1c28bed6ca7a42acf82e5951e7", "address": "Place in Saigon apartment 2, Phường 22, Bình Thạnh, <PERSON><PERSON>, Việt Nam", "city": "<PERSON><PERSON> <PERSON>", "contact": "ba test", "country": "VN", "countryCode": "+84", "description": "222", "district": "Bình Thạnh", "isoCode": "VN", "lat": 10.7906155, "lng": 106.7173618, "locationName": "nhà 35", "phoneNumber": "**********", "shortAddress": "Vietnam Bình Thạnh"}, {"_id": "x438049b9672cd60232209e8b95773984", "address": "<PERSON>rung Tâm <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Vi<PERSON><PERSON> Nam", "city": "<PERSON><PERSON> <PERSON>", "contact": "ba test", "country": "VN", "countryCode": "+84", "description": "số 22", "district": "<PERSON><PERSON><PERSON>", "images": [{"images": ["https://toanphambucket.s3.amazonaws.com/mystore%2F%2FuploadImage%2FVN%2Fx4ec1916bdb09721221e2c863a7e61bf2%2Fhome-moving%2Ffurniture%2F283e776b-9a31-446c-8381-4af3b7a23a19.jpg-1727406632"], "name": "SINGLE"}], "isoCode": "VN", "lat": 10.8276425, "lng": 106.7214707, "locationName": "H<PERSON>", "phoneNumber": "**********", "shortAddress": "240 242"}, {"_id": "x92f8a77f560612a089c77eb10bd79300", "address": "PJV6+<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 17A, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>, Việt Nam", "city": "<PERSON><PERSON> <PERSON>", "contact": "ba test", "country": "VN", "countryCode": "+84", "description": "tầng 6", "district": "Bình Tân", "images": [{"images": ["https://toanphambucket.s3.amazonaws.com/mystore%2F%2FuploadImage%2FVN%2Fx4ec1916bdb09721221e2c863a7e61bf2%2Fhome-moving%2Ffurniture%2F7bb6f345-e534-4562-8e87-57867f07195e.jpg-1727235850"], "name": "SINGLE"}, {"images": ["https://toanphambucket.s3.amazonaws.com/mystore%2F%2FuploadImage%2FVN%2Fx4ec1916bdb09721221e2c863a7e61bf2%2Fhome-moving%2Ffurniture%2F9c4b326c-126a-4a7b-8788-b95f40fa5fe3.jpg-1727235862"], "name": "COUPLE"}, {"images": ["https://toanphambucket.s3.amazonaws.com/mystore%2F%2FuploadImage%2FVN%2Fx4ec1916bdb09721221e2c863a7e61bf2%2Fhome-moving%2Ffurniture%2F4303ca7b-a604-4d77-9856-07b9b9c4fd7d.jpg-1727235856", "https://toanphambucket.s3.amazonaws.com/mystore%2F%2FuploadImage%2FVN%2Fx4ec1916bdb09721221e2c863a7e61bf2%2Fhome-moving%2Ffurniture%2Ff63e1cf8-93d7-4759-8f40-d77e005bfc87.jpg-1727235856"], "name": "FAMILY"}, {"images": ["https://toanphambucket.s3.amazonaws.com/mystore%2F%2FuploadImage%2FVN%2Fx4ec1916bdb09721221e2c863a7e61bf2%2Fhome-moving%2Ffurniture%2Fcdfd9bbb-c963-4b83-aa32-82f128589fbf.jpg-1727235866"], "name": "DORM_1"}], "isoCode": "VN", "lat": 10.7440898, "lng": 106.6121872, "locationName": "aeon", "phoneNumber": "**********", "shortAddress": "PJV6+GVR Đường Số 17A"}, {"_id": "x56eabb05dee77208d0cf2da6d4a4e5dc", "address": "<PERSON><PERSON><PERSON> ty <PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Việt Nam", "city": "<PERSON><PERSON> <PERSON>", "contact": "ba test", "country": "VN", "countryCode": "+84", "description": "số 9", "district": "<PERSON><PERSON><PERSON>", "images": [{"images": ["https://toanphambucket.s3.amazonaws.com/mystore%2F%2FuploadImage%2FVN%2Fx4ec1916bdb09721221e2c863a7e61bf2%2Fhome-moving%2Ffurniture%2FF3BDFDEF-E655-4EEE-8564-C74FD2C48752.jpg-1727086260", "https://toanphambucket.s3.amazonaws.com/mystore%2F%2FuploadImage%2FVN%2Fx4ec1916bdb09721221e2c863a7e61bf2%2Fhome-moving%2Ffurniture%2F24FFC044-E6C6-40D3-B97E-E347E457ABE9.jpg-1727083138", "https://toanphambucket.s3.amazonaws.com/mystore%2F%2FuploadImage%2FVN%2Fx4ec1916bdb09721221e2c863a7e61bf2%2Fhome-moving%2Ffurniture%2F535DA519-5AE4-4A75-B4E6-F11147FC28AE.jpg-1726818763", "https://toanphambucket.s3.amazonaws.com/mystore%2F%2FuploadImage%2FVN%2Fx4ec1916bdb09721221e2c863a7e61bf2%2Fhome-moving%2Ffurniture%2FB9FA7A9E-71A9-448A-A8A8-C9DC8A0AE2C5.jpg-1726818763"], "name": "SINGLE"}, {"images": ["https://toanphambucket.s3.amazonaws.com/mystore%2F%2FuploadImage%2FVN%2Fx4ec1916bdb09721221e2c863a7e61bf2%2Fhome-moving%2Ffurniture%2FDCD36254-6616-45BF-ABD3-FDFE83763635.jpg-1726818823", "https://toanphambucket.s3.amazonaws.com/mystore%2F%2FuploadImage%2FVN%2Fx4ec1916bdb09721221e2c863a7e61bf2%2Fhome-moving%2Ffurniture%2FB1F5F89E-1FC5-4347-9655-D833D9DA3CD6.jpg-1726818823"], "name": "DORM_2"}, {"images": ["https://toanphambucket.s3.amazonaws.com/mystore%2F%2FuploadImage%2FVN%2Fx4ec1916bdb09721221e2c863a7e61bf2%2Fhome-moving%2Ffurniture%2F2891D5F6-F9DE-4F3B-ABE3-FC7FE3FB82E0.jpg-1726818776", "https://toanphambucket.s3.amazonaws.com/mystore%2F%2FuploadImage%2FVN%2Fx4ec1916bdb09721221e2c863a7e61bf2%2Fhome-moving%2Ffurniture%2FA83DDCB0-664F-4D47-A89E-7835162C378E.jpg-1726818776"], "name": "COUPLE"}, {"images": ["https://toanphambucket.s3.amazonaws.com/mystore%2F%2FuploadImage%2FVN%2Fx4ec1916bdb09721221e2c863a7e61bf2%2Fhome-moving%2Ffurniture%2F079A97C5-756F-421A-BF3B-18D2D7B231A7.jpg-1726818785", "https://toanphambucket.s3.amazonaws.com/mystore%2F%2FuploadImage%2FVN%2Fx4ec1916bdb09721221e2c863a7e61bf2%2Fhome-moving%2Ffurniture%2FB8F80F6A-8625-4204-8E02-33B5509EC2D8.jpg-1726818785"], "name": "DORM_1"}, {"images": ["https://toanphambucket.s3.amazonaws.com/mystore%2F%2FuploadImage%2FVN%2Fx4ec1916bdb09721221e2c863a7e61bf2%2Fhome-moving%2Ffurniture%2F3104F0C8-DAE5-4338-A623-5DAF65C8444B.jpg-1726818795", "https://toanphambucket.s3.amazonaws.com/mystore%2F%2FuploadImage%2FVN%2Fx4ec1916bdb09721221e2c863a7e61bf2%2Fhome-moving%2Ffurniture%2FF6B09AE2-9123-4248-8322-4582446BA36E.jpg-1726818795"], "name": "FAMILY"}], "isoCode": "VN", "lat": 10.7866056, "lng": 106.7298182, "locationName": "tầng 3", "phoneNumber": "**********", "shortAddress": "Tòa nhà HQ Tower 09"}, {"_id": "x5ee893b33db1ba80d87c0fd2e05b2f38", "address": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> D1, <PERSON><PERSON> <PERSON><PERSON> thị <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 7, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON><PERSON> <PERSON>", "contact": "ba test", "country": "VN", "countryCode": "+84", "description": "b<PERSON><PERSON><PERSON>", "district": "Quận 7", "images": [{"images": ["https://toanphambucket.s3.amazonaws.com/mystore%2F%2FuploadImage%2FVN%2Fx4ec1916bdb09721221e2c863a7e61bf2%2Fhome-moving%2Ffurniture%2Fb6a3ac4b-4e9d-4e7d-bdc1-6327a915d292.jpg-1727237749", "https://toanphambucket.s3.amazonaws.com/mystore%2F%2FuploadImage%2FVN%2Fx4ec1916bdb09721221e2c863a7e61bf2%2Fhome-moving%2Ffurniture%2Ffb2969d4-8c6c-4ec5-84fb-6300423ac7f0.jpg-1727237749"], "name": "2_ROOM"}, {"images": ["https://toanphambucket.s3.amazonaws.com/mystore%2F%2FuploadImage%2FVN%2Fx4ec1916bdb09721221e2c863a7e61bf2%2Fhome-moving%2Ffurniture%2Fc4cb69dc-4b1d-43f4-b06a-86bb2d98bd9a.jpg-1727237738"], "name": "1_ROOM"}], "isoCode": "VN", "lat": 10.7394465, "lng": 106.696324, "locationName": "tezst", "phoneNumber": "**********", "shortAddress": "69 Đường D1"}], "isBusiness": true, "isBusinessMember": false, "isTesterCommunity": false, "isoCode": "VN", "language": "en", "lastPostedTask": "2025-06-08T06:05:07Z", "locations": [{"_id": "x02d858e8bb2c0e01de8216b7a6a3d801", "address": "Thanh <PERSON>, Việt Nam", "city": "<PERSON><PERSON>", "contact": "Quan test", "country": "VN", "countryCode": "+84", "description": "ma", "district": "<PERSON><PERSON><PERSON><PERSON> phố <PERSON> Hoá", "homeType": "HOME", "isoCode": "VN", "lat": 19.806692, "lng": 105.7851816, "phoneNumber": "0397411511", "shortAddress": "<PERSON>h ho<PERSON>"}, {"_id": "xd6a36168ada192611c922cc962907a04", "address": "<PERSON><PERSON><PERSON><PERSON> 1, <PERSON><PERSON>, Việt Nam", "city": "<PERSON><PERSON> <PERSON>", "contact": "ba test", "country": "VN", "countryCode": "+84", "description": "uỷ ban", "district": "Quận 1", "homeType": "HOME_OFFICE", "isoCode": "VN", "lat": 10.7756587, "lng": 106.7004238, "phoneNumber": "**********", "shortAddress": "District 1 Ho Chi Minh City"}, {"_id": "xc3f74b0de1f15cfcb59df62a8ad60791", "address": "Place in Saigon apartment 2, Phường 22, Bình Thạnh, <PERSON><PERSON>, Việt Nam", "city": "<PERSON><PERSON> <PERSON>", "contact": "ba test", "country": "VN", "countryCode": "+84", "description": "222", "district": "Bình Thạnh", "homeType": "HOME", "isoCode": "VN", "lat": 10.7906155, "lng": 106.7173618, "phoneNumber": "**********", "shortAddress": "Vietnam Bình Thạnh"}, {"_id": "x777a0907842372808c64869bda77d56b", "address": "<PERSON>, A20.5/A20.5 <PERSON><PERSON>, <PERSON><PERSON> 2, <PERSON><PERSON><PERSON><PERSON> 11, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON><PERSON> <PERSON>", "contact": "ba test", "country": "VN", "countryCode": "+84", "description": "1", "district": "Quận 11", "homeType": "HOME_OFFICE", "isoCode": "VN", "lat": 10.7617947, "lng": 106.6602193, "phoneNumber": "**********", "shortAddress": "Chung Cư A20.5"}, {"_id": "x07c6ccd2f6cbbfc9772a085e7008e3ff", "address": "PJV6+<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 17A, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>, Việt Nam", "city": "<PERSON><PERSON> <PERSON>", "contact": "ba test", "country": "VN", "countryCode": "+84", "description": "tầng 6", "district": "Bình Tân", "homeType": "HOME", "isoCode": "VN", "lat": 10.7440898, "lng": 106.6121872, "phoneNumber": "**********", "shortAddress": "PJV6+GVR Đường Số 17A"}, {"_id": "x24b73aa2cd08d0a07efdb25c5b796c29", "address": "<PERSON>rung Tâm <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Vi<PERSON><PERSON> Nam", "city": "<PERSON><PERSON> <PERSON>", "contact": "ba test", "country": "VN", "countryCode": "+84", "description": "số 22", "district": "<PERSON><PERSON><PERSON>", "homeType": "HOME", "isoCode": "VN", "lat": 10.8276425, "lng": 106.7214707, "phoneNumber": "**********", "shortAddress": "240 242"}, {"_id": "x52c330cf19a1b09f0393f115578a64e5", "address": "<PERSON>rung Tâm <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Vi<PERSON><PERSON> Nam", "city": "<PERSON><PERSON> <PERSON>", "contact": "ba test", "country": "VN", "countryCode": "+84", "description": "số22", "district": "<PERSON><PERSON><PERSON>", "homeType": "HOME", "isoCode": "VN", "lat": 10.8276425, "lng": 106.7214707, "phoneNumber": "**********", "shortAddress": "240 242"}, {"_id": "xecf011a36230f35c740fa7417337f378", "address": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Việt Nam", "city": "<PERSON><PERSON> <PERSON>", "contact": "ba test", "country": "VN", "countryCode": "+84", "description": "1", "district": "<PERSON><PERSON><PERSON>", "homeType": "HOME_OFFICE", "isoCode": "VN", "lat": 10.687392, "lng": 106.5938538, "phoneNumber": "**********", "shortAddress": "<PERSON><PERSON><PERSON>"}, {"_id": "x78aea451e9fcd376764917006273dfdf", "address": "VẠN PHÚC CITY, <PERSON><PERSON><PERSON><PERSON> 13, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> Nam", "city": "<PERSON><PERSON> <PERSON>", "contact": "ba test", "country": "VN", "countryCode": "+84", "description": "số 22", "district": "<PERSON><PERSON><PERSON>", "homeType": "HOME", "isoCode": "VN", "lat": 10.8403244, "lng": 106.7160982, "phoneNumber": "**********", "shortAddress": "375 Qu<PERSON>c lộ 13"}, {"_id": "x3a4c5e289aef7446c49ec105e1d2f447", "address": "<PERSON><PERSON><PERSON> ty <PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Việt Nam", "city": "<PERSON><PERSON> <PERSON>", "contact": "ba test", "country": "VN", "countryCode": "+84", "description": "số 9", "district": "<PERSON><PERSON><PERSON>", "homeType": "HOME", "isoCode": "VN", "lat": 10.7866056, "lng": 106.7298182, "phoneNumber": "**********", "shortAddress": "Tòa nhà HQ Tower 09"}, {"_id": "xe6a3f5940e2c8e59184bda7cf280bc91", "address": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> D1, <PERSON><PERSON> <PERSON><PERSON> thị <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 7, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON><PERSON> <PERSON>", "contact": "ba test", "country": "VN", "countryCode": "+84", "description": "b<PERSON><PERSON><PERSON>", "district": "Quận 7", "homeType": "HOME", "isDefault": true, "isoCode": "VN", "lat": 10.7394465, "lng": 106.696324, "phoneNumber": "**********", "shortAddress": "69 Đường D1"}], "name": "Quan test", "phone": "0397411511", "point": 146, "rankInfo": {"point": 684, "rankName": "GOLD", "text": {"en": "Gold", "ko": "금", "th": "ระดับ Gold", "vi": "<PERSON><PERSON><PERSON>"}}, "rankInfoByCountry": {"TH": {"point": 347, "rankName": "SILVER", "text": {"en": "Silver", "ko": "실버", "th": "ระดับ Silver", "vi": "Bạc"}}}, "referralCode": "quantestxEfn", "reviewStore": {"count": 1}, "status": "ACTIVE", "taskDone": 54, "taskNoteByServiceV3": [{"note": "test02", "serviceId": "pcZRQ6PqmjrAPe5gt"}, {"note": "test123", "serviceId": "xTgw3s7tdpJa4JNJj"}], "type": "ASKER", "updatePrivacyPolicyAt": "2025-01-06T10:27:19Z", "voiceCallToken": {"status": "ACTIVE", "token": "eyJhbGciOiJIUzI1NiIsImN0eSI6InN0cmluZ2VlLWFwaTt2PTEiLCJ0eXAiOiJKV1QifQ.eyJleHAiOjQ4ODA2NzY0NzAsImlzcyI6IlNLeXNacHY3dHVWdEhFRWtFMGE2bU4xN25KVHpnSVk0UjUiLCJqdGkiOiJTS3lzWnB2N3R1VnRIRUVrRTBhNm1OMTduSlR6Z0lZNFI1LTE3MjUwMDI4NzAiLCJ1c2VySWQiOiJ4NGVjMTkxNmJkYjA5NzIxMjIxZTJjODYzYTdlNjFiZjIifQ.QjVTBRmtMrCNIrSIRNYYi-F2viANJHt14ovbnj1J2ak"}, "voiceCallTokenV2": {"status": "ACTIVE", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoieDRlYzE5MTZiZGIwOTcyMTIyMWUyYzg2M2E3ZTYxYlF1YW5UZXN0In0.nUL9C9H4x8zLAlH--pw2A7obn_Xp4iqcBl-xMG2Lhbg"}}