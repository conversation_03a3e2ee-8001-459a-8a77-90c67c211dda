import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

import { IUser } from '../../types';
import { AppStorage } from '../../utils';
import USER from './data.json';

// <PERSON><PERSON><PERSON> nghĩa kiểu dữ liệu cho state
interface UserState {
  user?: IUser;
  setUser: (user: IUser) => void;
}

export const useUserStore = create<UserState>()(
  persist(
    (set) => ({
      user: USER,
      setUser: (user: IUser) => set({ user }),
    }),
    {
      name: 'user-storage',
      storage: createJSONStorage(() => AppStorage.zustandStorage),
      version: 1,
    },
  ),
);
