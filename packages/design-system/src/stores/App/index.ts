import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

import { ITimezone } from '../../helpers';
import { ICurrency, IUser, Maybe } from '../../types';
import { AppStorage } from '../../utils';
import { ISO_CODE } from '../../utils/constant';
import { LOCALES } from './../../utils/constant';

export type IFeatureConfig = {
  ENABLE_NEW_FEATURE_OPEN_MALAYSIA?: boolean;
  ENABLE_NEW_FEATURE_PUZZLE_GAME?: boolean;
  ENABLE_NEW_FEATURE_WATER_GUN?: boolean;
};

// Định nghĩa kiểu dữ liệu cho state
interface AppState {
  isFirstOpen?: boolean;
  user: IUser;
  isoCode?: Maybe<ISO_CODE>;
  currency: ICurrency;
  locale: LOCALES;
  featureConfig?: IFeatureConfig;
  setFeatureConfig: (featureConfig?: IFeatureConfig) => void;
  onChangeIsoCode: (isoCode: ISO_CODE) => void;
  onChangeIsFirstOpen: () => void;
  setCurrency: (currency: ICurrency) => void;
  setUser: (user: IUser) => void;
  setLocale: (locale: LOCALES) => void;
}

export const useAppStore = create<AppState>()(
  persist(
    (set) => ({
      user: {},
      isFirstOpen: true,
      isoCode: null,
      timezone: 'Asia/Ho_Chi_Minh',
      currency: {
        sign: '',
        code: '',
      },
      locale: LOCALES.en,
      setLocale: (locale: LOCALES) => set({ locale }),
      onChangeIsoCode: (isoCode) => set({ isoCode }),
      setFeatureConfig: (featureConfig) => set({ featureConfig }),
      onChangeIsFirstOpen: () => set({ isFirstOpen: false }),
      setCurrency: (currency) => set({ currency }),
      setUser: (user: IUser) => set({ user }),
    }),
    {
      name: 'app-storage',
      storage: createJSONStorage(() => AppStorage.zustandStorage),
      version: 1,
    },
  ),
);
