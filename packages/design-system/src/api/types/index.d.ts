import { EndpointKeys } from '../endpoints';
import { ICheckTaskerConflictTimeAPI } from './CheckTaskerConflictTime';
import { ICheckTaskSameTimeAPI } from './CheckTaskSameTime';
import { IGetPriceAirConditionerAPI } from './get-price/GetPriceAirConditioner';
import { IGetPriceChildCareAPI } from './get-price/GetPriceChildCare';
import { IGetPriceCleaningSubscriptionAPI } from './get-price/GetPriceCleaningSubscription';
import { IGetPriceOfficeCleaningAPI } from './get-price/GetPriceOfficeCleaning';
import { IGetPricePatientCareAPI } from './get-price/GetPricePatientCare';
import { IGetEnvAPI } from './GetEnv';
import { IGetPriceAirConditionerAPI } from './GetPriceAirConditioner';
import { IGetPriceChildCareAPI } from './GetPriceChildCare';
import { IGetSettingsAPI } from './GetSettings';
import { IGetUpComingTasks } from './GetUpComingTasks';
import { IPostTaskAirConditionerAPI } from './post-task/PostTaskAirConditioner';
import { IPostTaskChildCareAPI } from './post-task/PostTaskChildCare';
import { IPostTaskOfficeCleaningAPI } from './post-task/PostTaskOfficeCleaning';
import { IPostTaskPatientCareAPI } from './post-task/PostTaskPatientCare';

export interface ApiType {
  [EndpointKeys.getEnv]: IGetEnvAPI;
  [EndpointKeys.getAllSettingsWithoutLogin]: IGetSettingsAPI;
  [EndpointKeys.getAllSettings]: IGetSettingsAPI;
  [EndpointKeys.getUser]: IGetEnvAPI;
  [EndpointKeys.getUpComingTasks]: IGetUpComingTasks;
  [EndpointKeys.checkTaskSameTime]: ICheckTaskSameTimeAPI;
  [EndpointKeys.checkTaskerConflictTime]: ICheckTaskerConflictTimeAPI;

  /* ------------------------- SERVICE AIR CONDITIONER ------------------------ */
  [EndpointKeys.postTaskAirConditioner]: IPostTaskAirConditionerAPI;
  [EndpointKeys.getPriceAirConditioner]: IGetPriceAirConditionerAPI;
  /* ----------------------- END SERVICE AIR CONDITIONER ---------------------- */

  /* --------------------------- SERVICE CHILD CARE --------------------------- */
  [EndpointKeys.postTaskChildCare]: IPostTaskChildCareAPI;
  [EndpointKeys.getPriceChildCare]: IGetPriceChildCareAPI;
  /* ------------------------- END SERVICE CHILD CARE ------------------------- */

  /* -------------------------- SERVICE PATIENT CARE -------------------------- */
  [EndpointKeys.postTaskPatientCare]: IPostTaskPatientCareAPI;
  [EndpointKeys.getPricePatientCare]: IGetPricePatientCareAPI;
  /* ------------------------ END SERVICE PATIENT CARE ------------------------ */

  /* ------------------------- SERVICE OFFICE CLEANING ------------------------ */
  [EndpointKeys.postTaskOfficeCleaning]: IPostTaskOfficeCleaningAPI;
  [EndpointKeys.getPriceOfficeCleaning]: IGetPriceOfficeCleaningAPI;
  /* ----------------------- END SERVICE OFFICE CLEANING ---------------------- */

  [EndpointKeys.pricingHomeCleaning]: IGetEnvAPI;

  /* ----------------------- SERVICE CLEANING SUBSCRIPTION ---------------------- */
  [EndpointKeys.getPriceCleaningSubscription]: IGetPriceCleaningSubscriptionAPI;
}
