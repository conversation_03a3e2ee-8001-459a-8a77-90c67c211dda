import { IAddons, <PERSON>rice, <PERSON>ask<PERSON>lace, Maybe } from '../../../types';

export class IParamsGetPrice {
  task: {
    timezone?: string;
    date: string;
    autoChooseTasker: boolean;
    taskPlace: ITaskPlace;
    homeType?: string;
    duration: number;
    payment?: {
      method: string;
    };
    requirements?: { type: number }[];
    addons?: IAddons[];
    isPremium?: boolean;
    detailAirConditioner?: Maybe;
  };
  service: {
    _id: string;
  };
  isoCode: string;
}

export class IGetPriceAirConditionerAPI {
  params?: IParamsGetPrice;
  response?: IPrice;
  error?: any;
}
