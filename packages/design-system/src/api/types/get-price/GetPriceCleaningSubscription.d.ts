import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>piError, IPriceSub, ITaskPlace } from '../../../types';

export type IParamsGetPrice = {
  schedule: string[];
  timezone: string;
  month: number;
  task: {
    taskPlace: ITaskPlace;
    homeType?: string;
    duration: number;
    payment?: {
      method: string;
    };
    addons?: IAddons[];
    isPremium?: boolean;
    isEco?: boolean;
    promotion?: {
      code: string;
    };
  };
  service: {
    _id: string;
  };
  isoCode: string;
};

export class IGetPriceCleaningSubscriptionAPI {
  params?: IParamsGetPrice;
  response?: IPriceSub;
  error?: IApiError;
}
