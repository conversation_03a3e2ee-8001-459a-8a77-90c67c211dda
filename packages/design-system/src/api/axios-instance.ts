import NetInfo from '@react-native-community/netinfo';
import axios, {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  CancelTokenSource,
} from 'axios';

import { Alert } from '../helpers';
import { Maybe } from '../types';
import { API_ERROR_CODE, ISO_CODE } from '../utils';

interface ApiConfig {
  baseURL: string;
  maxRetries?: number;
  timeout?: number;
  accessKey?: Maybe<string>;
  accessKeyEnv?: Maybe<string>;
  token?: Maybe<string>;
  isoCode?: Maybe<ISO_CODE>;
}

// Token storage keys
const TIMEOUT_WAIT_RESPOND = 30000;

// Default API configuration
const DEFAULT_CONFIG: ApiConfig = {
  baseURL: 'https://0.0.0.1:8080',
  maxRetries: 1,
};

export class ApiClient {
  private static instance: AxiosInstance;
  private static config: ApiConfig = DEFAULT_CONFIG;

  // Request management properties
  private static activeCancelTokens: Set<CancelTokenSource> = new Set();
  private static unauthorizedHandler: (() => void) | null = null;

  /**
   * Create and initialize the API client
   * @param config - API configuration
   * @param accessKey - Access key for API authentication
   */
  public static create(config: Partial<ApiConfig> = {}): void {
    // Merge with default config
    const mergedConfig: ApiConfig = {
      ...ApiClient.config,
      ...config,
    };

    ApiClient.config = mergedConfig;

    // Create axios instance
    ApiClient.instance = axios.create({
      baseURL: `${ApiClient.config.baseURL}/api`,
      timeout: ApiClient.config.timeout || TIMEOUT_WAIT_RESPOND,
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
    });

    // Setup interceptors
    ApiClient.setupInterceptors();
  }

  static getConfig() {
    return ApiClient.config;
  }

  static updateToken(token?: ApiConfig['token']): void {
    ApiClient.config.token = token;
  }

  static updateIsoCode(isoCode?: ApiConfig['isoCode']): void {
    ApiClient.config.isoCode = isoCode;
  }

  static updateAccessKey(accessKey?: ApiConfig['accessKey']): void {
    ApiClient.config.accessKey = accessKey;
  }

  /**
   * Update the authentication headers for API requests
   *
   * This method sets the appropriate authentication headers based on the provided parameters:
   * - If both token and isDualAuth are provided, it sets both Authorization and accessKey headers
   * - If only token is provided, it sets only the Authorization header
   * - If no token is provided, it sets only the accessKey header
   *
   * @param {Object} params - The parameters for updating headers
   * @param {string} [params.token] - The authentication token to use for the Authorization header
   * @param {boolean} [params.isDualAuth] - Whether to use dual authentication (both token and accessKey)
   *
   * @throws {Error} Throws an error if the API client is not initialized
   *
   * @example
   * // Set only token authentication
   * ApiClient.updateHeader({ token: 'my-token' });
   *
   * @example
   * // Set dual authentication (both token and accessKey)
   * ApiClient.updateHeader({ token: 'my-token', isDualAuth: true });
   *
   * @example
   * // Set only accessKey authentication
   * ApiClient.updateHeader({});
   */
  public static updateHeader({ isDualAuth }: { isDualAuth?: boolean }): void {
    if (!ApiClient.instance) {
      throw new Error(API_ERROR_CODE.CLIENT_NOT_INITIALIZED);
    }
    const token = ApiClient.config.token;
    const accessKey = ApiClient.config.accessKey;

    if (isDualAuth && token && accessKey) {
      ApiClient.instance = axios.create({
        ...ApiClient.instance.defaults,
        headers: {
          ...ApiClient.instance.defaults.headers,
          accessKey,
          Authorization: `Bearer ${token}`,
        },
      });
      return;
    }

    if (token) {
      ApiClient.instance = axios.create({
        ...ApiClient.instance.defaults,
        headers: {
          ...ApiClient.instance.defaults.headers,
          Authorization: `Bearer ${token}`,
        },
      });
      return;
    }

    ApiClient.instance = axios.create({
      ...ApiClient.instance.defaults,
      headers: {
        ...ApiClient.instance.defaults.headers,
        accessKey,
      },
    });
  }

  public static getInstance() {
    return ApiClient.instance;
  }

  /**
   * Đăng ký một cancel token cho request đang thực hiện
   */
  public static registerCancelToken(cancelToken: CancelTokenSource): void {
    ApiClient.activeCancelTokens.add(cancelToken);
  }

  /**
   * Hủy đăng ký cancel token khi request hoàn thành
   */
  public static unregisterCancelToken(cancelToken: CancelTokenSource): void {
    ApiClient.activeCancelTokens.delete(cancelToken);
  }

  /**
   * Cancel tất cả các requests đang thực hiện
   */
  public static cancelAllRequests(reason: string = 'Request cancelled'): void {
    ApiClient.activeCancelTokens.forEach((cancelToken) => {
      if (!cancelToken.token.reason) {
        cancelToken.cancel(reason);
      }
    });
    ApiClient.activeCancelTokens.clear();
  }

  /**
   * Xử lý lỗi 401 - Cancel tất cả requests và show alert
   */
  public static handleUnauthorizedError(): void {
    // Cancel tất cả requests đang chạy
    ApiClient.cancelAllRequests('Unauthorized access - session expired');
    Alert.alert.open({
      title: (t) => t('DIALOG_TITLE_INFORMATION'),
      message: (t) => t('AUTHENTICATED_EXPIRED'),
      onClose: () => {
        ApiClient.unauthorizedHandler?.();
      },
      actions: [{ testID: 'unauthorizeCloseBtn', text: (t) => t('CLOSE') }],
    });
  }

  /**
   * Đăng ký handler tùy chỉnh cho lỗi 401
   */
  public static setUnauthorizedHandler(handler: () => void): void {
    ApiClient.unauthorizedHandler = handler;
  }

  /**
   * Lấy số lượng requests đang thực hiện
   */
  public static getActiveRequestsCount(): number {
    return ApiClient.activeCancelTokens.size;
  }

  /**
   * Clear tất cả cancel tokens (dùng khi logout hoặc reset app)
   */
  public static clearAll(): void {
    ApiClient.activeCancelTokens.clear();
    ApiClient.unauthorizedHandler = null;
  }

  private static setupInterceptors(): void {
    // Request interceptor to add auth token
    ApiClient.instance.interceptors.request.use(
      async (conf) => {
        // Check network connectivity
        const netInfo = await NetInfo.fetch();
        if (!netInfo.isConnected) {
          throw new Error('No internet connection');
        }

        // Add authorization header if token exists
        if (ApiClient.config.token && conf.headers) {
          conf.headers.Authorization = `Bearer ${ApiClient.config.token}`;
        }
        return conf;
      },
      (error) => Promise.reject(error),
    );
  }

  public static post<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig,
  ): Promise<AxiosResponse<T>> {
    if (!ApiClient.instance) {
      return Promise.reject({
        code: API_ERROR_CODE.CLIENT_NOT_INITIALIZED,
      });
    }

    return ApiClient.instance.post<T>(url, data, config);
  }
}
