import { EndpointConfig, EndpointKeys } from './type';

export const EndpointsTH: Record<EndpointKeys, EndpointConfig> = {
  [EndpointKeys.getEnv]: {
    path: 'v5/env-th/get-asker-env',
    isDualAuth: true,
  },
  [EndpointKeys.getAllSettingsWithoutLogin]: {
    path: 'v5/api-asker-th/get-all-settings-without-login',
  },
  [EndpointKeys.getAllSettings]: {
    path: 'v5/api-asker-th/get-all-settings',
  },
  [EndpointKeys.getUser]: {
    path: 'v5/api-asker-th/get-user',
  },
  [EndpointKeys.getUpComingTasks]: {
    path: 'v5/api-asker-th/get-up-coming-tasks',
  },
  [EndpointKeys.checkTaskSameTime]: {
    path: 'v5/api-asker-th/check-task-sametime',
  },
  [EndpointKeys.postTaskAirConditioner]: {
    path: 'v5/booking-th/air-conditioner',
  },
  [EndpointKeys.postTaskChildCare]: {
    path: 'v5/booking-th/child-care',
  },
  [EndpointKeys.postTaskElderlyCare]: {
    path: 'v5/booking-th/elderly-care',
  },
  [EndpointKeys.postTaskPatientCare]: {
    path: 'v5/booking-th/patient-care',
  },
  [EndpointKeys.postTaskOfficeCleaning]: {
    path: 'v5/booking-th/office-cleaning',
  },
  [EndpointKeys.checkTaskerConflictTime]: {
    path: 'v5/api-asker-th/check-tasker-conflict-time',
  },

  /* -------------------------------- DUAL AUTH ------------------------------- */
  /* -------------- Public APIs accessible with or without login -------------- */
  [EndpointKeys.pricingHomeCleaning]: {
    path: 'v5/pricing-th/home-cleaning',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceAirConditioner]: {
    path: 'v5/pricing-th/air-conditioner-v2',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceChildCare]: {
    path: 'v5/pricing-th/child-care',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceElderlyCare]: {
    path: 'v5/pricing-th/elderly-care',
    isDualAuth: true,
  },
  [EndpointKeys.getPricePatientCare]: {
    path: 'v5/pricing-th/patient-care',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceOfficeCleaning]: {
    path: 'v5/pricing-th/office-cleaning',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceCleaningSubscription]: {
    path: 'v5/pricing-th/subscription',
    isDualAuth: true,
  },
};
