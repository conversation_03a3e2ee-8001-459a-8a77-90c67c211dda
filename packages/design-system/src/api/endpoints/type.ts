export interface EndpointConfig {
  path: string;
  isDualAuth?: boolean;
}

export enum EndpointKeys {
  getEnv = 'getEnv',
  getAllSettingsWithoutLogin = 'getAllSettingsWithoutLogin',
  getAllSettings = 'getAllSettings',
  getUser = 'getUser',
  getUpComingTasks = 'getUpComingTasks',
  checkTaskSameTime = 'checkTaskSameTime',
  postTaskAirConditioner = 'postTaskAirConditioner',
  postTaskChildCare = 'postTaskChildCare',
  postTaskElderlyCare = 'postTaskElderlyCare',
  postTaskPatientCare = 'postTaskPatientCare',
  postTaskOfficeCleaning = 'postTaskOfficeCleaning',
  checkTaskerConflictTime = 'checkTaskerConflictTime',

  /* -------------------------------- DUAL AUTH ------------------------------- */
  /* -------------- Public APIs accessible with or without login -------------- */
  pricingHomeCleaning = 'pricingHomeCleaning',
  getPriceAirConditioner = 'getPriceAirConditioner',
  getPriceChildCare = 'getPriceChildCare',
  getPriceElderlyCare = 'getPriceElderlyCare',
  getPricePatientCare = 'getPricePatientCare',
  getPriceOfficeCleaning = 'getPriceOfficeCleaning',
  getPriceCleaningSubscription = 'getPriceCleaningSubscription',
}
