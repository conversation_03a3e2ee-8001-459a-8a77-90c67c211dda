import { EndpointConfig, EndpointKeys } from './type';

export const EndpointsMY: Record<EndpointKeys, EndpointConfig> = {
  [EndpointKeys.getEnv]: {
    path: 'v5/env-my/get-asker-env',
  },
  [EndpointKeys.getAllSettingsWithoutLogin]: {
    path: 'v5/api-asker-my/get-all-settings-without-login',
  },
  [EndpointKeys.getAllSettings]: {
    path: 'v5/api-asker-my/get-all-settings',
  },
  [EndpointKeys.getUser]: {
    path: 'v5/api-asker-my/get-user',
  },
  [EndpointKeys.getUpComingTasks]: {
    path: 'v5/api-asker-my/get-up-coming-tasks',
  },
  [EndpointKeys.checkTaskSameTime]: {
    path: 'v5/api-asker-my/check-task-sametime',
  },
  [EndpointKeys.postTaskAirConditioner]: {
    path: 'v5/booking-my/air-conditioner',
  },
  [EndpointKeys.postTaskChildCare]: {
    path: 'v5/booking-my/child-care',
  },
  [EndpointKeys.postTaskElderlyCare]: {
    path: 'v5/booking-my/elderly-care',
  },
  [EndpointKeys.postTaskPatientCare]: {
    path: 'v5/booking-my/patient-care',
  },
  [EndpointKeys.postTaskOfficeCleaning]: {
    path: 'v5/booking-my/office-cleaning',
  },
  [EndpointKeys.checkTaskerConflictTime]: {
    path: 'v5/api-asker-my/check-tasker-conflict-time',
  },

  /* -------------------------------- DUAL AUTH ------------------------------- */
  /* -------------- Public APIs accessible with or without login -------------- */
  [EndpointKeys.pricingHomeCleaning]: {
    path: 'v5/pricing-my/home-cleaning',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceAirConditioner]: {
    path: 'v5/pricing-my/air-conditioner-v2',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceChildCare]: {
    path: 'v5/pricing-my/child-care',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceElderlyCare]: {
    path: 'v5/pricing-my/elderly-care',
    isDualAuth: true,
  },
  [EndpointKeys.getPricePatientCare]: {
    path: 'v5/pricing-my/patient-care',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceOfficeCleaning]: {
    path: 'v5/pricing-my/office-cleaning',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceCleaningSubscription]: {
    path: 'v5/pricing-my/subscription',
    isDualAuth: true,
  },
};
