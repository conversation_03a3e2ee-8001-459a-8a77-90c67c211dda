import { EndpointConfig, EndpointKeys } from './type';

export const EndpointsVN: Record<EndpointKeys, EndpointConfig> = {
  [EndpointKeys.getEnv]: {
    path: 'v5/env-vn/get-asker-env',
  },
  [EndpointKeys.getAllSettingsWithoutLogin]: {
    path: 'v5/api-asker-vn/get-all-settings-without-login',
  },
  [EndpointKeys.getAllSettings]: {
    path: 'v5/api-asker-vn/get-all-settings',
  },
  [EndpointKeys.getUser]: {
    path: 'v5/api-asker-vn/get-user',
  },
  [EndpointKeys.getUpComingTasks]: {
    path: 'v5/api-asker-vn/get-up-coming-tasks',
  },
  [EndpointKeys.checkTaskSameTime]: {
    path: 'v5/api-asker-vn/check-task-sametime',
  },
  [EndpointKeys.postTaskAirConditioner]: {
    path: 'v5/booking/air-conditioner',
  },
  [EndpointKeys.postTaskChildCare]: {
    path: 'v5/booking/child-care',
  },
  [EndpointKeys.postTaskElderlyCare]: {
    path: 'v5/booking/elderly-care',
  },
  [EndpointKeys.postTaskPatientCare]: {
    path: 'v5/booking/patient-care',
  },
  [EndpointKeys.postTaskOfficeCleaning]: {
    path: 'v5/booking/office-cleaning',
  },
  [EndpointKeys.checkTaskerConflictTime]: {
    path: 'v5/api-asker-vn/check-tasker-conflict-time',
  },

  /* -------------------------------- DUAL AUTH ------------------------------- */
  /* -------------- Public APIs accessible with or without login -------------- */
  [EndpointKeys.pricingHomeCleaning]: {
    path: 'v5/pricing-vn/home-cleaning',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceAirConditioner]: {
    path: 'v5/pricing-vn/air-conditioner-v2',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceChildCare]: {
    path: 'v5/pricing-vn/child-care',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceElderlyCare]: {
    path: 'v5/pricing-vn/elderly-care',
    isDualAuth: true,
  },
  [EndpointKeys.getPricePatientCare]: {
    path: 'v5/pricing-vn/patient-care',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceOfficeCleaning]: {
    path: 'v5/pricing-vn/office-cleaning',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceCleaningSubscription]: {
    path: 'v5/pricing-vn/subscription',
    isDualAuth: true,
  },
};
