import axios, { AxiosRequestConfig, CancelTokenSource } from 'axios';
import get from 'lodash-es/get';

import { IRespond } from '../types';
import { API_ERROR_CODE, API_RESULT_STATUS, Log } from '../utils';
import { ApiClient } from './axios-instance';
import { EndpointKeys, getEndpointConfig } from './endpoints';
import { ApiType } from './types';

// API error messages
export const getErrorMessage = (code: API_ERROR_CODE): string => {
  const errorMessages: Record<API_ERROR_CODE, string> = {
    [API_ERROR_CODE.MISSING_ISO_CODE]:
      'Missing ISO code. Please provide a valid ISO code.',
    [API_ERROR_CODE.CLIENT_NOT_INITIALIZED]:
      'API client instance has not been created. Call ApiClient.create() first.',
    [API_ERROR_CODE.INVALID_URL]: 'Invalid URL. Please check the API endpoint.',
    [API_ERROR_CODE.REQUEST_CANCELLED]: 'Request cancelled by user or system.',
    [API_ERROR_CODE.REQUEST_TIMEOUT]: 'Request timeout. Please try again.',
    [API_ERROR_CODE.NETWORK_ERROR]:
      'Network error. Please check your connection.',
    [API_ERROR_CODE.OFFLINE]:
      'You are offline. Please check your internet connection.',

    [API_ERROR_CODE.BAD_REQUEST]: 'Bad request. Please check your input.',
    [API_ERROR_CODE.UNAUTHORIZED]: 'Unauthorized. Please log in again.',
    [API_ERROR_CODE.FORBIDDEN]:
      'Forbidden. You do not have permission to access this resource.',
    [API_ERROR_CODE.NOT_FOUND]: '404 page not found',
    [API_ERROR_CODE.METHOD_NOT_ALLOWED]:
      'Method not allowed for this endpoint.',
    [API_ERROR_CODE.CONFLICT]: 'Conflict with current state of the resource.',
    [API_ERROR_CODE.GONE]: 'Resource is no longer available.',
    [API_ERROR_CODE.UNPROCESSABLE_ENTITY]:
      'Validation error. Please check your input.',
    [API_ERROR_CODE.TOO_MANY_REQUESTS]:
      'Too many requests. Please try again later.',

    [API_ERROR_CODE.INTERNAL_SERVER_ERROR]:
      'Internal server error. Please try again later.',
    [API_ERROR_CODE.BAD_GATEWAY]: 'Bad gateway. Please try again later.',
    [API_ERROR_CODE.SERVICE_UNAVAILABLE]:
      'Service unavailable. Please try again later.',
    [API_ERROR_CODE.GATEWAY_TIMEOUT]:
      'Gateway timeout. Please try again later.',
  };

  return errorMessages[code] || 'An unknown error occurred.';
};

/**
 * Format API response according to the old format
 * @param data - Response data
 * @returns Formatted response
 */
export function formatApiResponse<T>(data: T): IRespond<T> {
  return {
    status: API_RESULT_STATUS.SUCCESS,
    isSuccess: true,
    data: data || null,
  };
}

/**
 * Format API error according to the old format
 * @param error - Error object
 * @returns Formatted error response
 */
export function formatApiError(error: any): IRespond<any> {
  // Other server errors
  let errorCode =
    error?.response?.data?.error?.code ||
    error?.code ||
    error?.response?.status;

  if (axios.isCancel(error)) {
    errorCode = API_ERROR_CODE.REQUEST_CANCELLED;
  }

  // Xử lý lỗi 401 - Unauthorized
  if (errorCode === API_ERROR_CODE.UNAUTHORIZED) {
    // Gọi ApiClient để xử lý lỗi 401
    ApiClient.handleUnauthorizedError();
  }

  const message =
    error?.response?.data?.error?.message || getErrorMessage(errorCode);

  const errorData = {
    code: errorCode,
    message,
    data: error?.response?.data?.error || error?.data?.error,
    errorText: error?.response?.data?.error?.errorText,
  };

  return {
    status: API_RESULT_STATUS.ERROR,
    isSuccess: false,
    error: errorData,
    details: get(error, 'response.data.details', {}),
  };
}

export const getAccessKey = (accessKey: string, secretKey: string) => {
  return `${accessKey.slice(0, 16)}${secretKey}${accessKey.slice(-16)}`;
};

/**
 * Generic API request function for making POST requests
 * @param endpoint - API endpoint key
 * @param params - Request parameters
 * @param isoCode - ISO country code
 * @param cancelToken - Optional cancel token for request cancellation
 */
export async function apiRequest<T extends EndpointKeys>({
  key,
  params,
  cancelToken,
}: {
  key: T;
  params: ApiType[T]['params'];
  cancelToken?: CancelTokenSource;
}): Promise<ApiType[T]['response']> {
  const clientConfig = ApiClient.getConfig();
  const isoCode = clientConfig.isoCode;
  if (!isoCode) return;

  const endpointPath = getEndpointConfig(key, isoCode).path;
  const isDualAuth = getEndpointConfig(key, isoCode).isDualAuth;

  // Tạo cancel token nếu không được provide
  const activeCancelToken = cancelToken || createCancelToken();

  // Đăng ký cancel token với ApiClient
  ApiClient.registerCancelToken(activeCancelToken);

  try {
    const config: AxiosRequestConfig = {
      cancelToken: activeCancelToken.token,
    };

    if (key === EndpointKeys.getEnv) {
      config.headers = {
        ...config.headers,
        accessKey: clientConfig.accessKeyEnv,
      };
    } else {
      ApiClient.updateHeader({ isDualAuth });
    }

    const response = await ApiClient.post<ApiType[T]['response']>(
      `/${endpointPath}`,
      params,
      config,
    );

    const apiInstance = ApiClient.getInstance();
    Log.consoleLog('✅ Request success:', {
      header: apiInstance.defaults.headers,
      baseURL: apiInstance.defaults.baseURL,
      endpoint: endpointPath,
      body: params,
      response: response.data,
    });

    // Format successful response
    const formattedResponse = formatApiResponse(response.data);
    return formattedResponse.data as ApiType[T]['response'];
  } catch (error: any) {
    const apiInstance = ApiClient.getInstance();
    Log.consoleLog('🚫 Request error:', {
      header: apiInstance.defaults.headers,
      baseURL: apiInstance.defaults.baseURL,
      endpoint: endpointPath,
      body: params,
      error: error?.response || error,
    });

    // Format error response
    const formattedError = formatApiError(error);
    throw formattedError.error;
  } finally {
    // Hủy đăng ký cancel token sau khi request hoàn thành
    ApiClient.unregisterCancelToken(activeCancelToken);
  }
}

/**
 * Create a cancel token source for request cancellation
 */
export const createCancelToken = (): CancelTokenSource => {
  return axios.CancelToken.source();
};
