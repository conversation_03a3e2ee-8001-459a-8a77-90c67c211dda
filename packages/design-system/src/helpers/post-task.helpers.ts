import uniq from 'lodash/uniq';
import { get, isEmpty } from 'lodash-es';

import { IAddress } from '../types';
import {
  ESTIMATED_TIME_POST_TASK_MINUTES,
  MIN_POST_TASK_TIME,
  SERVICES,
} from '../utils/constant';
import { roundOfNumberMinutes } from './';
import {
  DateTimeHelpers,
  IDate,
  ITimezone,
  TypeFormatDate,
} from './date-time.helpers';

export class PostTaskHelpers {
  static getDefaultDateTime = (
    settingTask: {
      serviceName: SERVICES;
      isTet?: boolean;
      defaultTaskTime?: number;
      taskDate?: IDate;
    },
    settingSystemTaskTime?: number,
    city?: string,
  ) => {
    const timezone = DateTimeHelpers.getTimezoneByCity(city);
    const { serviceName, isTet, defaultTaskTime, taskDate } = settingTask;

    const defaultTimePostTask = defaultTaskTime || settingSystemTaskTime || 14; // hours 2PM

    const toDay = DateTimeHelpers.toDayTz({ timezone });

    // default tomorrow, 14 PM
    let tempDate = toDay
      .add(isTet ? 3 : 1, 'day')
      .hour(defaultTimePostTask)
      .startOf('hour');

    // Công việc giặt ủi và vệ sinh thảm sẽ được cộng thêm 1 ngày
    const serviceAddOneDay = [
      SERVICES.LAUNDRY,
      SERVICES.OFFICE_CARPET_CLEANING,
    ];
    if (serviceAddOneDay.indexOf(serviceName) >= 0) {
      tempDate = tempDate.add(1, 'day');
    }

    // add to MONDAY, show base price
    // tomorrow is SAT
    if (tempDate.day() === 6) {
      tempDate = tempDate.add(2, 'day');
    } else if (tempDate.day() === 0) {
      // tomorrow is SUN
      tempDate = tempDate.add(1, 'day');
    }

    // Tránh trường hợp user chọn công việc tết sau đó chuyển sang công việc thường dẫn đến ngày giờ bị lỗi
    if (!isTet && taskDate) {
      const minTime = toDay.add(70, 'minute');
      const maxTime = toDay.add(7, 'day').endOf('date');
      if (
        DateTimeHelpers.toDateTz({ date: taskDate, timezone }).isBefore(
          DateTimeHelpers.toDateTz({ date: minTime, timezone }),
        ) ||
        DateTimeHelpers.toDateTz({ date: taskDate, timezone }).isAfter(
          DateTimeHelpers.toDateTz({ date: maxTime, timezone }),
        )
      ) {
        return DateTimeHelpers.formatToString({ date: tempDate, timezone });
      }
    }

    // Tránh trường hợp book công việc tết nhưng default day của công việc thường (ngày mai)
    if (
      isTet &&
      toDay.isAfter(DateTimeHelpers.toDateTz({ date: tempDate, timezone }))
    ) {
      tempDate = toDay.hour(defaultTimePostTask).startOf('hour');
    }

    return DateTimeHelpers.formatToString({ date: tempDate, timezone });
  };

  static checkTimeValidFromService = (
    timezone: ITimezone,
    date: IDate,
    duration: number,
    postingLimits: any,
    serviceName?: SERVICES,
  ) => {
    // check service can book all time
    if (!postingLimits) {
      return true;
    }

    //check data
    if (!date) {
      return false;
    }
    const from = get(postingLimits, 'from', null);
    const toDate = get(postingLimits, 'to', null);
    // not set limit time
    if (!from || !toDate) {
      return true;
    }
    const beforeTime = DateTimeHelpers.toDateTz({
      timezone,
      date: from,
      format: TypeFormatDate.TimeHourMinuteSecond,
      keepLocalTime: true,
    }); //ex: from = 06:00:00

    const afterTime = DateTimeHelpers.toDateTz({
      timezone,
      date: toDate,
      format: TypeFormatDate.TimeHourMinuteSecond,
      keepLocalTime: true,
    }).subtract(duration, 'hour'); // ex: to = 22:59:59

    // If home cooking service, not subtract duration
    // if (serviceName && isHomeCookingService(serviceName)) {
    //   afterTime = DateTimeHelpers.toDateTz({
    //     timezone,
    //     date: toDate,
    //     format: TypeFormatDate.TimeHourMinuteSecond,
    //     keepLocalTime: true,
    //   });
    // }

    const currentTime = DateTimeHelpers.toDateTz({
      timezone,
      date: DateTimeHelpers.formatToString({
        timezone,
        date,
        typeFormat: TypeFormatDate.TimeHourMinuteSecond,
        keepLocalTime: true,
      }),
      format: TypeFormatDate.TimeHourMinuteSecond,
    });

    const isSameOrBefore = DateTimeHelpers.checkIsSameOrBefore({
      timezone,
      firstDate: currentTime,
      secondDate: afterTime,
    });
    const isSameOrAfter = DateTimeHelpers.checkIsSameOrAfter({
      timezone,
      firstDate: currentTime,
      secondDate: beforeTime,
    });
    if (isSameOrBefore && isSameOrAfter) {
      return true;
    }
    return false;
  };

  static formatPostingLimits = ({
    postingLimits,
    timezone,
  }: {
    timezone: ITimezone;
    postingLimits?: { from?: string; to?: string };
  }) => {
    const postingLimitsFrom = postingLimits?.from;
    const postingLimitsTo = postingLimits?.to;
    const fromDateTxt = DateTimeHelpers.formatToString({
      timezone,
      date: postingLimitsFrom,
      dateFormat: TypeFormatDate.TimeHourMinuteSecond,
      typeFormat: TypeFormatDate.TimeHourMinute,
      keepLocalTime: true,
    });

    const toDateTxt = DateTimeHelpers.formatToString({
      timezone,
      date: postingLimitsTo,
      dateFormat: TypeFormatDate.TimeHourMinuteSecond,
      typeFormat: TypeFormatDate.TimeHourMinute,
      keepLocalTime: true,
    });
    return {
      from: fromDateTxt,
      to: toDateTxt,
    };
  };

  static validateDateTime = (
    timezone: ITimezone,
    date: IDate,
    minPostTaskTime?: number,
  ) => {
    // Date is greater than or equa to current date.
    // Time is greater than current time 30 minutes.
    const minTime = DateTimeHelpers.toDayTz({ timezone }).add(
      minPostTaskTime || 60,
      'minute',
    );
    const isSameOrAfter = DateTimeHelpers.checkIsSameOrAfter({
      timezone,
      firstDate: date,
      secondDate: minTime,
    });
    if (date && isSameOrAfter) {
      return true;
    }
    return false;
  };

  static formatHourDuration = ({
    timezone,
    taskDate,
    duration,
  }: {
    timezone: ITimezone;
    taskDate: IDate;
    duration: number;
  }) => {
    const fromHour = DateTimeHelpers.toDateTz({ timezone, date: taskDate }).add(
      duration,
      'hour',
    );
    const from = DateTimeHelpers.formatToString({
      timezone,
      date: taskDate,
      typeFormat: TypeFormatDate.TimeHourMinute,
    });
    const toDate = DateTimeHelpers.formatToString({
      timezone,
      date: fromHour,
      typeFormat: TypeFormatDate.TimeHourMinute,
    });
    return {
      from,
      to: toDate,
    };
  };

  /**
   * @description get cách ngày trong tuần của 1 khoản thời gian
   * @param timezone
   * @param weekdays [1,2,3]
   * @param startDate Ngày bắt đầu
   * @param endDate Ngày kết thúc
   * @return chuyển [ 1,2,3 ] -> [dateTime, dateTime, dateTime]
   */
  static getDateFromWeekday = ({
    timezone,
    weekdays,
    startDate,
    endDate,
  }: {
    timezone: ITimezone;
    weekdays: number[];
    startDate: IDate;
    endDate: IDate;
  }) => {
    if (weekdays && weekdays.length > 0) {
      const result = [];
      let start = DateTimeHelpers.toDateTz({ timezone, date: startDate });
      const endOfDate = DateTimeHelpers.toDateTz({ timezone, date: endDate });
      for (
        start;
        start.toDate().getTime() <= endOfDate.toDate().getTime();
        start = start.add(1, 'day')
      ) {
        if (weekdays.indexOf(start.get('day')) >= 0) {
          result.push(
            DateTimeHelpers.formatToString({ timezone, date: start }),
          );
        }
      }
      return result;
    }
    return [];
  };

  /**
   * @description chuyển [ dateTime ] -> [2]
   * @param timezone
   * @param arrayDate
   */
  static getDayNumberFromDate = (timezone: ITimezone, arrayDate: IDate[]) => {
    if (isEmpty(arrayDate)) {
      return [];
    }
    const weekday = arrayDate.map((date) => {
      return DateTimeHelpers.toDateTz({ timezone, date }).get('day');
    });
    return uniq(weekday);
  };

  static postingLimitsToDate = ({
    timezone,
    postingLimits,
    date,
  }: {
    timezone: ITimezone;
    date?: IDate;
    postingLimits?: { from?: string; to?: string };
  }) => {
    const dateTime = DateTimeHelpers.toDateTz({ timezone, date });
    const dateFrom = DateTimeHelpers.toDateTz({
      timezone,
      date: postingLimits?.from,
      format: TypeFormatDate.TimeHourMinuteSecond,
      keepLocalTime: true,
    });
    const dateTo = DateTimeHelpers.toDateTz({
      timezone,
      date: postingLimits?.to,
      format: TypeFormatDate.TimeHourMinuteSecond,
      keepLocalTime: true,
    });

    return {
      from: DateTimeHelpers.formatToString({
        timezone,
        date: dateTime
          .set('hour', dateFrom.hour())
          .set('minute', dateFrom.minute())
          .startOf('minute'),
        keepLocalTime: true,
      }),
      to: DateTimeHelpers.formatToString({
        timezone,
        date: dateTime
          .set('hour', dateTo.hour())
          .set('minute', dateTo.minute())
          .startOf('minute'),
        keepLocalTime: true,
      }),
    };
  };

  /**
   * @description Lấy thời gian tối thiểu có thể chọn
   * @param minTime
   * @param settingSystem
   * @param timezone
   * @param isDisabledMinimumTime
   * @return minimumDatePicker
   */
  static getMinimumDatePicker = ({
    minTime,
    settingSystem,
    timezone,
    isDisabledMinimumTime,
  }: {
    minTime?: IDate;
    timezone: ITimezone;
    settingSystem: any;
    isDisabledMinimumTime?: boolean;
  }) => {
    const minPostTaskTime =
      get(settingSystem, 'minPostTaskTime', MIN_POST_TASK_TIME) +
      ESTIMATED_TIME_POST_TASK_MINUTES;

    const roundMinutes = roundOfNumberMinutes(
      DateTimeHelpers.getMinute({ timezone, date: minTime }),
    );

    const minimumDateTime = DateTimeHelpers.toDateTz({
      timezone,
      date: minTime,
    })
      .minute(roundMinutes)
      .add(isDisabledMinimumTime ? 0 : minPostTaskTime, 'minute')
      .startOf('minute'); // minimum date to post task
    const minimumDate = DateTimeHelpers.toDateTz({
      timezone,
      date: minimumDateTime,
    }).toDate();

    return minimumDate;
  };

  static getAddressSub = (data: any) => {
    const address: IAddress = {
      lat: data?.location?.lat,
      lng: data?.location?.lng,
      country: data?.taskPlace?.country,
      city: data?.taskPlace?.city,
      district: data?.taskPlace?.district,
      address: data?.address,
      contact: data?.contactName,
      phoneNumber: data?.phone,
      countryCode: data?.countryCode,
    };

    if (data.shortAddress) {
      address.shortAddress = data.shortAddress;
    }

    return address;
  };
}
