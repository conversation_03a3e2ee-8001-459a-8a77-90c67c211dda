const prettierConfig = require('./.prettierrc.js');

module.exports = {
  root: true,
  extends: ['@react-native', 'prettier'],
  plugins: ['prettier', 'extra-rules', 'simple-import-sort', 'import'],
  parser: '@typescript-eslint/parser',
  env: {
    jest: true,
  },
  rules: {
    'prettier/prettier': [
      'warn',
      {
        ...prettierConfig,
      },
    ],
    'arrow-body-style': 'off',
    'prefer-arrow-callback': 'off',
    'no-shadow': [0],
    '@typescript-eslint/no-shadow': [
      1,
      {
        ignoreTypeValueShadow: true,
        ignoreFunctionTypeParameterNameValueShadow: true,
      },
    ],
    '@typescript-eslint/no-unused-vars': [
      1,
      { vars: 'all', args: 'after-used', ignoreRestSiblings: true },
    ],
    'no-undef': 2,
    'no-undefined': 2,
    'no-unused-vars': 1,
    semi: 2,
    'react-hooks/exhaustive-deps': 1,
    'react-native/no-unused-styles': 2,
    'react-native/no-color-literals': 1,
    'react-native/no-raw-text': 1,
    'no-dupe-class-members': 2,
    'no-dupe-args': 2,
    'no-dupe-keys': 2,
    'no-duplicate-case': 2,
    'no-duplicate-imports': 1,
    'no-this-before-super': 2,
    'no-var': 1,
    'no-confusing-arrow': 1,
    'no-useless-escape': 0,
    'no-console': ['error', { allow: ['warn', 'error', 'info'] }],
    'no-nested-ternary': 2,
    'prefer-const': 'error',
    'simple-import-sort/imports': [
      'error',
      {
        groups: [
          ['^\\u0000'],
          ['^react', '^@?\\w'],
          [
            '^(@hooks|@navigation|@screens|@images|@components|@i18n|@app|@utils|@stores|@lottie)(/.*|$)',
          ],
        ],
      },
    ],
    'simple-import-sort/exports': 'error',
    'sort-imports': 'off',
    'import/first': 'error',
    'import/newline-after-import': 'error',
    'import/no-duplicates': 'error',
    'no-restricted-imports': [
      'error',
      {
        paths: [
          {
            name: '@react-navigation/native',
            importNames: ['useNavigation'],
            message:
              'Hãy dùng useAppNavigation import từ @hooks/useAppNavigation',
          },
          {
            name: 'react-native',
            importNames: [
              'Text',
              'TextProps',
              'View',
              'ViewProps',
              'TouchableOpacity',
              'TouchableOpacityProps',
              'Image',
              'ImageProps',
              'ScrollView',
              'FlatList',
              'ScrollViewProps',
              'FlatListProps',
              'TextInput',
              'TextInputProps',
              'Switch',
            ],
            message: 'Hãy dùng trong @btaskee/design-system',
          },
          {
            name: 'react-native',
            importNames: ['Colors'],
            message: 'Hãy dùng ColorsV2 trong @btaskee/design-system',
          },
          {
            name: 'lodash',
            message: 'Hãy dùng lodash-es',
          },
          {
            name: 'react-i18next',
            message: 'Không được dùng trực tiếp',
          },
        ],
        patterns: [
          {
            group: ['@components/*'],
            message: 'Hãy import component trong components/index.ts',
          },
          {
            group: ['@hooks/*'],
            message: 'Hãy import trong hooks/index.ts',
          },
          {
            group: ['screens/*'],
            message: 'Hãy import screen trong screens/index.ts',
          },
          {
            group: ['**/src/*', 'src/*'],
            message: 'Hãy define path alias trong tsconfig.json',
          },
        ],
      },
    ],
  },
};
